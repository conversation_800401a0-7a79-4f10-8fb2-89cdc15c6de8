# Ultimate Agentic RAG System - Enhanced Evaluation Configuration
# Complete configuration for comprehensive evaluation across all sophistication levels

metadata:
  version: "3.0"
  created: "2025-05-31"
  description: "Production-ready evaluation configuration for Ultimate Agentic RAG system"
  evaluation_framework_version: "1.0"

# Test Environment Configuration
environment:
  tenant_slug: "eval-tenant"
  test_timeout_seconds: 60
  max_retries: 3
  parallel_execution: true
  max_concurrent_tests: 10
  resource_monitoring: true
  detailed_logging: true

# Sophistication Level Testing Configuration with Ultimate Intelligence
sophistication_levels:
  basic:
    description: "Fast, simple queries with basic vector search"
    features:
      - simple_vector_search
    expected_latency_ms: [100, 500]
    quality_threshold: 0.6
    test_complexity: ["simple"]
    target_metrics:
      accuracy: 0.7
      completeness: 0.6
      citation_quality: 0.7
      response_coherence: 0.7
    
  standard:
    description: "Enhanced search with HyDE and hybrid ranking"
    features:
      - simple_vector_search
      - hyde
      - hybrid_search
    expected_latency_ms: [500, 1500] 
    quality_threshold: 0.7
    test_complexity: ["simple", "moderate"]
    target_metrics:
      accuracy: 0.8
      completeness: 0.7
      citation_quality: 0.8
      response_coherence: 0.8
    
  advanced:
    description: "Cross-domain intelligence with file retrieval"
    features:
      - simple_vector_search
      - hyde
      - hybrid_search
      - multi_step
      - file_retrieval
      - cross_domain
    expected_latency_ms: [1000, 3000]
    quality_threshold: 0.8
    test_complexity: ["simple", "moderate", "complex"]
    target_metrics:
      accuracy: 0.85
      completeness: 0.8
      citation_quality: 0.85
      response_coherence: 0.85
      domain_routing_precision: 0.8
    
  expert:
    description: "Ultimate intelligence with LLM orchestration and adaptive features"
    features:
      - all_features
      - llm_orchestration
      - adaptive_selection
      - quality_monitoring
      - performance_prediction
      - ultimate_search
      - complexity_analysis
      - strategy_optimization
    expected_latency_ms: [2000, 5000]
    quality_threshold: 0.85
    test_complexity: ["simple", "moderate", "complex", "adaptive"]
    target_metrics:
      accuracy: 0.9
      completeness: 0.85
      citation_quality: 0.9
      response_coherence: 0.9
      domain_routing_precision: 0.9
      strategy_accuracy: 0.85
      business_value: 0.8
      intelligence_effectiveness: 0.85

# Enhanced Data Domain Configuration with Cross-Domain Intelligence
data_domains:
  slack_conversations:
    description: "General team discussions and communications"
    expected_complexity: "simple_to_moderate"
    typical_queries: ["team updates", "project discussions", "announcements"]
    
  slack_engineering:
    description: "Engineering team technical discussions"
    expected_complexity: "moderate_to_complex"
    typical_queries: ["technical problems", "architecture discussions", "code reviews"]
    
  slack_support:
    description: "Customer support discussions and issues"
    expected_complexity: "moderate"
    typical_queries: ["customer issues", "bug reports", "support escalations"]
    
  github_code:
    description: "Source code repositories and implementations"
    expected_complexity: "complex"
    typical_queries: ["code implementations", "technical details", "specific functions"]
    
  github_issues:
    description: "Bug reports, feature requests, and issue tracking"
    expected_complexity: "moderate_to_complex"
    typical_queries: ["bug investigations", "feature planning", "issue resolution"]
    
  github_wiki:
    description: "Project documentation and guides"
    expected_complexity: "moderate"
    typical_queries: ["how-to guides", "project documentation", "setup instructions"]
    
  github_discussions:
    description: "Community discussions and Q&A"
    expected_complexity: "moderate"
    typical_queries: ["community questions", "feature discussions", "best practices"]
    
  documentation:
    description: "Formal documentation and guides"
    expected_complexity: "moderate_to_complex"
    typical_queries: ["API documentation", "user guides", "technical specifications"]
    
  meeting_notes:
    description: "Meeting minutes and decision records"
    expected_complexity: "moderate"
    typical_queries: ["meeting decisions", "action items", "strategic discussions"]
    
  customer_docs:
    description: "Customer-facing documentation and materials"
    expected_complexity: "simple_to_moderate"
    typical_queries: ["user documentation", "tutorials", "help articles"]

# Advanced Retrieval Strategy Configuration
retrieval_strategies:
  chunks:
    description: "Standard chunk-based retrieval for explanations"
    expected_use_cases: ["explanations", "questions", "general_queries"]
    optimization_params:
      chunk_size: 512
      chunk_overlap: 50
      top_k: 10
    
  files_via_content:
    description: "Full file retrieval by content similarity"
    expected_use_cases: ["complete_documents", "full_code_files", "entire_guides"]
    optimization_params:
      similarity_threshold: 0.7
      max_file_size: 10000
      top_k: 5
    
  files_via_metadata:
    description: "File retrieval by filename/path matching"
    expected_use_cases: ["specific_files", "config_files", "named_documents"]
    optimization_params:
      fuzzy_matching: true
      path_weight: 0.6
      name_weight: 0.4

# Cross-Domain Fusion Strategies with Ultimate Intelligence
fusion_strategies:
  weighted_merge:
    description: "Weight by domain importance and confidence"
    use_cases: ["general_cross_domain"]
    parameters:
      domain_weights:
        github_code: 1.0
        documentation: 0.9
        slack_engineering: 0.8
        github_issues: 0.7
        slack_support: 0.6
    
  reciprocal_rank_fusion:
    description: "RRF algorithm for balanced ranking"
    use_cases: ["balanced_results"]
    parameters:
      k_value: 60
      normalize_scores: true
    
  semantic_fusion:
    description: "Semantic similarity-based fusion"
    use_cases: ["content_similarity"]
    parameters:
      similarity_threshold: 0.75
      semantic_weight: 0.8
    
  diversity_optimized:
    description: "Maximize cross-domain diversity"
    use_cases: ["comprehensive_coverage"]
    parameters:
      diversity_weight: 0.7
      max_results_per_domain: 3
    
  problem_resolution_merge:
    description: "Optimize for troubleshooting workflows"
    use_cases: ["bug_reports", "issue_resolution"]
    parameters:
      issue_weight: 1.0
      solution_weight: 0.9
      discussion_weight: 0.7
    
  process_flow_merge:
    description: "Optimize for process understanding"
    use_cases: ["workflow_explanation", "deployment_processes"]
    parameters:
      sequential_weight: 0.9
      documentation_weight: 0.8
      discussion_weight: 0.6
    
  comprehensive:
    description: "Balanced comprehensive coverage with ultimate intelligence"
    use_cases: ["analysis_queries", "audit_requests", "strategic_planning"]
    parameters:
      coverage_weight: 0.8
      quality_weight: 0.9
      diversity_weight: 0.7
      business_relevance_weight: 0.8

# Enhanced Quality Metrics Configuration
quality_metrics:
  accuracy:
    weight: 0.25
    threshold: 0.8
    description: "Factual correctness of information"
    measurement_criteria:
      - factual_correctness
      - technical_accuracy
      - up_to_date_information
    
  completeness:
    weight: 0.2
    threshold: 0.75
    description: "Coverage of relevant information"
    measurement_criteria:
      - covers_all_relevant_aspects
      - addresses_query_comprehensively
      - includes_necessary_context
    
  relevance:
    weight: 0.25
    threshold: 0.8
    description: "Relevance to the query"
    measurement_criteria:
      - directly_answers_question
      - appropriate_level_of_detail
      - contextually_relevant
    
  citation_quality:
    weight: 0.15
    threshold: 0.85
    description: "Quality and accuracy of citations"
    measurement_criteria:
      - accurate_source_attribution
      - complete_citation_information
      - verifiable_references
    
  response_coherence:
    weight: 0.15
    threshold: 0.8
    description: "Logical flow and coherence"
    measurement_criteria:
      - logical_structure
      - clear_progression
      - consistent_terminology

# Ultimate Intelligence Metrics
intelligence_metrics:
  strategy_accuracy:
    weight: 0.3
    threshold: 0.85
    description: "Accuracy of LLM-driven strategy selection"
    
  complexity_analysis_precision:
    weight: 0.25
    threshold: 0.8
    description: "Accuracy of multi-dimensional complexity analysis"
    
  domain_routing_effectiveness:
    weight: 0.25
    threshold: 0.85
    description: "Effectiveness of cross-domain routing decisions"
    
  adaptive_improvement_success:
    weight: 0.2
    threshold: 0.75
    description: "Success rate of adaptive quality improvements"

# Business Value Metrics for Enterprise Readiness
business_metrics:
  business_value_relevance:
    weight: 0.3
    threshold: 0.8
    description: "Relevance to business objectives and outcomes"
    
  actionability:
    weight: 0.25
    threshold: 0.8
    description: "Presence of specific, actionable recommendations"
    
  decision_support_quality:
    weight: 0.25
    threshold: 0.85
    description: "Quality of executive and strategic decision support"
    
  roi_analysis_depth:
    weight: 0.2
    threshold: 0.75
    description: "Depth and accuracy of ROI and business impact analysis"

# Enhanced Performance Benchmarks
performance_benchmarks:
  latency_targets:
    basic_queries_ms: [100, 500]
    standard_queries_ms: [500, 1500]
    advanced_queries_ms: [1000, 3000]
    expert_queries_ms: [2000, 5000]
    ultimate_intelligence_ms: [3000, 8000]
    
  throughput_targets:
    concurrent_users: [1, 5, 10, 20, 50]
    queries_per_second: [10, 50, 100, 200, 500]
    
  resource_utilization:
    max_memory_mb: 4096
    max_cpu_percent: 85
    max_disk_io_mb: 200
    max_network_mb: 100
    
  quality_consistency:
    min_quality_variance: 0.1
    max_quality_degradation: 0.05
    quality_stability_threshold: 0.9

# Comprehensive Test Categories with Weights
test_categories:
  basic_retrieval:
    weight: 0.15
    description: "Core retrieval functionality validation"
    priority: "high"
    
  sophistication_levels:
    weight: 0.2
    description: "All sophistication levels comprehensive testing"
    priority: "critical"
    
  agentic_intelligence:
    weight: 0.25
    description: "LLM-driven decision making and strategy orchestration"
    priority: "critical"
    
  ultimate_intelligence:
    weight: 0.2
    description: "Highest level AI capabilities and adaptive features"
    priority: "critical"
    
  cross_domain:
    weight: 0.15
    description: "Multi-domain intelligence and fusion testing"
    priority: "high"
    
  business_scenarios:
    weight: 0.15
    description: "Enterprise business use case validation"
    priority: "high"
    
  quality_performance:
    weight: 0.1
    description: "Result quality and system performance validation"
    priority: "medium"
    
  edge_cases:
    weight: 0.08
    description: "Challenging scenarios and failure mode testing"
    priority: "medium"
    
  conversation_flow:
    weight: 0.12
    description: "Multi-turn conversation and context management"
    priority: "medium"

# Advanced Failure Modes and Edge Cases
failure_modes:
  empty_query:
    expected_behavior: "graceful_error_handling_with_helpful_message"
    test_priority: "high"
    
  malicious_query:
    expected_behavior: "refuse_and_explain_with_ethical_guidance"
    test_priority: "critical"
    
  nonsensical_query:
    expected_behavior: "best_effort_response_with_clarification_request"
    test_priority: "medium"
    
  repetitive_query:
    expected_behavior: "deduplicate_and_process_intelligently"
    test_priority: "medium"
    
  overly_broad_query:
    expected_behavior: "seek_clarification_or_provide_structured_overview"
    test_priority: "medium"
    
  no_relevant_documents:
    expected_behavior: "explain_no_results_with_helpful_suggestions"
    test_priority: "high"
    
  service_degradation:
    expected_behavior: "graceful_fallback_with_user_notification"
    test_priority: "critical"
    
  resource_exhaustion:
    expected_behavior: "controlled_degradation_with_error_recovery"
    test_priority: "high"

# CI/CD Integration Configuration
cicd_integration:
  quality_gates:
    minimum_pass_rate: 0.8
    minimum_quality_score: 0.75
    maximum_avg_latency_ms: 3000
    maximum_p95_latency_ms: 5000
    minimum_throughput_rps: 10
    maximum_error_rate: 0.02
    
  test_selection:
    smoke_tests: ["basic_retrieval", "sophistication_levels"]
    regression_tests: ["performance", "quality_metrics", "intelligence_features"]
    full_validation: ["all_categories"]
    
  notification_config:
    slack_integration: true
    email_integration: true
    notify_on_failure: true
    notify_on_success: false
    notify_on_regression: true
    
  artifact_retention:
    detailed_results_days: 90
    summary_reports_days: 365
    performance_metrics_days: 180
    baseline_metrics_permanent: true

# Advanced Monitoring and Alerting
monitoring:
  real_time_metrics:
    - query_latency
    - quality_scores
    - error_rates
    - resource_utilization
    - strategy_effectiveness
    
  alert_thresholds:
    critical_latency_ms: 10000
    critical_error_rate: 0.05
    critical_quality_drop: 0.2
    critical_memory_usage: 0.9
    
  trend_analysis:
    performance_degradation_threshold: 0.2
    quality_degradation_threshold: 0.1
    rolling_window_hours: 24
    
  dashboard_metrics:
    - sophistication_level_performance
    - intelligence_feature_effectiveness
    - business_scenario_success_rates
    - cross_domain_fusion_quality

# Evaluation Scoring and Thresholds
scoring:
  overall_pass_threshold: 0.8
  excellent_threshold: 0.9
  enterprise_ready_threshold: 0.85
  
  weight_distribution:
    functionality: 0.3
    intelligence: 0.25
    quality: 0.25
    performance: 0.15
    business_value: 0.05
    
  sophistication_level_requirements:
    basic:
      minimum_pass_rate: 0.9
      target_excellence_rate: 0.95
    standard:
      minimum_pass_rate: 0.85
      target_excellence_rate: 0.9
    advanced:
      minimum_pass_rate: 0.8
      target_excellence_rate: 0.85
    expert:
      minimum_pass_rate: 0.75
      target_excellence_rate: 0.8

# Ultimate Intelligence Feature Validation
ultimate_intelligence_validation:
  llm_orchestration:
    strategy_selection_accuracy: 0.9
    reasoning_quality: 0.85
    decision_transparency: 0.8
    
  adaptive_learning:
    improvement_detection: 0.8
    strategy_optimization: 0.75
    quality_enhancement: 0.8
    
  complexity_analysis:
    multi_dimensional_accuracy: 0.85
    semantic_complexity: 0.8
    domain_complexity: 0.8
    temporal_complexity: 0.75
    structural_complexity: 0.75
    contextual_complexity: 0.8
    
  cross_domain_intelligence:
    domain_routing_precision: 0.9
    fusion_strategy_effectiveness: 0.85
    result_quality_improvement: 0.8
    diversity_optimization: 0.75

# Enterprise Readiness Validation
enterprise_requirements:
  security_and_compliance:
    data_privacy_compliance: true
    audit_trail_completeness: true
    access_control_validation: true
    
  scalability_and_performance:
    concurrent_user_capacity: 100
    peak_load_handling: true
    performance_consistency: 0.9
    
  business_integration:
    stakeholder_value_demonstration: true
    roi_quantification: true
    decision_support_effectiveness: 0.85
    
  operational_excellence:
    monitoring_completeness: true
    error_handling_robustness: true
    recovery_procedures: true
    documentation_quality: 0.9

# Development and Testing Workflow
development_workflow:
  pre_commit_tests: ["smoke", "basic_functionality"]
  pre_merge_tests: ["comprehensive", "performance_quick"]
  pre_release_tests: ["full_validation", "business_scenarios"]
  post_release_monitoring: ["regression", "performance_trends"]
  
  continuous_improvement:
    baseline_update_frequency: "weekly"
    performance_trend_analysis: "daily"
    quality_improvement_tracking: "continuous"
    intelligence_feature_optimization: "monthly"
