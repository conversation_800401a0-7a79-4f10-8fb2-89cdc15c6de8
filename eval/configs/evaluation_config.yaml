# Comprehensive Evaluation Configuration for Agentic RAG System
# Testing all sophistication levels and intelligence features

metadata:
  version: "2.0"
  created: "2025-05-31"
  description: "Complete evaluation configuration for advanced agentic RAG features"
  
# Test Environment Configuration
environment:
  tenant_slug: "eval-tenant"
  test_timeout_seconds: 30
  max_retries: 3
  parallel_execution: true
  
# Sophistication Level Testing Configuration
sophistication_levels:
  basic:
    features:
      - simple_vector_search
    expected_latency_ms: [100, 500]
    quality_threshold: 0.6
    test_complexity: ["simple"]
    
  standard:
    features:
      - simple_vector_search
      - hyde
      - hybrid_search
    expected_latency_ms: [500, 1500] 
    quality_threshold: 0.7
    test_complexity: ["simple", "moderate"]
    
  advanced:
    features:
      - simple_vector_search
      - hyde
      - hybrid_search
      - multi_step
      - file_retrieval
      - cross_domain
    expected_latency_ms: [1000, 3000]
    quality_threshold: 0.8
    test_complexity: ["simple", "moderate", "complex"]
    
  expert:
    features:
      - all_features
      - llm_orchestration
      - adaptive_selection
      - quality_monitoring
      - performance_prediction
    expected_latency_ms: [2000, 5000]
    quality_threshold: 0.85
    test_complexity: ["simple", "moderate", "complex", "adaptive"]

# Data Domain Configuration
data_domains:
  - slack_conversations
  - slack_engineering 
  - slack_support
  - github_code
  - github_issues
  - github_wiki
  - github_discussions
  - documentation
  - meeting_notes
  - customer_docs

# Retrieval Strategy Configuration
retrieval_strategies:
  chunks:
    description: "Standard chunk-based retrieval"
    expected_use_cases: ["explanations", "questions", "general_queries"]
    
  files_via_content:
    description: "Full file retrieval by content similarity"
    expected_use_cases: ["complete_documents", "full_code_files", "entire_guides"]
    
  files_via_metadata:
    description: "File retrieval by filename/path matching"
    expected_use_cases: ["specific_files", "config_files", "named_documents"]

# Cross-Domain Fusion Strategies
fusion_strategies:
  weighted_merge:
    description: "Weight by domain importance and confidence"
    use_cases: ["general_cross_domain"]
    
  reciprocal_rank_fusion:
    description: "RRF algorithm for balanced ranking"
    use_cases: ["balanced_results"]
    
  semantic_fusion:
    description: "Semantic similarity-based fusion"
    use_cases: ["content_similarity"]
    
  diversity_optimized:
    description: "Maximize cross-domain diversity"
    use_cases: ["comprehensive_coverage"]
    
  problem_resolution_merge:
    description: "Optimize for troubleshooting workflows"
    use_cases: ["bug_reports", "issue_resolution"]
    
  process_flow_merge:
    description: "Optimize for process understanding"
    use_cases: ["workflow_explanation", "deployment_processes"]
    
  comprehensive:
    description: "Balanced comprehensive coverage"
    use_cases: ["analysis_queries", "audit_requests"]

# Quality Metrics Configuration
quality_metrics:
  accuracy:
    weight: 0.25
    threshold: 0.8
    description: "Factual correctness of information"
    
  completeness:
    weight: 0.2
    threshold: 0.75
    description: "Coverage of relevant information"
    
  relevance:
    weight: 0.25
    threshold: 0.8
    description: "Relevance to the query"
    
  citation_quality:
    weight: 0.15
    threshold: 0.85
    description: "Quality and accuracy of citations"
    
  response_coherence:
    weight: 0.15
    threshold: 0.8
    description: "Logical flow and coherence"

# Performance Benchmarks
performance_benchmarks:
  latency:
    basic_queries_ms: [100, 500]
    standard_queries_ms: [500, 1500]
    advanced_queries_ms: [1000, 3000]
    expert_queries_ms: [2000, 5000]
    
  throughput:
    concurrent_users: [1, 5, 10, 20]
    queries_per_second: [10, 50, 100, 200]
    
  resource_usage:
    max_memory_mb: 2048
    max_cpu_percent: 80
    max_disk_io_mb: 100

# Test Categories and Weights
test_categories:
  basic_retrieval:
    weight: 0.15
    description: "Core retrieval functionality"
    
  agentic_intelligence:
    weight: 0.25
    description: "LLM-driven decision making"
    
  cross_domain:
    weight: 0.2
    description: "Multi-domain intelligence"
    
  quality_performance:
    weight: 0.15
    description: "Result quality and performance"
    
  edge_cases:
    weight: 0.1
    description: "Challenging scenarios"
    
  conversation_flow:
    weight: 0.15
    description: "Multi-turn conversations"

# Failure Modes and Edge Cases
failure_modes:
  empty_query:
    expected_behavior: "graceful_error_handling"
    
  malicious_query:
    expected_behavior: "refuse_and_explain"
    
  nonsensical_query:
    expected_behavior: "best_effort_or_clarification"
    
  repetitive_query:
    expected_behavior: "deduplicate_and_process"
    
  overly_broad_query:
    expected_behavior: "seek_clarification_or_provide_overview"
    
  no_relevant_documents:
    expected_behavior: "explain_no_results_found"

# Evaluation Scoring
scoring:
  pass_threshold: 0.75
  excellent_threshold: 0.9
  weight_distribution:
    functionality: 0.4
    quality: 0.3
    performance: 0.2
    intelligence: 0.1
