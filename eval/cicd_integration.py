#!/usr/bin/env python3
"""
CI/CD Integration Script for Ultimate Agentic RAG System

This script provides automated evaluation integration for CI/CD pipelines including:
- Pre-deployment quality checks
- Performance regression testing  
- Automated test result reporting
- Quality gate enforcement
- Slack/email notifications
"""

import asyncio
import json
import logging
import os
import sys
import time
import argparse
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import subprocess
import statistics

# Add the project root to Python path
sys.path.append(str(Path(__file__).parent.parent.parent))

from comprehensive_evaluator import UltimateRAGEvaluator, EvaluationReport
from performance_stress_tester import PerformanceStressTester, PerformanceTestResult

class CICDEvaluationRunner:
    """CI/CD integration for automated RAG system evaluation."""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config = self._load_cicd_config(config_path)
        self.logger = self._setup_logging()
        
        # Initialize evaluators
        self.evaluator = UltimateRAGEvaluator()
        self.performance_tester = PerformanceStressTester()
        
        # CI/CD context
        self.ci_context = self._get_ci_context()
        
        # Results storage
        self.results_dir = Path(__file__).parent / "results" / "cicd"
        self.results_dir.mkdir(parents=True, exist_ok=True)
    
    def _load_cicd_config(self, config_path: Optional[str]) -> Dict[str, Any]:
        """Load CI/CD configuration."""
        if config_path and Path(config_path).exists():
            with open(config_path) as f:
                return json.load(f)
        
        # Default CI/CD configuration
        return {
            "quality_gates": {
                "minimum_pass_rate": 0.8,
                "minimum_quality_score": 0.7,
                "maximum_avg_latency_ms": 3000,
                "maximum_p95_latency_ms": 5000,
                "minimum_throughput_rps": 5
            },
            "test_configuration": {
                "run_full_evaluation": True,
                "run_performance_tests": True,
                "run_regression_tests": True,
                "test_categories": [
                    "basic_retrieval",
                    "sophistication_levels",
                    "agentic_intelligence",
                    "ultimate_intelligence"
                ],
                "performance_test_duration": 60,
                "max_concurrent_users": 5
            },
            "notifications": {
                "slack_webhook": os.getenv("SLACK_WEBHOOK_URL"),
                "email_recipients": os.getenv("EMAIL_RECIPIENTS", "").split(","),
                "notify_on_failure": True,
                "notify_on_success": False,
                "notify_on_regression": True
            },
            "artifact_storage": {
                "save_detailed_results": True,
                "save_performance_metrics": True,
                "save_regression_comparison": True,
                "retention_days": 30
            }
        }
    
    def _setup_logging(self) -> logging.Logger:
        """Set up CI/CD logging."""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[logging.StreamHandler()]
        )
        return logging.getLogger("CICDEvaluationRunner")
    
    def _get_ci_context(self) -> Dict[str, str]:
        """Extract CI/CD context from environment variables."""
        return {
            "pipeline_id": os.getenv("CI_PIPELINE_ID", "unknown"),
            "commit_sha": os.getenv("CI_COMMIT_SHA", "unknown"),
            "commit_message": os.getenv("CI_COMMIT_MESSAGE", ""),
            "branch": os.getenv("CI_COMMIT_BRANCH", "unknown"),
            "build_number": os.getenv("BUILD_NUMBER", "unknown"),
            "pr_number": os.getenv("CI_MERGE_REQUEST_IID", ""),
            "environment": os.getenv("CI_ENVIRONMENT_NAME", "test"),
            "runner": os.getenv("CI_RUNNER_DESCRIPTION", "unknown")
        }
    
    async def run_cicd_evaluation(self, test_type: str = "full") -> Tuple[bool, Dict[str, Any]]:
        """Run CI/CD evaluation pipeline."""
        self.logger.info(f"Starting CI/CD evaluation pipeline: {test_type}")
        self.logger.info(f"CI Context: {self.ci_context}")
        
        start_time = time.time()
        pipeline_results = {
            "test_type": test_type,
            "start_time": datetime.now(timezone.utc).isoformat(),
            "ci_context": self.ci_context,
            "quality_gates": self.config["quality_gates"],
            "results": {}
        }
        
        success = True
        
        try:
            # Run appropriate tests based on type
            if test_type in ["full", "comprehensive"]:
                success, pipeline_results = await self._run_comprehensive_pipeline(pipeline_results)
            elif test_type == "performance":
                success, pipeline_results = await self._run_performance_pipeline(pipeline_results)
            elif test_type == "regression":
                success, pipeline_results = await self._run_regression_pipeline(pipeline_results)
            elif test_type == "smoke":
                success, pipeline_results = await self._run_smoke_pipeline(pipeline_results)
            else:
                raise ValueError(f"Unknown test type: {test_type}")
            
            # Add final metrics
            end_time = time.time()
            pipeline_results["end_time"] = datetime.now(timezone.utc).isoformat()
            pipeline_results["total_duration_seconds"] = end_time - start_time
            pipeline_results["overall_success"] = success
            
            # Save results
            self._save_cicd_results(pipeline_results)
            
            # Send notifications
            await self._send_notifications(success, pipeline_results)
            
            # Generate quality report
            self._generate_quality_report(pipeline_results)
            
            return success, pipeline_results
            
        except Exception as e:
            self.logger.error(f"CI/CD evaluation failed: {e}")
            pipeline_results["error"] = str(e)
            pipeline_results["overall_success"] = False
            
            await self._send_failure_notification(str(e), pipeline_results)
            return False, pipeline_results
    
    async def _run_comprehensive_pipeline(self, pipeline_results: Dict[str, Any]) -> Tuple[bool, Dict[str, Any]]:
        """Run comprehensive evaluation pipeline."""
        self.logger.info("Running comprehensive evaluation pipeline")
        
        success = True
        
        # 1. Run full system evaluation
        if self.config["test_configuration"]["run_full_evaluation"]:
            self.logger.info("Running full system evaluation...")
            
            test_categories = self.config["test_configuration"]["test_categories"]
            evaluation_report = await self.evaluator.run_comprehensive_evaluation(test_categories)
            
            pipeline_results["results"]["evaluation"] = {
                "pass_rate": evaluation_report.pass_rate,
                "avg_quality_score": evaluation_report.avg_quality_score,
                "avg_response_time_ms": evaluation_report.avg_response_time_ms,
                "total_tests": evaluation_report.total_tests,
                "sophistication_results": evaluation_report.sophistication_results
            }
            
            # Check quality gates
            gates_passed = self._check_quality_gates(evaluation_report)
            if not gates_passed:
                success = False
                self.logger.error("Evaluation quality gates failed")
        
        # 2. Run performance tests
        if self.config["test_configuration"]["run_performance_tests"]:
            self.logger.info("Running performance tests...")
            
            # Quick performance test for CI/CD
            performance_results = await self._run_quick_performance_test()
            
            pipeline_results["results"]["performance"] = {
                "avg_latency_ms": performance_results["avg_latency"],
                "p95_latency_ms": performance_results["p95_latency"],
                "throughput_rps": performance_results["throughput"],
                "success_rate": performance_results["success_rate"]
            }
            
            # Check performance gates
            perf_gates_passed = self._check_performance_gates(performance_results)
            if not perf_gates_passed:
                success = False
                self.logger.error("Performance quality gates failed")
        
        # 3. Run regression tests
        if self.config["test_configuration"]["run_regression_tests"]:
            self.logger.info("Running regression tests...")
            
            regression_results = await self._run_regression_tests()
            pipeline_results["results"]["regression"] = regression_results
            
            if regression_results["has_regressions"]:
                success = False
                self.logger.error("Regression tests detected performance degradation")
        
        return success, pipeline_results
    
    async def _run_performance_pipeline(self, pipeline_results: Dict[str, Any]) -> Tuple[bool, Dict[str, Any]]:
        """Run performance-focused pipeline."""
        self.logger.info("Running performance evaluation pipeline")
        
        # Run comprehensive performance tests
        performance_results = await self.performance_tester.run_comprehensive_performance_tests()
        
        # Analyze results
        success_rate = statistics.mean([
            r.successful_requests / r.total_requests for r in performance_results
        ])
        avg_latency = statistics.mean([r.avg_latency for r in performance_results])
        max_throughput = max([r.requests_per_second for r in performance_results])
        
        pipeline_results["results"]["performance_comprehensive"] = {
            "test_count": len(performance_results),
            "avg_success_rate": success_rate,
            "avg_latency_ms": avg_latency,
            "max_throughput_rps": max_throughput,
            "detailed_results": [
                {
                    "test_name": r.test_name,
                    "concurrent_users": r.concurrent_users,
                    "throughput_rps": r.requests_per_second,
                    "avg_latency_ms": r.avg_latency,
                    "p95_latency_ms": r.p95_latency,
                    "quality_score": r.avg_quality_score
                }
                for r in performance_results
            ]
        }
        
        # Check performance gates
        gates = self.config["quality_gates"]
        success = (
            success_rate >= 0.95 and
            avg_latency <= gates["maximum_avg_latency_ms"] and
            max_throughput >= gates["minimum_throughput_rps"]
        )
        
        return success, pipeline_results
    
    async def _run_regression_pipeline(self, pipeline_results: Dict[str, Any]) -> Tuple[bool, Dict[str, Any]]:
        """Run regression testing pipeline."""
        self.logger.info("Running regression testing pipeline")
        
        regression_results = await self._run_regression_tests()
        pipeline_results["results"]["regression"] = regression_results
        
        success = not regression_results["has_regressions"]
        return success, pipeline_results
    
    async def _run_smoke_pipeline(self, pipeline_results: Dict[str, Any]) -> Tuple[bool, Dict[str, Any]]:
        """Run smoke testing pipeline (quick validation)."""
        self.logger.info("Running smoke testing pipeline")
        
        # Run minimal set of critical tests
        smoke_queries = [
            {"query": "authentication", "sophistication": "basic"},
            {"query": "How does our system work?", "sophistication": "standard"},
            {"query": "Analyze our security posture", "sophistication": "advanced"}
        ]
        
        smoke_results = []
        for test_query in smoke_queries:
            try:
                start_time = time.time()
                
                if test_query["sophistication"] == "basic":
                    response = self.evaluator.search_service.search(test_query["query"])
                else:
                    from apps.core.retrieval import SophisticationLevel
                    soph_level = getattr(SophisticationLevel, test_query["sophistication"].upper())
                    response = self.evaluator.search_service.agentic_search(
                        test_query["query"], soph_level
                    )
                
                end_time = time.time()
                latency = (end_time - start_time) * 1000
                
                # Basic quality check
                answer_length = len(response.get('answer', ''))
                has_citations = len(response.get('citations', [])) > 0
                has_error = 'error' in response.get('metadata', {})
                
                passed = not has_error and answer_length > 50 and latency < 5000
                
                smoke_results.append({
                    "query": test_query["query"],
                    "sophistication": test_query["sophistication"],
                    "latency_ms": latency,
                    "answer_length": answer_length,
                    "has_citations": has_citations,
                    "has_error": has_error,
                    "passed": passed
                })
                
            except Exception as e:
                self.logger.error(f"Smoke test failed for query '{test_query['query']}': {e}")
                smoke_results.append({
                    "query": test_query["query"],
                    "sophistication": test_query["sophistication"],
                    "passed": False,
                    "error": str(e)
                })
        
        # Evaluate smoke test results
        total_tests = len(smoke_results)
        passed_tests = sum(1 for r in smoke_results if r.get("passed", False))
        pass_rate = passed_tests / total_tests
        
        pipeline_results["results"]["smoke"] = {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "pass_rate": pass_rate,
            "test_results": smoke_results
        }
        
        success = pass_rate >= 0.8
        return success, pipeline_results
    
    async def _run_quick_performance_test(self) -> Dict[str, float]:
        """Run quick performance test for CI/CD pipeline."""
        duration = self.config["test_configuration"]["performance_test_duration"]
        max_users = self.config["test_configuration"]["max_concurrent_users"]
        
        # Run a single performance test configuration
        performance_result = await self.performance_tester._run_performance_test(
            test_name="cicd_quick_test",
            concurrent_users=min(max_users, 3),
            duration_seconds=duration,
            sophistication="standard",
            query_type="simple"
        )
        
        return {
            "avg_latency": performance_result.avg_latency,
            "p95_latency": performance_result.p95_latency,
            "throughput": performance_result.requests_per_second,
            "success_rate": performance_result.successful_requests / performance_result.total_requests
        }
    
    async def _run_regression_tests(self) -> Dict[str, Any]:
        """Run regression tests comparing against baseline."""
        # Load baseline metrics (if available)
        baseline_file = self.results_dir / "baseline_metrics.json"
        
        if not baseline_file.exists():
            self.logger.warning("No baseline metrics found, skipping regression tests")
            return {
                "has_regressions": False,
                "message": "No baseline available for comparison"
            }
        
        with open(baseline_file) as f:
            baseline = json.load(f)
        
        # Run current tests
        current_results = await self._run_quick_performance_test()
        
        # Compare against baseline
        regressions = []
        
        # Check latency regression (>20% increase)
        if current_results["avg_latency"] > baseline["avg_latency"] * 1.2:
            regressions.append({
                "metric": "avg_latency",
                "baseline": baseline["avg_latency"],
                "current": current_results["avg_latency"],
                "degradation_percent": (current_results["avg_latency"] / baseline["avg_latency"] - 1) * 100
            })
        
        # Check throughput regression (>20% decrease)
        if current_results["throughput"] < baseline["throughput"] * 0.8:
            regressions.append({
                "metric": "throughput",
                "baseline": baseline["throughput"],
                "current": current_results["throughput"],
                "degradation_percent": (1 - current_results["throughput"] / baseline["throughput"]) * 100
            })
        
        return {
            "has_regressions": len(regressions) > 0,
            "regressions": regressions,
            "baseline_metrics": baseline,
            "current_metrics": current_results
        }
    
    def _check_quality_gates(self, evaluation_report: EvaluationReport) -> bool:
        """Check if evaluation results pass quality gates."""
        gates = self.config["quality_gates"]
        
        checks = [
            evaluation_report.pass_rate >= gates["minimum_pass_rate"],
            evaluation_report.avg_quality_score >= gates["minimum_quality_score"],
            evaluation_report.avg_response_time_ms <= gates["maximum_avg_latency_ms"]
        ]
        
        return all(checks)
    
    def _check_performance_gates(self, performance_results: Dict[str, float]) -> bool:
        """Check if performance results pass quality gates."""
        gates = self.config["quality_gates"]
        
        checks = [
            performance_results["avg_latency"] <= gates["maximum_avg_latency_ms"],
            performance_results["p95_latency"] <= gates["maximum_p95_latency_ms"],
            performance_results["throughput"] >= gates["minimum_throughput_rps"],
            performance_results["success_rate"] >= 0.95
        ]
        
        return all(checks)
    
    def _save_cicd_results(self, pipeline_results: Dict[str, Any]):
        """Save CI/CD pipeline results."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        commit_sha = self.ci_context["commit_sha"][:8]
        
        results_file = self.results_dir / f"cicd_results_{timestamp}_{commit_sha}.json"
        
        with open(results_file, 'w') as f:
            json.dump(pipeline_results, f, indent=2, default=str)
        
        self.logger.info(f"CI/CD results saved: {results_file}")
        
        # Update baseline if tests pass
        if pipeline_results.get("overall_success") and "performance" in pipeline_results["results"]:
            baseline_file = self.results_dir / "baseline_metrics.json"
            with open(baseline_file, 'w') as f:
                json.dump(pipeline_results["results"]["performance"], f, indent=2)
    
    async def _send_notifications(self, success: bool, pipeline_results: Dict[str, Any]):
        """Send notifications based on results."""
        notifications_config = self.config["notifications"]
        
        should_notify = (
            (not success and notifications_config["notify_on_failure"]) or
            (success and notifications_config["notify_on_success"]) or
            (pipeline_results["results"].get("regression", {}).get("has_regressions") and 
             notifications_config["notify_on_regression"])
        )
        
        if not should_notify:
            return
        
        # Prepare notification content
        status = "✅ PASSED" if success else "❌ FAILED"
        commit_sha = self.ci_context["commit_sha"][:8]
        branch = self.ci_context["branch"]
        
        message = f"""
**Ultimate RAG CI/CD Evaluation {status}**

**Branch:** {branch}
**Commit:** {commit_sha}
**Pipeline:** {self.ci_context['pipeline_id']}

"""
        
        # Add results summary
        if "evaluation" in pipeline_results["results"]:
            eval_results = pipeline_results["results"]["evaluation"]
            message += f"""
**Evaluation Results:**
- Pass Rate: {eval_results['pass_rate']:.1%}
- Quality Score: {eval_results['avg_quality_score']:.3f}
- Avg Response Time: {eval_results['avg_response_time_ms']:.0f}ms
"""
        
        if "performance" in pipeline_results["results"]:
            perf_results = pipeline_results["results"]["performance"]
            message += f"""
**Performance Results:**
- Avg Latency: {perf_results['avg_latency_ms']:.0f}ms
- Throughput: {perf_results['throughput_rps']:.1f} req/s
- Success Rate: {perf_results['success_rate']:.1%}
"""
        
        # Send Slack notification
        if notifications_config.get("slack_webhook"):
            await self._send_slack_notification(message, success)
        
        # Send email notification (placeholder)
        if notifications_config.get("email_recipients"):
            self.logger.info(f"Email notification would be sent to: {notifications_config['email_recipients']}")
    
    async def _send_slack_notification(self, message: str, success: bool):
        """Send Slack notification."""
        webhook_url = self.config["notifications"]["slack_webhook"]
        if not webhook_url:
            return
        
        color = "good" if success else "danger"
        
        payload = {
            "attachments": [
                {
                    "color": color,
                    "text": message,
                    "footer": "Ultimate RAG CI/CD Pipeline",
                    "ts": int(time.time())
                }
            ]
        }
        
        try:
            import requests
            response = requests.post(webhook_url, json=payload, timeout=10)
            if response.status_code == 200:
                self.logger.info("Slack notification sent successfully")
            else:
                self.logger.error(f"Slack notification failed: {response.status_code}")
        except Exception as e:
            self.logger.error(f"Failed to send Slack notification: {e}")
    
    async def _send_failure_notification(self, error_message: str, pipeline_results: Dict[str, Any]):
        """Send failure notification."""
        commit_sha = self.ci_context["commit_sha"][:8]
        
        message = f"""
**❌ Ultimate RAG CI/CD Pipeline FAILED**

**Error:** {error_message}
**Commit:** {commit_sha}
**Branch:** {self.ci_context['branch']}
**Pipeline:** {self.ci_context['pipeline_id']}

Please check the pipeline logs for more details.
"""
        
        if self.config["notifications"].get("slack_webhook"):
            await self._send_slack_notification(message, False)
    
    def _generate_quality_report(self, pipeline_results: Dict[str, Any]):
        """Generate quality report for CI/CD."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        commit_sha = self.ci_context["commit_sha"][:8]
        
        report_file = self.results_dir / f"quality_report_{timestamp}_{commit_sha}.md"
        
        with open(report_file, 'w') as f:
            f.write("# Ultimate RAG CI/CD Quality Report\n\n")
            f.write(f"**Date:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"**Commit:** {self.ci_context['commit_sha']}\n")
            f.write(f"**Branch:** {self.ci_context['branch']}\n")
            f.write(f"**Pipeline:** {self.ci_context['pipeline_id']}\n\n")
            
            # Overall status
            status = "✅ PASSED" if pipeline_results["overall_success"] else "❌ FAILED"
            f.write(f"## Overall Status: {status}\n\n")
            
            # Quality gates
            f.write("## Quality Gates\n\n")
            gates = self.config["quality_gates"]
            for gate, threshold in gates.items():
                f.write(f"- **{gate}**: {threshold}\n")
            
            f.write("\n")
            
            # Results summary
            f.write("## Results Summary\n\n")
            for test_type, results in pipeline_results["results"].items():
                f.write(f"### {test_type.title()}\n\n")
                for metric, value in results.items():
                    if isinstance(value, (int, float)):
                        f.write(f"- **{metric}**: {value}\n")
                f.write("\n")
        
        self.logger.info(f"Quality report generated: {report_file}")

def main():
    """Main CI/CD runner."""
    parser = argparse.ArgumentParser(description="Ultimate RAG CI/CD Evaluation")
    parser.add_argument(
        "--test-type",
        choices=["full", "performance", "regression", "smoke"],
        default="full",
        help="Type of tests to run"
    )
    parser.add_argument(
        "--config",
        help="Path to CI/CD configuration file"
    )
    parser.add_argument(
        "--fail-fast",
        action="store_true",
        help="Exit immediately on first failure"
    )
    
    args = parser.parse_args()
    
    async def run_cicd():
        runner = CICDEvaluationRunner(args.config)
        success, results = await runner.run_cicd_evaluation(args.test_type)
        
        if success:
            print("🎉 CI/CD evaluation PASSED")
            return 0
        else:
            print("❌ CI/CD evaluation FAILED")
            return 1
    
    exit_code = asyncio.run(run_cicd())
    sys.exit(exit_code)

if __name__ == "__main__":
    main()
