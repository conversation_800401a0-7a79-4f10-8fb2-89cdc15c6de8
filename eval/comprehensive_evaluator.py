#!/usr/bin/env python3
"""
Comprehensive Automated Evaluation Framework for Ultimate Agentic RAG System

This framework provides automated testing, performance monitoring, quality assessment,
and detailed reporting for all sophistication levels and intelligence features.
"""

import asyncio
import json
import logging
import time
import traceback
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from concurrent.futures import ThreadPoolExecutor, as_completed
import statistics
import psutil
import requests
import sys
import os

# Add the project root to Python path
sys.path.append(str(Path(__file__).parent.parent.parent))

from apps.search.services.simplified_search_service import SearchService
from apps.core.llama_index_manager import LlamaIndexManager
from apps.core.retrieval import SophisticationLevel, RetrievalMode, DataDomain

@dataclass
class TestResult:
    """Individual test result with comprehensive metrics."""
    test_id: str
    query: str
    sophistication: str
    complexity: str
    
    # Response metrics
    response_time_ms: float
    quality_score: float
    strategy_used: str
    domains_searched: List[str]
    result_count: int
    
    # Intelligence metrics
    strategy_accuracy: float
    complexity_analysis_accuracy: float
    domain_routing_precision: float
    
    # Business metrics
    business_value_score: float
    actionability_score: float
    
    # System metrics
    memory_usage_mb: float
    cpu_usage_percent: float
    
    # Status
    passed: bool
    error_message: Optional[str] = None
    
    # Additional metadata
    timestamp: str = ""
    metadata: Dict[str, Any] = None

@dataclass
class EvaluationReport:
    """Comprehensive evaluation report."""
    
    # Summary metrics
    total_tests: int
    passed_tests: int
    failed_tests: int
    pass_rate: float
    
    # Performance metrics
    avg_response_time_ms: float
    percentile_95_response_time_ms: float
    avg_quality_score: float
    min_quality_score: float
    
    # Intelligence metrics
    strategy_accuracy: float
    complexity_analysis_accuracy: float
    domain_routing_precision: float
    
    # Sophistication level breakdown
    sophistication_results: Dict[str, Dict[str, float]]
    
    # Business value metrics
    business_value_score: float
    actionability_score: float
    
    # System performance
    avg_memory_usage_mb: float
    max_memory_usage_mb: float
    avg_cpu_usage_percent: float
    max_cpu_usage_percent: float
    
    # Detailed results
    test_results: List[TestResult]
    
    # Timestamps
    start_time: str
    end_time: str
    total_duration_seconds: float

class UltimateRAGEvaluator:
    """Comprehensive evaluator for Ultimate Agentic RAG system."""
    
    def __init__(self, tenant_slug: str = "eval-tenant", config_path: Optional[str] = None):
        self.tenant_slug = tenant_slug
        self.config = self._load_config(config_path)
        self.search_service = SearchService(tenant_slug)
        self.llama_manager = LlamaIndexManager(tenant_slug)
        
        # Set up logging
        self._setup_logging()
        
        # Load test datasets
        self.eval_path = Path(__file__).parent
        self.datasets = self._load_datasets()
        self.scenarios = self._load_scenarios()
        
        # Initialize metrics tracking
        self.test_results: List[TestResult] = []
        self.start_time = None
        self.end_time = None
        
    def _load_config(self, config_path: Optional[str]) -> Dict[str, Any]:
        """Load evaluation configuration."""
        if config_path:
            config_file = Path(config_path)
        else:
            config_file = self.eval_path / "configs" / "evaluation_config.yaml"
        
        try:
            import yaml
            with open(config_file) as f:
                return yaml.safe_load(f)
        except Exception as e:
            self.logger.warning(f"Could not load config from {config_file}: {e}")
            return self._default_config()
    
    def _default_config(self) -> Dict[str, Any]:
        """Default configuration if config file not found."""
        return {
            "environment": {
                "test_timeout_seconds": 30,
                "max_retries": 3,
                "parallel_execution": True
            },
            "sophistication_levels": {
                "basic": {"quality_threshold": 0.6, "max_latency_ms": 500},
                "standard": {"quality_threshold": 0.7, "max_latency_ms": 1500},
                "advanced": {"quality_threshold": 0.8, "max_latency_ms": 3000},
                "expert": {"quality_threshold": 0.85, "max_latency_ms": 5000}
            }
        }
    
    def _setup_logging(self):
        """Set up comprehensive logging."""
        log_dir = self.eval_path / "results" / "logs"
        log_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = log_dir / f"evaluation_{timestamp}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger("UltimateRAGEvaluator")
        self.logger.info(f"Evaluation logging initialized: {log_file}")
    
    def _load_datasets(self) -> Dict[str, Any]:
        """Load all evaluation datasets."""
        datasets = {}
        dataset_files = [
            "comprehensive_query_dataset.json",
            "enhanced_query_dataset.json", 
            "representative_documents.json",
            "enhanced_documents_dataset.json"
        ]
        
        for file_name in dataset_files:
            file_path = self.eval_path / "datasets" / file_name
            if file_path.exists():
                try:
                    with open(file_path) as f:
                        datasets[file_name.replace('.json', '')] = json.load(f)
                    self.logger.info(f"Loaded dataset: {file_name}")
                except Exception as e:
                    self.logger.error(f"Failed to load dataset {file_name}: {e}")
            else:
                self.logger.warning(f"Dataset file not found: {file_path}")
        
        return datasets
    
    def _load_scenarios(self) -> Dict[str, Any]:
        """Load all evaluation scenarios."""
        scenarios = {}
        scenario_files = [
            "agentic_intelligence_scenarios.json",
            "advanced_intelligence_scenarios.json"
        ]
        
        for file_name in scenario_files:
            file_path = self.eval_path / "scenarios" / file_name
            if file_path.exists():
                try:
                    with open(file_path) as f:
                        scenarios[file_name.replace('.json', '')] = json.load(f)
                    self.logger.info(f"Loaded scenarios: {file_name}")
                except Exception as e:
                    self.logger.error(f"Failed to load scenarios {file_name}: {e}")
        
        return scenarios
    
    async def run_comprehensive_evaluation(self, 
                                         test_categories: Optional[List[str]] = None,
                                         max_concurrent: int = 5) -> EvaluationReport:
        """Run comprehensive evaluation across all categories."""
        self.logger.info("Starting comprehensive Ultimate RAG evaluation")
        self.start_time = datetime.now(timezone.utc)
        
        # Determine test categories to run
        if test_categories is None:
            test_categories = [
                "basic_retrieval",
                "sophistication_levels", 
                "agentic_intelligence",
                "cross_domain",
                "ultimate_intelligence",
                "business_scenarios",
                "conversation_flow",
                "edge_cases",
                "performance_stress"
            ]
        
        # Execute all test categories
        for category in test_categories:
            self.logger.info(f"Running test category: {category}")
            try:
                await self._run_test_category(category, max_concurrent)
            except Exception as e:
                self.logger.error(f"Error in test category {category}: {e}")
                self.logger.error(traceback.format_exc())
        
        # Generate comprehensive report
        self.end_time = datetime.now(timezone.utc)
        report = self._generate_report()
        
        # Save report
        self._save_report(report)
        
        self.logger.info(f"Evaluation completed. Pass rate: {report.pass_rate:.2%}")
        return report
    
    async def _run_test_category(self, category: str, max_concurrent: int):
        """Run tests for a specific category."""
        test_queries = self._get_test_queries_for_category(category)
        
        if not test_queries:
            self.logger.warning(f"No test queries found for category: {category}")
            return
        
        self.logger.info(f"Running {len(test_queries)} tests for category: {category}")
        
        # Run tests with controlled concurrency
        semaphore = asyncio.Semaphore(max_concurrent)
        tasks = [
            self._run_single_test_with_semaphore(semaphore, query_data, category)
            for query_data in test_queries
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        for result in results:
            if isinstance(result, Exception):
                self.logger.error(f"Test execution error: {result}")
            elif isinstance(result, TestResult):
                self.test_results.append(result)
    
    async def _run_single_test_with_semaphore(self, semaphore: asyncio.Semaphore, 
                                            query_data: Dict[str, Any], 
                                            category: str) -> TestResult:
        """Run a single test with semaphore control."""
        async with semaphore:
            return await self._run_single_test(query_data, category)
    
    async def _run_single_test(self, query_data: Dict[str, Any], category: str) -> TestResult:
        """Run a single test and collect comprehensive metrics."""
        test_id = query_data.get('id', f"{category}_{len(self.test_results)}")
        query = query_data['query']
        sophistication = query_data.get('sophistication', 'standard')
        complexity = query_data.get('complexity', 'moderate')
        
        # Track system metrics before test
        initial_memory = psutil.virtual_memory().used / 1024 / 1024
        initial_cpu = psutil.cpu_percent()
        
        try:
            start_time = time.time()
            
            # Execute the query based on sophistication level
            response = await self._execute_query(query, sophistication, query_data)
            
            end_time = time.time()
            response_time_ms = (end_time - start_time) * 1000
            
            # Track system metrics after test
            final_memory = psutil.virtual_memory().used / 1024 / 1024
            final_cpu = psutil.cpu_percent()
            
            # Analyze response quality and intelligence
            quality_metrics = self._analyze_response_quality(response, query_data)
            intelligence_metrics = self._analyze_intelligence_features(response, query_data)
            business_metrics = self._analyze_business_value(response, query_data)
            
            # Determine if test passed
            sophistication_config = self.config['sophistication_levels'].get(sophistication, {})
            quality_threshold = sophistication_config.get('quality_threshold', 0.7)
            latency_threshold = sophistication_config.get('max_latency_ms', 3000)
            
            passed = (
                quality_metrics['overall_quality'] >= quality_threshold and
                response_time_ms <= latency_threshold and
                not quality_metrics.get('has_errors', False)
            )
            
            return TestResult(
                test_id=test_id,
                query=query,
                sophistication=sophistication,
                complexity=complexity,
                response_time_ms=response_time_ms,
                quality_score=quality_metrics['overall_quality'],
                strategy_used=response.get('metadata', {}).get('strategy_used', 'unknown'),
                domains_searched=response.get('metadata', {}).get('domains_searched', []),
                result_count=len(response.get('citations', [])),
                strategy_accuracy=intelligence_metrics.get('strategy_accuracy', 0.0),
                complexity_analysis_accuracy=intelligence_metrics.get('complexity_accuracy', 0.0),
                domain_routing_precision=intelligence_metrics.get('domain_precision', 0.0),
                business_value_score=business_metrics.get('business_value', 0.0),
                actionability_score=business_metrics.get('actionability', 0.0),
                memory_usage_mb=final_memory - initial_memory,
                cpu_usage_percent=final_cpu - initial_cpu,
                passed=passed,
                timestamp=datetime.now(timezone.utc).isoformat(),
                metadata={
                    'category': category,
                    'response_metadata': response.get('metadata', {}),
                    'quality_details': quality_metrics,
                    'intelligence_details': intelligence_metrics,
                    'business_details': business_metrics
                }
            )
            
        except Exception as e:
            self.logger.error(f"Test {test_id} failed: {e}")
            return TestResult(
                test_id=test_id,
                query=query,
                sophistication=sophistication,
                complexity=complexity,
                response_time_ms=0,
                quality_score=0.0,
                strategy_used="error",
                domains_searched=[],
                result_count=0,
                strategy_accuracy=0.0,
                complexity_analysis_accuracy=0.0,
                domain_routing_precision=0.0,
                business_value_score=0.0,
                actionability_score=0.0,
                memory_usage_mb=0,
                cpu_usage_percent=0,
                passed=False,
                error_message=str(e),
                timestamp=datetime.now(timezone.utc).isoformat(),
                metadata={'category': category, 'error': str(e)}
            )
    
    async def _execute_query(self, query: str, sophistication: str, query_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute query using appropriate sophistication level."""
        sophistication_level = getattr(SophisticationLevel, sophistication.upper(), SophisticationLevel.STANDARD)
        
        # Check if this should use ultimate search
        if sophistication == 'expert' or query_data.get('use_ultimate_search', False):
            return await self._execute_ultimate_search(query, sophistication_level, query_data)
        elif sophistication in ['advanced', 'expert'] and query_data.get('cross_domain', True):
            return await self._execute_cross_domain_search(query, sophistication_level, query_data)
        elif sophistication in ['standard', 'advanced', 'expert']:
            return await self._execute_agentic_search(query, sophistication_level, query_data)
        else:
            return await self._execute_basic_search(query, query_data)
    
    async def _execute_ultimate_search(self, query: str, sophistication: SophisticationLevel, 
                                     query_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute ultimate search with full intelligence."""
        try:
            user_context = query_data.get('user_context', {})
            
            # Use LlamaIndexManager for ultimate search
            results = self.llama_manager.ultimate_search(
                query=query,
                intent=query_data.get('intent', 'general'),
                sophistication=sophistication,
                user_context=user_context
            )
            
            return results
            
        except Exception as e:
            self.logger.error(f"Ultimate search failed: {e}")
            # Fallback to agentic search
            return await self._execute_agentic_search(query, sophistication, query_data)
    
    async def _execute_cross_domain_search(self, query: str, sophistication: SophisticationLevel,
                                         query_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute cross-domain search."""
        try:
            available_domains = query_data.get('expected_domains', [])
            fusion_strategy = query_data.get('fusion_strategy')
            
            results = self.llama_manager.cross_domain_search(
                query=query,
                available_domains=available_domains,
                sophistication=sophistication,
                fusion_strategy=fusion_strategy
            )
            
            return results
            
        except Exception as e:
            self.logger.error(f"Cross-domain search failed: {e}")
            # Fallback to agentic search
            return await self._execute_agentic_search(query, sophistication, query_data)
    
    async def _execute_agentic_search(self, query: str, sophistication: SophisticationLevel,
                                    query_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute agentic search."""
        try:
            explicit_mode = query_data.get('expected_strategy')
            if explicit_mode:
                mode = getattr(RetrievalMode, explicit_mode.upper(), None)
            else:
                mode = None
            
            results = self.search_service.search(
                query=query,
                sophistication=sophistication,
                explicit_mode=mode
            )
            
            return results
            
        except Exception as e:
            self.logger.error(f"Agentic search failed: {e}")
            # Fallback to basic search
            return await self._execute_basic_search(query, query_data)
    
    async def _execute_basic_search(self, query: str, query_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute basic search as fallback."""
        try:
            results = self.search_service.search(query)
            return results
        except Exception as e:
            self.logger.error(f"Basic search failed: {e}")
            return {
                'answer': f"Search failed: {str(e)}",
                'citations': [],
                'metadata': {'error': str(e)}
            }
    
    def _analyze_response_quality(self, response: Dict[str, Any], query_data: Dict[str, Any]) -> Dict[str, float]:
        """Analyze response quality across multiple dimensions."""
        quality_metrics = {}
        
        # Basic quality checks
        answer = response.get('answer', '')
        citations = response.get('citations', [])
        
        # Answer completeness (0-1)
        quality_metrics['completeness'] = min(1.0, len(answer) / 500) if answer else 0.0
        
        # Citation quality (0-1) 
        quality_metrics['citation_quality'] = min(1.0, len(citations) / 5) if citations else 0.0
        
        # Response coherence (basic check for structure)
        quality_metrics['coherence'] = 1.0 if answer and len(answer) > 50 else 0.5
        
        # Expected result count alignment
        expected_range = query_data.get('expected_results_count', [1, 10])
        actual_count = len(citations)
        if expected_range[0] <= actual_count <= expected_range[1]:
            quality_metrics['result_count_alignment'] = 1.0
        else:
            quality_metrics['result_count_alignment'] = 0.5
        
        # Error detection
        has_errors = 'error' in response.get('metadata', {}) or 'failed' in answer.lower()
        quality_metrics['has_errors'] = has_errors
        
        # Overall quality (weighted average)
        if not has_errors:
            quality_metrics['overall_quality'] = (
                quality_metrics['completeness'] * 0.3 +
                quality_metrics['citation_quality'] * 0.3 +
                quality_metrics['coherence'] * 0.2 +
                quality_metrics['result_count_alignment'] * 0.2
            )
        else:
            quality_metrics['overall_quality'] = 0.1
        
        return quality_metrics
    
    def _analyze_intelligence_features(self, response: Dict[str, Any], query_data: Dict[str, Any]) -> Dict[str, float]:
        """Analyze intelligence and agentic features."""
        intelligence_metrics = {}
        metadata = response.get('metadata', {})
        
        # Strategy accuracy
        expected_strategy = query_data.get('expected_strategy')
        actual_strategy = metadata.get('strategy_used')
        if expected_strategy and actual_strategy:
            intelligence_metrics['strategy_accuracy'] = 1.0 if expected_strategy in actual_strategy else 0.5
        else:
            intelligence_metrics['strategy_accuracy'] = 0.5
        
        # Domain routing precision
        expected_domains = query_data.get('expected_domains', [])
        actual_domains = metadata.get('domains_searched', [])
        if expected_domains and actual_domains:
            overlap = len(set(expected_domains) & set(actual_domains))
            precision = overlap / len(actual_domains) if actual_domains else 0
            intelligence_metrics['domain_precision'] = precision
        else:
            intelligence_metrics['domain_precision'] = 0.5
        
        # Complexity analysis (if available)
        complexity_info = metadata.get('complexity_analysis', {})
        if complexity_info:
            intelligence_metrics['complexity_accuracy'] = 0.8  # Assume good if present
        else:
            intelligence_metrics['complexity_accuracy'] = 0.5
        
        # Ultimate search features
        ultimate_info = metadata.get('ultimate_search_info', {})
        if ultimate_info:
            intelligence_metrics['ultimate_intelligence'] = 1.0
        else:
            intelligence_metrics['ultimate_intelligence'] = 0.0
        
        return intelligence_metrics
    
    def _analyze_business_value(self, response: Dict[str, Any], query_data: Dict[str, Any]) -> Dict[str, float]:
        """Analyze business value and actionability."""
        business_metrics = {}
        answer = response.get('answer', '')
        
        # Business context awareness
        business_context = query_data.get('business_context', '')
        if business_context and business_context.lower() in answer.lower():
            business_metrics['context_awareness'] = 1.0
        else:
            business_metrics['context_awareness'] = 0.5
        
        # Actionability (presence of specific recommendations)
        actionable_terms = ['recommend', 'suggest', 'should', 'action', 'implement', 'next steps']
        actionability_score = sum(1 for term in actionable_terms if term in answer.lower())
        business_metrics['actionability'] = min(1.0, actionability_score / 3)
        
        # Business value (comprehensive analysis, metrics, ROI mentioned)
        business_terms = ['roi', 'revenue', 'cost', 'efficiency', 'productivity', 'business impact']
        business_score = sum(1 for term in business_terms if term in answer.lower())
        business_metrics['business_value'] = min(1.0, business_score / 2)
        
        return business_metrics
    
    def _get_test_queries_for_category(self, category: str) -> List[Dict[str, Any]]:
        """Get test queries for a specific category."""
        queries = []
        
        # Check comprehensive dataset
        comprehensive_data = self.datasets.get('comprehensive_query_dataset', {})
        if category in comprehensive_data.get('test_cases', {}):
            category_data = comprehensive_data['test_cases'][category]
            queries.extend(category_data.get('queries', []))
        
        # Check enhanced dataset
        enhanced_data = self.datasets.get('enhanced_query_dataset', {})
        if category in enhanced_data.get('enhanced_test_cases', {}):
            category_data = enhanced_data['enhanced_test_cases'][category]
            queries.extend(category_data.get('queries', []))
        
        # Check scenarios
        scenario_data = self.scenarios.get('advanced_intelligence_scenarios', {})
        if category in scenario_data.get('ultimate_intelligence_scenarios', {}):
            category_scenarios = scenario_data['ultimate_intelligence_scenarios'][category]
            queries.extend(category_scenarios.get('scenarios', []))
        
        return queries
    
    def _generate_report(self) -> EvaluationReport:
        """Generate comprehensive evaluation report."""
        if not self.test_results:
            raise ValueError("No test results available for report generation")
        
        # Basic statistics
        total_tests = len(self.test_results)
        passed_tests = sum(1 for r in self.test_results if r.passed)
        failed_tests = total_tests - passed_tests
        pass_rate = passed_tests / total_tests
        
        # Performance metrics
        response_times = [r.response_time_ms for r in self.test_results]
        quality_scores = [r.quality_score for r in self.test_results]
        
        avg_response_time = statistics.mean(response_times)
        percentile_95_response_time = statistics.quantiles(response_times, n=20)[18]  # 95th percentile
        avg_quality_score = statistics.mean(quality_scores)
        min_quality_score = min(quality_scores)
        
        # Intelligence metrics
        strategy_accuracies = [r.strategy_accuracy for r in self.test_results]
        complexity_accuracies = [r.complexity_analysis_accuracy for r in self.test_results]
        domain_precisions = [r.domain_routing_precision for r in self.test_results]
        
        strategy_accuracy = statistics.mean(strategy_accuracies)
        complexity_analysis_accuracy = statistics.mean(complexity_accuracies)
        domain_routing_precision = statistics.mean(domain_precisions)
        
        # Sophistication level breakdown
        sophistication_results = {}
        for sophistication in ['basic', 'standard', 'advanced', 'expert']:
            soph_results = [r for r in self.test_results if r.sophistication == sophistication]
            if soph_results:
                sophistication_results[sophistication] = {
                    'total_tests': len(soph_results),
                    'passed_tests': sum(1 for r in soph_results if r.passed),
                    'pass_rate': sum(1 for r in soph_results if r.passed) / len(soph_results),
                    'avg_response_time_ms': statistics.mean([r.response_time_ms for r in soph_results]),
                    'avg_quality_score': statistics.mean([r.quality_score for r in soph_results])
                }
        
        # Business value metrics
        business_values = [r.business_value_score for r in self.test_results]
        actionability_scores = [r.actionability_score for r in self.test_results]
        
        business_value_score = statistics.mean(business_values)
        actionability_score = statistics.mean(actionability_scores)
        
        # System performance
        memory_usages = [r.memory_usage_mb for r in self.test_results if r.memory_usage_mb > 0]
        cpu_usages = [r.cpu_usage_percent for r in self.test_results if r.cpu_usage_percent > 0]
        
        avg_memory_usage = statistics.mean(memory_usages) if memory_usages else 0
        max_memory_usage = max(memory_usages) if memory_usages else 0
        avg_cpu_usage = statistics.mean(cpu_usages) if cpu_usages else 0
        max_cpu_usage = max(cpu_usages) if cpu_usages else 0
        
        # Duration
        duration = (self.end_time - self.start_time).total_seconds()
        
        return EvaluationReport(
            total_tests=total_tests,
            passed_tests=passed_tests,
            failed_tests=failed_tests,
            pass_rate=pass_rate,
            avg_response_time_ms=avg_response_time,
            percentile_95_response_time_ms=percentile_95_response_time,
            avg_quality_score=avg_quality_score,
            min_quality_score=min_quality_score,
            strategy_accuracy=strategy_accuracy,
            complexity_analysis_accuracy=complexity_analysis_accuracy,
            domain_routing_precision=domain_routing_precision,
            sophistication_results=sophistication_results,
            business_value_score=business_value_score,
            actionability_score=actionability_score,
            avg_memory_usage_mb=avg_memory_usage,
            max_memory_usage_mb=max_memory_usage,
            avg_cpu_usage_percent=avg_cpu_usage,
            max_cpu_usage_percent=max_cpu_usage,
            test_results=self.test_results,
            start_time=self.start_time.isoformat(),
            end_time=self.end_time.isoformat(),
            total_duration_seconds=duration
        )
    
    def _save_report(self, report: EvaluationReport):
        """Save comprehensive evaluation report."""
        results_dir = self.eval_path / "results"
        results_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save detailed JSON report
        json_file = results_dir / f"evaluation_report_{timestamp}.json"
        with open(json_file, 'w') as f:
            json.dump(asdict(report), f, indent=2, default=str)
        
        # Save executive summary
        summary_file = results_dir / f"executive_summary_{timestamp}.md"
        self._save_executive_summary(report, summary_file)
        
        # Save CSV for analysis
        csv_file = results_dir / f"test_results_{timestamp}.csv"
        self._save_csv_results(report.test_results, csv_file)
        
        self.logger.info(f"Reports saved:")
        self.logger.info(f"  Detailed report: {json_file}")
        self.logger.info(f"  Executive summary: {summary_file}")
        self.logger.info(f"  CSV results: {csv_file}")
    
    def _save_executive_summary(self, report: EvaluationReport, file_path: Path):
        """Save executive summary in markdown format."""
        with open(file_path, 'w') as f:
            f.write(f"""# Ultimate Agentic RAG System - Evaluation Report

**Evaluation Date:** {report.start_time}  
**Total Duration:** {report.total_duration_seconds:.2f} seconds  

## Executive Summary

The Ultimate Agentic RAG system has been comprehensively evaluated across all sophistication levels and intelligence features.

### Overall Performance
- **Tests Executed:** {report.total_tests}
- **Pass Rate:** {report.pass_rate:.1%}
- **Average Quality Score:** {report.avg_quality_score:.3f}
- **Average Response Time:** {report.avg_response_time_ms:.0f}ms

### Intelligence Assessment
- **Strategy Selection Accuracy:** {report.strategy_accuracy:.1%}
- **Complexity Analysis Accuracy:** {report.complexity_analysis_accuracy:.1%}
- **Domain Routing Precision:** {report.domain_routing_precision:.1%}

### Business Value
- **Business Value Score:** {report.business_value_score:.3f}
- **Actionability Score:** {report.actionability_score:.3f}

### Sophistication Level Performance

""")
            
            for sophistication, metrics in report.sophistication_results.items():
                f.write(f"""#### {sophistication.title()} Level
- Tests: {metrics['total_tests']} (Pass Rate: {metrics['pass_rate']:.1%})
- Avg Response Time: {metrics['avg_response_time_ms']:.0f}ms
- Avg Quality: {metrics['avg_quality_score']:.3f}

""")
            
            f.write(f"""### System Performance
- **Average Memory Usage:** {report.avg_memory_usage_mb:.1f}MB
- **Peak Memory Usage:** {report.max_memory_usage_mb:.1f}MB
- **Average CPU Usage:** {report.avg_cpu_usage_percent:.1f}%
- **Peak CPU Usage:** {report.max_cpu_usage_percent:.1f}%

### Recommendations

""")
            
            # Add recommendations based on results
            if report.pass_rate < 0.8:
                f.write("- **Priority**: Improve overall pass rate through quality enhancements\n")
            
            if report.avg_response_time_ms > 3000:
                f.write("- **Performance**: Optimize response times for better user experience\n")
            
            if report.strategy_accuracy < 0.8:
                f.write("- **Intelligence**: Enhance strategy selection algorithms\n")
            
            f.write(f"\n### Detailed Results\n\nSee full JSON report for detailed test results and metrics.\n")
    
    def _save_csv_results(self, test_results: List[TestResult], file_path: Path):
        """Save test results in CSV format for analysis."""
        import csv
        
        with open(file_path, 'w', newline='') as csvfile:
            fieldnames = [
                'test_id', 'query', 'sophistication', 'complexity',
                'response_time_ms', 'quality_score', 'strategy_used',
                'result_count', 'passed', 'error_message'
            ]
            
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            
            for result in test_results:
                writer.writerow({
                    'test_id': result.test_id,
                    'query': result.query[:100] + '...' if len(result.query) > 100 else result.query,
                    'sophistication': result.sophistication,
                    'complexity': result.complexity,
                    'response_time_ms': result.response_time_ms,
                    'quality_score': result.quality_score,
                    'strategy_used': result.strategy_used,
                    'result_count': result.result_count,
                    'passed': result.passed,
                    'error_message': result.error_message or ''
                })

async def main():
    """Main evaluation runner."""
    print("🚀 Starting Ultimate Agentic RAG Comprehensive Evaluation")
    
    evaluator = UltimateRAGEvaluator()
    
    try:
        report = await evaluator.run_comprehensive_evaluation()
        
        print(f"\n✅ Evaluation Complete!")
        print(f"📊 Results: {report.passed_tests}/{report.total_tests} tests passed ({report.pass_rate:.1%})")
        print(f"⏱️  Average response time: {report.avg_response_time_ms:.0f}ms")
        print(f"🎯 Average quality score: {report.avg_quality_score:.3f}")
        print(f"🧠 Intelligence metrics:")
        print(f"   - Strategy accuracy: {report.strategy_accuracy:.1%}")
        print(f"   - Domain routing precision: {report.domain_routing_precision:.1%}")
        
        if report.pass_rate >= 0.8:
            print("🏆 System performance meets excellence standards!")
        elif report.pass_rate >= 0.7:
            print("✅ System performance is acceptable with room for improvement")
        else:
            print("⚠️  System performance needs significant improvement")
        
        return 0 if report.pass_rate >= 0.7 else 1
        
    except Exception as e:
        print(f"❌ Evaluation failed: {e}")
        print(traceback.format_exc())
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
