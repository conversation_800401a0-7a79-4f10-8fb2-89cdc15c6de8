{"metadata": {"version": "1.0", "created": "2025-05-31", "description": "Representative document dataset for RAG evaluation", "document_count": 50, "domains_covered": ["slack_conversations", "slack_engineering", "slack_support", "github_code", "github_issues", "github_wiki", "documentation", "meeting_notes"]}, "documents": {"slack_engineering": [{"id": "SE001", "title": "Authentication System Discussion", "domain": "slack_engineering", "complexity": "moderate", "content": "We need to upgrade our authentication system to support OAuth 2.0 and JWT tokens. The current implementation uses basic session cookies which aren't scalable. Here's what I'm thinking:\n\n1. Replace session-based auth with JWT\n2. Implement OAuth 2.0 for third-party integrations\n3. Add rate limiting to prevent brute force attacks\n4. Use Redis for token blacklisting\n\nThoughts? @techLead @securityTeam", "metadata": {"channel": "engineering", "participants": ["developer1", "techLead", "securityTeam"], "timestamp": "2024-03-15T14:30:00Z", "has_code": false, "technical_complexity": 0.7, "thread_replies": 8}}, {"id": "SE002", "title": "Database Performance Optimization", "domain": "slack_engineering", "complexity": "complex", "content": "We're seeing slow query performance on the user_sessions table. Here's what I found:\n\n```sql\nEXPLAIN ANALYZE SELECT * FROM user_sessions WHERE user_id = ? AND expires_at > NOW();\n```\n\nThe query is doing a full table scan. We need:\n1. Composite index on (user_id, expires_at)\n2. Partition the table by month\n3. Add a cleanup job for expired sessions\n\nCurrent query time: 2.3s\nExpected after optimization: <100ms\n\nI'll create a PR with the migration.", "metadata": {"channel": "engineering", "participants": ["db<PERSON><PERSON><PERSON>", "developer2", "techLead"], "timestamp": "2024-03-20T09:15:00Z", "has_code": true, "technical_complexity": 0.8, "thread_replies": 12}}, {"id": "SE003", "title": "Microservices Architecture Planning", "domain": "slack_engineering", "complexity": "complex", "content": "Breaking down our monolith into microservices. Here's the proposed architecture:\n\n**Core Services:**\n- User Service (authentication, profiles)\n- Order Service (order management)\n- Payment Service (payment processing)\n- Notification Service (emails, SMS)\n- Analytics Service (metrics, reporting)\n\n**Infrastructure:**\n- API Gateway (Kong)\n- Service Discovery (Consul)\n- Message Queue (RabbitMQ)\n- Database per service\n- Distributed logging (ELK)\n\n**Challenges:**\n- Data consistency across services\n- Distributed transactions\n- Service communication patterns\n- Monitoring and debugging\n\nWhat are your thoughts on the service boundaries?", "metadata": {"channel": "architecture", "participants": ["architect", "techLead", "developer1", "developer3"], "timestamp": "2024-03-25T16:45:00Z", "has_code": false, "technical_complexity": 0.9, "thread_replies": 23}}], "slack_support": [{"id": "SS001", "title": "Customer Login Issues Report", "domain": "slack_support", "complexity": "moderate", "content": "Multiple customers reporting login issues today:\n\n**Issue Summary:**\n- Users getting 'Invalid credentials' error\n- Problem started around 2 PM EST\n- Affects both web and mobile app\n- Password reset not working either\n\n**Affected Customers:**\n- Enterprise clients: TechCorp, DataSoft\n- ~200 users impacted so far\n\n**Temporary Workaround:**\nClearing browser cache helps some users\n\n@engineering Can you check if there's an issue with the auth service?", "metadata": {"channel": "support", "participants": ["supportAgent1", "supportLead", "engineering"], "timestamp": "2024-03-15T14:45:00Z", "urgency": "high", "customer_impact": "high", "thread_replies": 15}}, {"id": "SS002", "title": "API Rate Limiting Customer Complaints", "domain": "slack_support", "complexity": "moderate", "content": "Getting complaints about API rate limiting being too aggressive:\n\n**Customer Feedback:**\n'Our integration is hitting rate limits during normal usage'\n'The 100 requests/minute limit is too low for our workflow'\n'No clear error messages when rate limited'\n\n**Customers Affected:**\n- DevTools Inc (Premium plan)\n- AutoSync Corp (Enterprise plan) \n- MegaData Ltd (Enterprise plan)\n\n**Requested Changes:**\n1. Increase limits for paid plans\n2. Better error messages\n3. Rate limit headers in API responses\n\nShould we escalate to product team?", "metadata": {"channel": "support", "participants": ["supportAgent2", "supportLead", "productManager"], "timestamp": "2024-03-18T11:20:00Z", "urgency": "medium", "customer_impact": "medium", "thread_replies": 9}}], "github_code": [{"id": "GC001", "title": "Authentication Service Implementation", "domain": "github_code", "complexity": "complex", "content": "```python\nclass AuthenticationService:\n    \"\"\"Main authentication service handling JWT tokens and OAuth.\"\"\"\n    \n    def __init__(self, secret_key, token_expiry=3600):\n        self.secret_key = secret_key\n        self.token_expiry = token_expiry\n        self.redis_client = redis.Redis()\n    \n    def authenticate_user(self, username, password):\n        \"\"\"Authenticate user credentials and return JWT token.\"\"\"\n        user = User.objects.filter(username=username).first()\n        if not user or not user.check_password(password):\n            raise AuthenticationError('Invalid credentials')\n        \n        if self.is_account_locked(user):\n            raise AuthenticationError('Account locked due to failed attempts')\n            \n        token = self.generate_jwt_token(user)\n        self.track_login_attempt(user, success=True)\n        return token\n    \n    def generate_jwt_token(self, user):\n        \"\"\"Generate JWT token for authenticated user.\"\"\"\n        payload = {\n            'user_id': user.id,\n            'username': user.username,\n            'exp': datetime.utcnow() + timedelta(seconds=self.token_expiry),\n            'iat': datetime.utcnow()\n        }\n        return jwt.encode(payload, self.secret_key, algorithm='HS256')\n    \n    def validate_token(self, token):\n        \"\"\"Validate JWT token and return user info.\"\"\"\n        try:\n            if self.is_token_blacklisted(token):\n                raise AuthenticationError('Token has been revoked')\n                \n            payload = jwt.decode(token, self.secret_key, algorithms=['HS256'])\n            return payload\n        except jwt.ExpiredSignatureError:\n            raise AuthenticationError('Token has expired')\n        except jwt.InvalidTokenError:\n            raise AuthenticationError('Invalid token')\n    \n    def logout_user(self, token):\n        \"\"\"Logout user by blacklisting their token.\"\"\"\n        self.blacklist_token(token)\n        \n    def blacklist_token(self, token):\n        \"\"\"Add token to Redis blacklist.\"\"\"\n        payload = jwt.decode(token, self.secret_key, algorithms=['HS256'], options={'verify_exp': False})\n        exp_time = payload.get('exp')\n        if exp_time:\n            ttl = exp_time - int(datetime.utcnow().timestamp())\n            if ttl > 0:\n                self.redis_client.setex(f\"blacklist:{token}\", ttl, \"true\")\n```", "metadata": {"file_path": "src/auth/services.py", "language": "python", "file_size": 2840, "complexity_score": 0.8, "functions": ["authenticate_user", "generate_jwt_token", "validate_token", "logout_user"], "classes": ["AuthenticationService"], "imports": ["jwt", "redis", "datetime"], "last_modified": "2024-03-15T12:00:00Z"}}, {"id": "GC002", "title": "Database Configuration", "domain": "github_code", "complexity": "moderate", "content": "```yaml\n# Database Configuration\ndatabase:\n  host: localhost\n  port: 5432\n  name: myapp_production\n  username: ${DB_USER}\n  password: ${DB_PASSWORD}\n  pool_size: 20\n  max_overflow: 30\n  pool_timeout: 30\n  pool_recycle: 3600\n  \n# Redis Configuration  \nredis:\n  host: localhost\n  port: 6379\n  db: 0\n  password: ${REDIS_PASSWORD}\n  max_connections: 50\n  socket_timeout: 5\n  \n# Authentication Settings\nauth:\n  jwt_secret: ${JWT_SECRET_KEY}\n  token_expiry: 3600  # 1 hour\n  refresh_token_expiry: 86400  # 24 hours\n  max_login_attempts: 5\n  lockout_duration: 900  # 15 minutes\n  \n# API Rate Limiting\nrate_limiting:\n  default_limit: 100  # requests per minute\n  authenticated_limit: 200\n  premium_limit: 500\n  burst_limit: 150\n  \n# Logging Configuration\nlogging:\n  level: INFO\n  format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'\n  file: '/var/log/myapp/app.log'\n  max_size: 100MB\n  backup_count: 5\n```", "metadata": {"file_path": "config/production.yaml", "language": "yaml", "file_size": 1250, "complexity_score": 0.4, "config_sections": ["database", "redis", "auth", "rate_limiting", "logging"], "environment_vars": ["DB_USER", "DB_PASSWORD", "REDIS_PASSWORD", "JWT_SECRET_KEY"], "last_modified": "2024-03-20T08:30:00Z"}}], "github_issues": [{"id": "GI001", "title": "Users unable to login - authentication service returning 500 errors", "domain": "github_issues", "complexity": "moderate", "content": "**Bug Report**\n\n**Description:**\nUsers are experiencing login failures with 500 Internal Server Error responses from the authentication service.\n\n**Steps to Reproduce:**\n1. Navigate to login page\n2. Enter valid username/password\n3. Click login button\n4. Observe 500 error\n\n**Expected Behavior:**\nUser should be logged in successfully\n\n**Actual Behavior:**\n500 Internal Server Error returned\n\n**Error Logs:**\n```\n2024-03-15 14:32:15 ERROR AuthenticationService: Redis connection failed\nConnectionError: [Errno 111] Connection refused\n    at redis.connection.Connection.connect()\n```\n\n**Environment:**\n- Version: 2.1.4\n- Environment: Production\n- Affected users: ~200\n\n**Impact:**\nHigh - Users cannot access the application\n\n**Workaround:**\nRestarting Redis service temporarily resolves the issue", "metadata": {"issue_number": 1247, "status": "open", "priority": "critical", "assignee": "devops_team", "labels": ["bug", "authentication", "redis", "production"], "created": "2024-03-15T14:35:00Z", "comments": 8}}, {"id": "GI002", "title": "Feature Request: Implement OAuth 2.0 for third-party integrations", "domain": "github_issues", "complexity": "complex", "content": "**Feature Request**\n\n**Summary:**\nImplement OAuth 2.0 authorization server to allow third-party applications to integrate with our API.\n\n**Business Case:**\n- Enterprise customers need to integrate with their existing systems\n- Current API key approach is less secure and harder to manage\n- OAuth 2.0 is industry standard for API authorization\n\n**Requirements:**\n1. Support Authorization Code flow\n2. Support Client Credentials flow for server-to-server\n3. Implement scopes for granular permissions\n4. Provide admin UI for managing OAuth clients\n5. Support PKCE for mobile/SPA applications\n\n**Technical Specifications:**\n- Use existing JWT infrastructure where possible\n- Store client credentials securely\n- Implement proper scope validation\n- Add rate limiting per client\n- Comprehensive audit logging\n\n**Acceptance Criteria:**\n- [ ] OAuth 2.0 endpoints implemented (/authorize, /token)\n- [ ] Client management API and UI\n- [ ] Scope-based authorization\n- [ ] Integration tests with sample client\n- [ ] Documentation and examples\n\n**Effort Estimate:** 3-4 sprints\n**Priority:** High", "metadata": {"issue_number": 1134, "status": "in_progress", "priority": "high", "assignee": "backend_team", "labels": ["feature", "authentication", "o<PERSON>h", "api"], "created": "2024-02-28T10:00:00Z", "comments": 15}}], "documentation": [{"id": "DOC001", "title": "Authentication API Documentation", "domain": "documentation", "complexity": "moderate", "content": "# Authentication API\n\nOur authentication system uses JWT tokens for API access. This guide covers authentication endpoints and usage.\n\n## Quick Start\n\n1. **Login** to get a JWT token\n2. **Include token** in API requests\n3. **Refresh token** before expiry\n\n## Endpoints\n\n### POST /auth/login\nAuthenticate user and receive JW<PERSON> token.\n\n**Request:**\n```json\n{\n  \"username\": \"<EMAIL>\",\n  \"password\": \"your_password\"\n}\n```\n\n**Response:**\n```json\n{\n  \"access_token\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...\",\n  \"refresh_token\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...\",\n  \"expires_in\": 3600,\n  \"token_type\": \"Bearer\"\n}\n```\n\n### POST /auth/refresh\nRefresh expired access token.\n\n**Request:**\n```json\n{\n  \"refresh_token\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...\"\n}\n```\n\n### POST /auth/logout\nLogout and invalidate tokens.\n\n**Headers:**\n```\nAuthorization: Bearer your_access_token\n```\n\n## Using JWT Tokens\n\nInclude the access token in the Authorization header:\n\n```bash\ncurl -H \"Authorization: Bearer YOUR_TOKEN\" \\\n     https://api.example.com/users/profile\n```\n\n## Error Handling\n\n| Status Code | Description |\n|------------|-------------|\n| 401 | Invalid credentials |\n| 403 | Account locked |\n| 429 | Rate limit exceeded |\n\n## Rate Limits\n\n- **Anonymous users:** 50 requests/minute\n- **Authenticated users:** 200 requests/minute\n- **Premium users:** 500 requests/minute\n\n## Security Best Practices\n\n1. **Store tokens securely** - Never log or expose tokens\n2. **Use HTTPS only** - Tokens must be transmitted securely\n3. **Handle expiry gracefully** - Implement automatic token refresh\n4. **Validate on server** - Always verify tokens server-side", "metadata": {"doc_type": "api_guide", "file_path": "docs/authentication.md", "sections": ["quick_start", "endpoints", "usage", "security"], "last_updated": "2024-03-10T15:00:00Z", "readability_score": 0.8}}, {"id": "DOC002", "title": "Deployment Guide", "domain": "documentation", "complexity": "complex", "content": "# Production Deployment Guide\n\nThis guide covers deploying the application to production infrastructure.\n\n## Prerequisites\n\n- Docker 20.10+\n- Kubernetes 1.21+\n- PostgreSQL 13+\n- Redis 6.0+\n- Load balancer (nginx/HAProxy)\n\n## Environment Setup\n\n### 1. Database Setup\n\n```bash\n# Create production database\ncreatedb myapp_production\n\n# Run migrations\npython manage.py migrate --settings=config.production\n\n# Create superuser\npython manage.py createsuperuser --settings=config.production\n```\n\n### 2. Redis Configuration\n\n```bash\n# Configure Redis for production\nsudo systemctl start redis\nsudo systemctl enable redis\n\n# Test connection\nredis-cli ping\n```\n\n### 3. Application Deployment\n\n```bash\n# Build Docker image\ndocker build -t myapp:latest .\n\n# Push to registry\ndocker push registry.company.com/myapp:latest\n\n# Deploy to Kubernetes\nkubectl apply -f k8s/production/\n```\n\n## Configuration\n\n### Environment Variables\n\n```bash\nexport DB_HOST=postgres.internal\nexport DB_PASSWORD=secure_password\nexport REDIS_HOST=redis.internal\nexport JWT_SECRET_KEY=your_secret_key\nexport DJANGO_SETTINGS_MODULE=config.production\n```\n\n### Kubernetes Deployment\n\n```yaml\napiVersion: apps/v1\nkind: Deployment\nmetadata:\n  name: myapp\nspec:\n  replicas: 3\n  selector:\n    matchLabels:\n      app: myapp\n  template:\n    metadata:\n      labels:\n        app: myapp\n    spec:\n      containers:\n      - name: myapp\n        image: registry.company.com/myapp:latest\n        ports:\n        - containerPort: 8000\n        env:\n        - name: DB_HOST\n          valueFrom:\n            secretKeyRef:\n              name: db-secret\n              key: host\n```\n\n## Monitoring and Health Checks\n\n### Health Check Endpoint\n\n```bash\ncurl http://localhost:8000/health/\n```\n\n### Monitoring Setup\n\n1. **Prometheus** - Metrics collection\n2. **Grafana** - Dashboards and alerting\n3. **ELK Stack** - Log aggregation\n4. **Sentry** - Error tracking\n\n## Backup and Recovery\n\n### Database Backups\n\n```bash\n# Daily backup script\npg_dump myapp_production > backup_$(date +%Y%m%d).sql\n\n# Upload to S3\naws s3 cp backup_$(date +%Y%m%d).sql s3://myapp-backups/\n```\n\n### Disaster Recovery\n\n1. **RTO:** 4 hours\n2. **RPO:** 1 hour\n3. **Backup frequency:** Daily\n4. **Cross-region replication:** Enabled", "metadata": {"doc_type": "deployment_guide", "file_path": "docs/deployment.md", "sections": ["prerequisites", "setup", "configuration", "monitoring", "backup"], "last_updated": "2024-03-25T12:00:00Z", "readability_score": 0.7}}], "meeting_notes": [{"id": "MN001", "title": "Architecture Review - Authentication System", "domain": "meeting_notes", "complexity": "complex", "content": "# Architecture Review Meeting\n**Date:** March 22, 2024\n**Attendees:** Tech Lead, Security Team, Backend Team\n\n## Current Authentication System Review\n\n### What's Working Well\n- JWT implementation is solid\n- Redis integration for token blacklisting\n- Rate limiting prevents abuse\n- Good error handling and logging\n\n### Pain Points Identified\n- Session management complexity\n- No OAuth support for third-party integrations\n- Limited granular permissions\n- Manual client credential management\n\n## Proposed Improvements\n\n### Phase 1: OAuth 2.0 Implementation\n**Timeline:** 4 weeks\n**Effort:** 2 developers\n\n**Scope:**\n- Authorization code flow\n- Client credentials flow\n- Basic scope management\n- Admin UI for client management\n\n### Phase 2: Enhanced Security\n**Timeline:** 2 weeks\n**Effort:** 1 developer + security review\n\n**Scope:**\n- PKCE support for mobile/SPA\n- Enhanced audit logging\n- Security headers implementation\n- Penetration testing\n\n### Phase 3: Scalability Improvements\n**Timeline:** 3 weeks\n**Effort:** 1 developer + DevOps\n\n**Scope:**\n- Redis clustering for HA\n- Token refresh optimization\n- Load testing and performance tuning\n- Monitoring and alerting\n\n## Decisions Made\n\n1. **Approved** OAuth 2.0 implementation for Q2\n2. **Assigned** Backend team lead as project owner\n3. **Scheduled** Security review for Phase 2\n4. **Budgeted** Additional Redis cluster for Phase 3\n\n## Action Items\n\n- [ ] Create detailed technical specification (John - Due: Mar 25)\n- [ ] Set up OAuth 2.0 development environment (Sarah - Due: Mar 27)\n- [ ] Schedule security review meeting (Mike - Due: Mar 28)\n- [ ] Prepare load testing scenarios (DevOps - Due: Apr 1)\n\n## Next Meeting\n**Date:** March 29, 2024\n**Agenda:** Technical specification review", "metadata": {"meeting_type": "architecture_review", "attendees": ["tech_lead", "security_team", "backend_team"], "date": "2024-03-22T14:00:00Z", "duration_minutes": 90, "decisions": 4, "action_items": 4}}]}}