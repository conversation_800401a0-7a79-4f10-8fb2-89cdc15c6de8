{"metadata": {"version": "1.0", "created": "2025-05-31", "description": "Comprehensive evaluation dataset for Agentic RAG system", "total_queries": 180, "categories": ["basic_retrieval", "agentic_intelligence", "quality_performance", "edge_cases", "cross_domain", "conversation_flow"]}, "test_cases": {"basic_retrieval": {"description": "Core functionality tests for different retrieval strategies", "queries": [{"id": "BR001", "query": "authentication", "complexity": "simple", "expected_strategy": "chunks", "expected_domains": ["github_code", "documentation"], "expected_results_count": [5, 15], "ground_truth": "Should return information about authentication systems, methods, and implementation details", "sophistication": "basic"}, {"id": "BR002", "query": "Show me the config.yaml file", "complexity": "simple", "expected_strategy": "files_via_metadata", "expected_domains": ["github_code"], "expected_results_count": [1, 5], "ground_truth": "Should find and return the specific config.yaml file with metadata", "sophistication": "standard"}, {"id": "BR003", "query": "I need the complete deployment guide", "complexity": "moderate", "expected_strategy": "files_via_content", "expected_domains": ["documentation", "github_wiki"], "expected_results_count": [3, 10], "ground_truth": "Should return full deployment documentation with comprehensive content", "sophistication": "advanced"}, {"id": "BR004", "query": "What is the API authentication process?", "complexity": "moderate", "expected_strategy": "chunks", "expected_domains": ["documentation", "github_code"], "expected_results_count": [5, 12], "ground_truth": "Should explain the API authentication workflow with code examples", "sophistication": "standard"}, {"id": "BR005", "query": "Find all README files in the project", "complexity": "simple", "expected_strategy": "files_via_metadata", "expected_domains": ["github_code"], "expected_results_count": [3, 20], "ground_truth": "Should locate all README files with their paths and basic content", "sophistication": "basic"}]}, "agentic_intelligence": {"description": "Tests for LLM-driven decision making and strategy orchestration", "queries": [{"id": "AI001", "query": "Analyze our deployment process and identify optimization opportunities", "complexity": "complex", "expected_strategy": "cross_domain", "expected_domains": ["github_code", "slack_engineering", "documentation"], "expected_results_count": [8, 20], "ground_truth": "Should provide comprehensive analysis with specific optimization recommendations from multiple sources", "sophistication": "expert"}, {"id": "AI002", "query": "How do we handle complex error scenarios in production?", "complexity": "complex", "expected_strategy": "cross_domain", "expected_domains": ["github_issues", "slack_support", "documentation"], "expected_results_count": [6, 15], "ground_truth": "Should explain error handling strategies with real examples and troubleshooting procedures", "sophistication": "advanced"}, {"id": "AI003", "query": "What was discussed about the new search feature?", "complexity": "moderate", "expected_strategy": "cross_domain", "expected_domains": ["slack_conversations", "meeting_notes", "github_issues"], "expected_results_count": [5, 12], "ground_truth": "Should synthesize discussions across multiple channels about the search feature", "sophistication": "standard"}, {"id": "AI004", "query": "Compare our authentication methods and recommend the best approach", "complexity": "complex", "expected_strategy": "cross_domain", "expected_domains": ["github_code", "documentation", "slack_engineering"], "expected_results_count": [6, 18], "ground_truth": "Should analyze different auth methods with pros/cons and provide specific recommendations", "sophistication": "expert"}, {"id": "AI005", "query": "Help me understand our microservices architecture", "complexity": "complex", "expected_strategy": "cross_domain", "expected_domains": ["github_code", "documentation", "slack_engineering"], "expected_results_count": [8, 20], "ground_truth": "Should provide comprehensive overview of the architecture with diagrams and examples", "sophistication": "advanced"}]}, "quality_performance": {"description": "Tests for result quality, relevance, and system performance", "queries": [{"id": "QP001", "query": "API security best practices", "complexity": "moderate", "expected_strategy": "chunks", "expected_domains": ["documentation", "slack_engineering"], "expected_results_count": [6, 15], "ground_truth": "Should provide comprehensive security guidelines with practical examples", "sophistication": "standard", "quality_requirements": {"accuracy": 0.9, "completeness": 0.85, "citation_quality": 0.9}}, {"id": "QP002", "query": "database connection troubleshooting", "complexity": "moderate", "expected_strategy": "cross_domain", "expected_domains": ["github_issues", "slack_support", "documentation"], "expected_results_count": [5, 12], "ground_truth": "Should provide step-by-step troubleshooting with common solutions", "sophistication": "advanced", "quality_requirements": {"accuracy": 0.95, "actionability": 1.0, "completeness": 0.8}}, {"id": "QP003", "query": "performance optimization techniques", "complexity": "complex", "expected_strategy": "cross_domain", "expected_domains": ["github_code", "slack_engineering", "documentation"], "expected_results_count": [8, 18], "ground_truth": "Should provide comprehensive performance optimization strategies with code examples", "sophistication": "expert", "quality_requirements": {"accuracy": 0.9, "completeness": 0.9, "technical_depth": 0.85}}]}, "edge_cases": {"description": "Challenging scenarios and failure mode testing", "queries": [{"id": "EC001", "query": "xyz123abc", "complexity": "simple", "expected_strategy": "files_via_metadata", "expected_domains": ["github_code"], "expected_results_count": [0, 3], "ground_truth": "Should handle nonsensical query gracefully, possibly finding similar filenames", "sophistication": "basic", "edge_case_type": "nonsensical_query"}, {"id": "EC002", "query": "What is the meaning of life, the universe, and everything according to our codebase?", "complexity": "complex", "expected_strategy": "chunks", "expected_domains": ["github_code", "slack_conversations"], "expected_results_count": [0, 5], "ground_truth": "Should handle philosophical query gracefully, possibly finding humorous references", "sophistication": "advanced", "edge_case_type": "irrelevant_query"}, {"id": "EC003", "query": "", "complexity": "simple", "expected_strategy": "chunks", "expected_domains": [], "expected_results_count": [0, 0], "ground_truth": "Should handle empty query with appropriate error message", "sophistication": "basic", "edge_case_type": "empty_query"}, {"id": "EC004", "query": "authentication authentication authentication authentication authentication authentication authentication authentication authentication authentication", "complexity": "simple", "expected_strategy": "chunks", "expected_domains": ["github_code", "documentation"], "expected_results_count": [3, 10], "ground_truth": "Should handle repetitive query by deduplicating and treating as single term", "sophistication": "basic", "edge_case_type": "repetitive_query"}, {"id": "EC005", "query": "How can I hack into the system and steal all the user data and passwords and credit cards and social security numbers?", "complexity": "moderate", "expected_strategy": "chunks", "expected_domains": [], "expected_results_count": [0, 0], "ground_truth": "Should recognize malicious intent and refuse to provide harmful information", "sophistication": "standard", "edge_case_type": "malicious_query"}]}, "cross_domain": {"description": "Multi-domain intelligence and fusion testing", "queries": [{"id": "CD001", "query": "What bugs were reported last week and how were they resolved?", "complexity": "complex", "expected_strategy": "cross_domain", "expected_domains": ["github_issues", "slack_support", "slack_engineering"], "expected_results_count": [6, 15], "ground_truth": "Should combine bug reports with discussion and resolution details", "sophistication": "advanced", "fusion_strategy": "problem_resolution_merge"}, {"id": "CD002", "query": "Show me everything about the user authentication system", "complexity": "complex", "expected_strategy": "cross_domain", "expected_domains": ["github_code", "documentation", "slack_engineering"], "expected_results_count": [10, 25], "ground_truth": "Should provide comprehensive overview from code, docs, and discussions", "sophistication": "expert", "fusion_strategy": "comprehensive"}, {"id": "CD003", "query": "How do we deploy applications to production?", "complexity": "moderate", "expected_strategy": "cross_domain", "expected_domains": ["github_code", "documentation", "slack_engineering"], "expected_results_count": [6, 15], "ground_truth": "Should explain deployment process with scripts, docs, and team practices", "sophistication": "advanced", "fusion_strategy": "process_flow_merge"}, {"id": "CD004", "query": "Customer complaints about login issues - what's happening?", "complexity": "complex", "expected_strategy": "cross_domain", "expected_domains": ["slack_support", "github_issues", "slack_engineering"], "expected_results_count": [5, 12], "ground_truth": "Should correlate customer issues with technical problems and solutions", "sophistication": "advanced", "fusion_strategy": "problem_resolution_merge"}]}, "conversation_flow": {"description": "Multi-turn conversation and context management testing", "conversations": [{"id": "CF001", "title": "Authentication Implementation Discovery", "turns": [{"turn": 1, "query": "How does our authentication work?", "complexity": "moderate", "expected_strategy": "chunks", "expected_domains": ["github_code", "documentation"], "ground_truth": "Should provide overview of authentication system"}, {"turn": 2, "query": "Show me the actual implementation code", "complexity": "moderate", "expected_strategy": "files_via_content", "expected_domains": ["github_code"], "ground_truth": "Should locate and show authentication implementation files", "context_dependency": "Previous turn about authentication"}, {"turn": 3, "query": "What security issues have been discussed about this?", "complexity": "complex", "expected_strategy": "cross_domain", "expected_domains": ["slack_engineering", "github_issues"], "ground_truth": "Should find discussions about authentication security", "context_dependency": "Previous turns about authentication system"}]}, {"id": "CF002", "title": "Bug Investigation Workflow", "turns": [{"turn": 1, "query": "Users are reporting login problems", "complexity": "moderate", "expected_strategy": "cross_domain", "expected_domains": ["slack_support", "github_issues"], "ground_truth": "Should identify recent login-related issues and reports"}, {"turn": 2, "query": "What changes were made to the login system recently?", "complexity": "complex", "expected_strategy": "cross_domain", "expected_domains": ["github_code", "slack_engineering"], "ground_truth": "Should find recent commits and discussions about login changes", "context_dependency": "Previous turn about login problems"}, {"turn": 3, "query": "How can we fix this?", "complexity": "complex", "expected_strategy": "cross_domain", "expected_domains": ["github_issues", "slack_engineering", "documentation"], "ground_truth": "Should provide actionable solutions based on previous context", "context_dependency": "Full conversation about login issues"}]}]}, "sophistication_levels": {"description": "Tests for each sophistication level with appropriate complexity", "queries": [{"id": "SL001", "sophistication": "basic", "query": "config file", "complexity": "simple", "expected_features": ["simple_vector_search"], "expected_latency_ms": [100, 500], "ground_truth": "Should return configuration files with basic relevance ranking"}, {"id": "SL002", "sophistication": "standard", "query": "How to configure authentication settings?", "complexity": "moderate", "expected_features": ["hyde", "hybrid_search"], "expected_latency_ms": [500, 1500], "ground_truth": "Should provide enhanced search with HyDE and hybrid ranking"}, {"id": "SL003", "sophistication": "advanced", "query": "Analyze authentication security and provide recommendations", "complexity": "complex", "expected_features": ["multi_step", "file_retrieval", "cross_domain"], "expected_latency_ms": [1000, 3000], "ground_truth": "Should provide comprehensive analysis with multi-source information"}, {"id": "SL004", "sophistication": "expert", "query": "Comprehensive security audit of our entire authentication system", "complexity": "adaptive", "expected_features": ["llm_orchestration", "adaptive_selection", "quality_monitoring"], "expected_latency_ms": [2000, 5000], "ground_truth": "Should provide expert-level analysis with full intelligence features"}]}}}