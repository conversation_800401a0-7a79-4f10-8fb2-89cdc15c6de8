{"metadata": {"version": "2.0", "created": "2025-05-31", "description": "Enhanced evaluation scenarios for Agentic RAG testing all intelligence levels", "total_scenarios": 120, "categories": ["agentic_intelligence", "complexity_analysis", "strategy_orchestration", "ultimate_search", "quality_monitoring", "adaptive_improvement"]}, "test_scenarios": {"agentic_intelligence": {"description": "Test LLM-driven decision making and strategy orchestration", "scenarios": [{"id": "AI_ADV001", "name": "Complex Multi-Domain Analysis", "query": "Analyze our entire authentication system including code, discussions, issues, and documentation. Identify security vulnerabilities and optimization opportunities.", "complexity": "adaptive", "sophistication": "expert", "expected_behavior": {"strategy_orchestration": "ultimate_search", "complexity_analysis": {"semantic_complexity": 0.9, "domain_complexity": 0.8, "temporal_complexity": 0.6, "structural_complexity": 0.7, "contextual_complexity": 0.8}, "domains": ["github_code", "slack_engineering", "github_issues", "documentation", "slack_support"], "fusion_strategy": "comprehensive", "quality_threshold": 0.85}, "validation_criteria": {"includes_code_analysis": true, "includes_security_recommendations": true, "cites_multiple_domains": true, "provides_specific_improvements": true, "result_count_range": [10, 25]}}, {"id": "AI_ADV002", "name": "Workflow Optimization with Context", "query": "Our deployment process is slow and error-prone. Analyze our current deployment workflow from code, scripts, discussions, and issues. Provide a comprehensive optimization plan.", "complexity": "complex", "sophistication": "expert", "expected_behavior": {"strategy_orchestration": "cross_domain_with_intelligence", "domains": ["github_code", "slack_engineering", "documentation", "github_issues"], "fusion_strategy": "process_flow_merge", "quality_threshold": 0.8}, "validation_criteria": {"analyzes_current_process": true, "identifies_bottlenecks": true, "provides_optimization_plan": true, "includes_implementation_steps": true, "cites_relevant_discussions": true}}, {"id": "AI_ADV003", "name": "Intelligent Problem Solving", "query": "Multiple customers are reporting login failures since last Friday. Investigate the root cause by analyzing recent changes, error reports, and team discussions.", "complexity": "complex", "sophistication": "advanced", "expected_behavior": {"strategy_orchestration": "problem_resolution_focused", "domains": ["slack_support", "github_issues", "github_code", "slack_engineering"], "fusion_strategy": "problem_resolution_merge", "temporal_awareness": true}, "validation_criteria": {"correlates_timeline": true, "identifies_recent_changes": true, "analyzes_error_patterns": true, "provides_troubleshooting_steps": true, "suggests_immediate_fixes": true}}]}, "complexity_analysis": {"description": "Test multi-dimensional complexity analysis and adaptive strategy selection", "scenarios": [{"id": "CA001", "name": "Simple Query with Basic Strategy", "query": "config.yaml", "complexity": "simple", "sophistication": "basic", "expected_behavior": {"complexity_scores": {"semantic_complexity": 0.1, "domain_complexity": 0.2, "temporal_complexity": 0.1, "structural_complexity": 0.2, "contextual_complexity": 0.1}, "strategy_decision": "files_via_metadata", "domains": ["github_code"], "confidence": 0.9}}, {"id": "CA002", "name": "Moderate Query with Enhanced Strategy", "query": "How does authentication work and what are the security considerations?", "complexity": "moderate", "sophistication": "standard", "expected_behavior": {"complexity_scores": {"semantic_complexity": 0.6, "domain_complexity": 0.5, "temporal_complexity": 0.3, "structural_complexity": 0.4, "contextual_complexity": 0.5}, "strategy_decision": "chunks_with_enhancement", "domains": ["github_code", "documentation"], "enable_hyde": true, "enable_hybrid": true}}, {"id": "CA003", "name": "Complex Query with Cross-Domain Intelligence", "query": "Provide a comprehensive security audit of our authentication system including code review, vulnerability assessment, and recommendations based on recent discussions and issues.", "complexity": "complex", "sophistication": "advanced", "expected_behavior": {"complexity_scores": {"semantic_complexity": 0.8, "domain_complexity": 0.7, "temporal_complexity": 0.6, "structural_complexity": 0.7, "contextual_complexity": 0.8}, "strategy_decision": "cross_domain_with_files", "domains": ["github_code", "documentation", "slack_engineering", "github_issues"], "fusion_strategy": "comprehensive"}}, {"id": "CA004", "name": "Adaptive Query with Ultimate Intelligence", "query": "I need a complete analysis of our system architecture, performance bottlenecks, security posture, development practices, and a roadmap for improvements with specific implementation priorities.", "complexity": "adaptive", "sophistication": "expert", "expected_behavior": {"strategy_orchestration": "ultimate_search", "adaptive_selection": true, "quality_monitoring": true, "performance_prediction": true, "fallback_strategies": true}}]}, "strategy_orchestration": {"description": "Test LLM-driven strategy orchestration and decision making", "scenarios": [{"id": "SO001", "name": "File Retrieval Decision", "query": "Show me the complete authentication service implementation", "expected_orchestration": {"primary_strategy": "files_via_content", "reasoning": "Query requests complete implementation, requiring full file content", "domains": ["github_code"], "confidence": 0.85}}, {"id": "SO002", "name": "Cross-Domain Routing Decision", "query": "What has been discussed about API rate limiting and what changes were made?", "expected_orchestration": {"primary_strategy": "cross_domain", "reasoning": "Query requires both discussions and code changes across domains", "domains": ["slack_engineering", "slack_support", "github_code"], "fusion_strategy": "problem_resolution_merge"}}, {"id": "SO003", "name": "Adaptive Strategy Selection", "query": "Comprehensive review of our development workflow efficiency", "expected_orchestration": {"strategy_type": "adaptive", "reasoning": "Complex analysis requiring ultimate intelligence features", "enable_quality_monitoring": true, "enable_adaptive_improvement": true}}]}, "ultimate_search": {"description": "Test ultimate search capabilities with full intelligence", "scenarios": [{"id": "US001", "name": "Ultimate Intelligence with Quality Monitoring", "query": "Analyze our authentication security posture and provide actionable recommendations", "sophistication": "expert", "expected_features": {"llm_orchestration": true, "quality_monitoring": true, "adaptive_improvement": true, "performance_prediction": true}, "quality_requirements": {"result_quality_score": 0.85, "intelligence_level": "ultimate", "strategy_confidence": 0.8}}, {"id": "US002", "name": "Multi-Stage Analysis with Fallbacks", "query": "Complete technical due diligence of our codebase, architecture, and development practices", "sophistication": "expert", "expected_features": {"multi_stage_analysis": true, "fallback_strategies": ["cross_domain", "agentic", "standard"], "comprehensive_fusion": true, "domain_expertise_detection": true}, "validation_criteria": {"covers_multiple_aspects": true, "provides_specific_metrics": true, "includes_improvement_roadmap": true, "demonstrates_deep_analysis": true}}]}, "quality_monitoring": {"description": "Test real-time quality analysis and adaptive improvement", "scenarios": [{"id": "QM001", "name": "Quality Score Calculation", "query": "How do we handle user authentication?", "sophistication": "advanced", "quality_expectations": {"result_count_appropriateness": 0.8, "score_distribution": 0.85, "domain_diversity": 0.7, "strategy_confidence_alignment": 0.8, "execution_performance": 0.9}}, {"id": "QM002", "name": "Adaptive Improvement Trigger", "query": "Complex deployment architecture analysis", "sophistication": "advanced", "expected_behavior": {"initial_quality_threshold": 0.6, "trigger_adaptive_improvement": true, "escalate_to_expert": true, "update_learning_metrics": true}}]}, "edge_cases_advanced": {"description": "Advanced edge cases and failure modes", "scenarios": [{"id": "EC_ADV001", "name": "Extremely Broad Query with Intelligence", "query": "Tell me everything about everything in our system", "sophistication": "expert", "expected_behavior": {"strategy": "seek_clarification", "provide_overview": true, "suggest_specific_questions": true, "avoid_overwhelming_response": true}}, {"id": "EC_ADV002", "name": "Highly Technical Query with Context", "query": "Analyze the cryptographic security of our JWT implementation including potential timing attacks, key management issues, and algorithm weaknesses", "sophistication": "expert", "expected_behavior": {"deep_technical_analysis": true, "security_focused_routing": true, "expert_level_response": true, "specific_security_recommendations": true}}, {"id": "EC_ADV003", "name": "Ambiguous Query with Context Resolution", "query": "The thing isn't working", "sophistication": "advanced", "expected_behavior": {"seek_clarification": true, "provide_troubleshooting_framework": true, "suggest_specific_diagnostic_steps": true, "use_context_if_available": true}}]}, "conversation_flow_advanced": {"description": "Advanced multi-turn conversation testing", "conversations": [{"id": "CF_ADV001", "title": "Deep Technical Investigation", "turns": [{"turn": 1, "query": "We're seeing performance issues with user authentication", "sophistication": "advanced", "expected_strategy": "cross_domain", "expected_domains": ["slack_support", "github_issues", "slack_engineering"]}, {"turn": 2, "query": "What specific changes were made to the authentication service in the last 30 days?", "sophistication": "advanced", "expected_strategy": "cross_domain_with_temporal", "context_awareness": true, "temporal_filtering": true}, {"turn": 3, "query": "Show me the exact code changes and their performance impact", "sophistication": "expert", "expected_strategy": "files_via_content_with_analysis", "deep_context_usage": true, "code_analysis": true}, {"turn": 4, "query": "Provide specific optimization recommendations", "sophistication": "expert", "expected_strategy": "ultimate_search", "synthesis_of_conversation": true, "actionable_recommendations": true}]}]}, "performance_stress_tests": {"description": "Performance and stress testing scenarios", "scenarios": [{"id": "PST001", "name": "High Complexity Query Performance", "query": "Comprehensive analysis of all security aspects across our entire system", "sophistication": "expert", "performance_requirements": {"max_latency_ms": 5000, "memory_usage_mb": 1024, "cpu_usage_percent": 70}}, {"id": "PST002", "name": "Concurrent Ultimate Search Load", "concurrent_queries": 10, "query_template": "Analyze our {domain} system and provide optimization recommendations", "domains": ["authentication", "deployment", "database", "api", "security"], "sophistication": "expert", "performance_requirements": {"avg_latency_ms": 3000, "throughput_qps": 5}}]}}}