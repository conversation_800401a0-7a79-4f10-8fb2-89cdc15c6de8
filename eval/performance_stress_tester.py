#!/usr/bin/env python3
"""
Performance Stress Testing Framework for Ultimate Agentic RAG System

This script provides comprehensive performance testing including:
- Load testing with concurrent users
- Latency testing across sophistication levels  
- Memory and CPU stress testing
- Adaptive improvement under load
- Failure scenario testing
"""

import asyncio
import time
import json
import logging
import psutil
import statistics
import threading
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from concurrent.futures import ThreadPoolExecutor, as_completed
import sys

# Add the project root to Python path
sys.path.append(str(Path(__file__).parent.parent.parent))

from apps.search.services.simplified_search_service import SearchService
from apps.core.llama_index_manager import LlamaIndexManager
from apps.core.retrieval import SophisticationLevel

@dataclass
class PerformanceTestResult:
    """Performance test result with detailed metrics."""
    test_name: str
    concurrent_users: int
    total_requests: int
    successful_requests: int
    failed_requests: int
    
    # Latency metrics (ms)
    avg_latency: float
    min_latency: float
    max_latency: float
    p50_latency: float
    p95_latency: float
    p99_latency: float
    
    # Throughput metrics
    requests_per_second: float
    
    # System resource metrics
    avg_memory_usage_mb: float
    peak_memory_usage_mb: float
    avg_cpu_usage_percent: float
    peak_cpu_usage_percent: float
    
    # Quality metrics
    avg_quality_score: float
    min_quality_score: float
    quality_consistency: float  # variance in quality scores
    
    # Test metadata
    test_duration_seconds: float
    timestamp: str
    test_config: Dict[str, Any]

class PerformanceStressTester:
    """Comprehensive performance and stress tester."""
    
    def __init__(self, tenant_slug: str = "stress-test-tenant"):
        self.tenant_slug = tenant_slug
        self.search_service = SearchService(tenant_slug)
        self.llama_manager = LlamaIndexManager(tenant_slug)
        
        # Set up logging
        self._setup_logging()
        
        # Test configurations
        self.test_configs = {
            "basic_load": {
                "concurrent_users": [1, 5, 10],
                "duration_seconds": 60,
                "sophistication": "basic",
                "query_type": "simple"
            },
            "advanced_load": {
                "concurrent_users": [1, 3, 5],
                "duration_seconds": 120,
                "sophistication": "advanced",
                "query_type": "complex"
            },
            "expert_stress": {
                "concurrent_users": [1, 2, 3],
                "duration_seconds": 180,
                "sophistication": "expert",
                "query_type": "ultimate"
            },
            "mixed_workload": {
                "concurrent_users": [5, 10, 15],
                "duration_seconds": 300,
                "sophistication": "mixed",
                "query_type": "varied"
            }
        }
        
        # Test queries for different complexity levels
        self.test_queries = {
            "simple": [
                "authentication",
                "config file",
                "README",
                "database setup",
                "API documentation"
            ],
            "complex": [
                "How does our authentication system handle security threats?",
                "Analyze our deployment process and identify bottlenecks",
                "What are the recent performance optimization discussions?",
                "Review our API rate limiting implementation and customer feedback",
                "Investigate database performance issues and proposed solutions"
            ],
            "ultimate": [
                "Conduct a comprehensive security audit of our authentication system including code analysis, vulnerability assessment, recent discussions, and provide prioritized recommendations",
                "Analyze our entire system architecture, identify scalability issues, review performance optimizations, and create a strategic improvement roadmap",
                "Investigate recent payment processing failures, correlate with code changes and infrastructure modifications, and provide immediate fixes and prevention measures",
                "Perform a complete technology stack assessment covering security, performance, development practices, and business alignment with detailed ROI analysis"
            ],
            "varied": [
                "config.yaml",
                "How does authentication work?",
                "Analyze our deployment security and recent issues",
                "Comprehensive audit of our API security posture with industry comparison and specific recommendations",
                "database connection",
                "What performance problems have been discussed recently?",
                "Create a strategic technology roadmap for the next quarter based on current challenges and opportunities"
            ]
        }
        
        # System monitoring
        self.system_metrics = []
        self.monitoring_active = False
    
    def _setup_logging(self):
        """Set up performance testing logging."""
        log_dir = Path(__file__).parent / "results" / "performance_logs"
        log_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = log_dir / f"performance_test_{timestamp}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger("PerformanceStressTester")
        self.logger.info(f"Performance testing logging initialized: {log_file}")
    
    async def run_comprehensive_performance_tests(self) -> List[PerformanceTestResult]:
        """Run all performance test configurations."""
        self.logger.info("Starting comprehensive performance stress testing")
        
        all_results = []
        
        for test_name, config in self.test_configs.items():
            self.logger.info(f"Running performance test: {test_name}")
            
            for concurrent_users in config["concurrent_users"]:
                self.logger.info(f"Testing with {concurrent_users} concurrent users")
                
                result = await self._run_performance_test(
                    test_name=f"{test_name}_{concurrent_users}_users",
                    concurrent_users=concurrent_users,
                    duration_seconds=config["duration_seconds"],
                    sophistication=config["sophistication"],
                    query_type=config["query_type"]
                )
                
                all_results.append(result)
                
                # Brief pause between tests
                await asyncio.sleep(5)
        
        # Save results
        self._save_performance_results(all_results)
        
        # Generate performance report
        self._generate_performance_report(all_results)
        
        return all_results
    
    async def _run_performance_test(self, 
                                  test_name: str,
                                  concurrent_users: int,
                                  duration_seconds: int,
                                  sophistication: str,
                                  query_type: str) -> PerformanceTestResult:
        """Run a single performance test configuration."""
        
        # Start system monitoring
        self._start_system_monitoring()
        
        # Test setup
        start_time = time.time()
        end_time = start_time + duration_seconds
        
        # Results tracking
        latencies = []
        quality_scores = []
        successful_requests = 0
        failed_requests = 0
        
        # Create semaphore for concurrency control
        semaphore = asyncio.Semaphore(concurrent_users)
        
        # Track active tasks
        active_tasks = []
        
        try:
            # Run requests for the specified duration
            while time.time() < end_time:
                # Launch concurrent requests up to the limit
                while len(active_tasks) < concurrent_users and time.time() < end_time:
                    task = asyncio.create_task(
                        self._execute_single_request(semaphore, sophistication, query_type)
                    )
                    active_tasks.append(task)
                
                # Check for completed tasks
                if active_tasks:
                    done, active_tasks = await asyncio.wait(
                        active_tasks, 
                        timeout=0.1, 
                        return_when=asyncio.FIRST_COMPLETED
                    )
                    
                    # Process completed tasks
                    for task in done:
                        try:
                            latency, quality_score = await task
                            latencies.append(latency)
                            quality_scores.append(quality_score)
                            successful_requests += 1
                        except Exception as e:
                            self.logger.error(f"Request failed: {e}")
                            failed_requests += 1
                
                # Small delay to prevent tight loop
                await asyncio.sleep(0.01)
            
            # Wait for remaining tasks to complete
            if active_tasks:
                await asyncio.gather(*active_tasks, return_exceptions=True)
        
        finally:
            # Stop system monitoring
            self._stop_system_monitoring()
        
        # Calculate metrics
        test_duration = time.time() - start_time
        total_requests = successful_requests + failed_requests
        
        if latencies:
            avg_latency = statistics.mean(latencies)
            min_latency = min(latencies)
            max_latency = max(latencies)
            
            # Calculate percentiles
            sorted_latencies = sorted(latencies)
            p50_latency = sorted_latencies[len(sorted_latencies) // 2]
            p95_latency = sorted_latencies[int(len(sorted_latencies) * 0.95)]
            p99_latency = sorted_latencies[int(len(sorted_latencies) * 0.99)]
        else:
            avg_latency = min_latency = max_latency = 0
            p50_latency = p95_latency = p99_latency = 0
        
        # Calculate quality metrics
        if quality_scores:
            avg_quality_score = statistics.mean(quality_scores)
            min_quality_score = min(quality_scores)
            quality_consistency = 1.0 - (statistics.pstdev(quality_scores) / max(avg_quality_score, 0.1))
        else:
            avg_quality_score = min_quality_score = quality_consistency = 0
        
        # Calculate throughput
        requests_per_second = total_requests / test_duration
        
        # System resource metrics
        memory_usages = [m['memory_mb'] for m in self.system_metrics]
        cpu_usages = [m['cpu_percent'] for m in self.system_metrics]
        
        avg_memory_usage = statistics.mean(memory_usages) if memory_usages else 0
        peak_memory_usage = max(memory_usages) if memory_usages else 0
        avg_cpu_usage = statistics.mean(cpu_usages) if cpu_usages else 0
        peak_cpu_usage = max(cpu_usages) if cpu_usages else 0
        
        return PerformanceTestResult(
            test_name=test_name,
            concurrent_users=concurrent_users,
            total_requests=total_requests,
            successful_requests=successful_requests,
            failed_requests=failed_requests,
            avg_latency=avg_latency,
            min_latency=min_latency,
            max_latency=max_latency,
            p50_latency=p50_latency,
            p95_latency=p95_latency,
            p99_latency=p99_latency,
            requests_per_second=requests_per_second,
            avg_memory_usage_mb=avg_memory_usage,
            peak_memory_usage_mb=peak_memory_usage,
            avg_cpu_usage_percent=avg_cpu_usage,
            peak_cpu_usage_percent=peak_cpu_usage,
            avg_quality_score=avg_quality_score,
            min_quality_score=min_quality_score,
            quality_consistency=quality_consistency,
            test_duration_seconds=test_duration,
            timestamp=datetime.now(timezone.utc).isoformat(),
            test_config={
                "concurrent_users": concurrent_users,
                "duration_seconds": duration_seconds,
                "sophistication": sophistication,
                "query_type": query_type
            }
        )
    
    async def _execute_single_request(self, 
                                    semaphore: asyncio.Semaphore,
                                    sophistication: str, 
                                    query_type: str) -> Tuple[float, float]:
        """Execute a single request and return latency and quality score."""
        async with semaphore:
            start_time = time.time()
            
            try:
                # Select query
                query = self._select_query(query_type)
                
                # Execute based on sophistication level
                if sophistication == "basic":
                    response = self.search_service.search(query)
                elif sophistication == "standard":
                    response = self.search_service.agentic_search(
                        query, SophisticationLevel.STANDARD
                    )
                elif sophistication == "advanced":
                    response = self.search_service.agentic_search(
                        query, SophisticationLevel.ADVANCED
                    )
                elif sophistication == "expert":
                    response = self.llama_manager.ultimate_search(
                        query, sophistication=SophisticationLevel.EXPERT
                    )
                elif sophistication == "mixed":
                    # Randomly select sophistication level
                    import random
                    soph_level = random.choice([
                        SophisticationLevel.BASIC,
                        SophisticationLevel.STANDARD, 
                        SophisticationLevel.ADVANCED,
                        SophisticationLevel.EXPERT
                    ])
                    if soph_level == SophisticationLevel.EXPERT:
                        response = self.llama_manager.ultimate_search(query, sophistication=soph_level)
                    else:
                        response = self.search_service.agentic_search(query, soph_level)
                
                end_time = time.time()
                latency = (end_time - start_time) * 1000  # Convert to milliseconds
                
                # Estimate quality score (simplified)
                answer_length = len(response.get('answer', ''))
                citation_count = len(response.get('citations', []))
                has_error = 'error' in response.get('metadata', {})
                
                if has_error:
                    quality_score = 0.1
                else:
                    quality_score = min(1.0, (answer_length / 500) * 0.7 + (citation_count / 5) * 0.3)
                
                return latency, quality_score
                
            except Exception as e:
                end_time = time.time()
                latency = (end_time - start_time) * 1000
                self.logger.error(f"Request failed: {e}")
                return latency, 0.0
    
    def _select_query(self, query_type: str) -> str:
        """Select a query based on the test type."""
        import random
        queries = self.test_queries.get(query_type, ["test query"])
        return random.choice(queries)
    
    def _start_system_monitoring(self):
        """Start system resource monitoring."""
        self.system_metrics = []
        self.monitoring_active = True
        
        def monitor_resources():
            while self.monitoring_active:
                try:
                    memory_info = psutil.virtual_memory()
                    cpu_percent = psutil.cpu_percent(interval=1)
                    
                    self.system_metrics.append({
                        'timestamp': time.time(),
                        'memory_mb': memory_info.used / 1024 / 1024,
                        'memory_percent': memory_info.percent,
                        'cpu_percent': cpu_percent
                    })
                except Exception as e:
                    self.logger.error(f"System monitoring error: {e}")
                
                time.sleep(1)
        
        self.monitor_thread = threading.Thread(target=monitor_resources, daemon=True)
        self.monitor_thread.start()
    
    def _stop_system_monitoring(self):
        """Stop system resource monitoring."""
        self.monitoring_active = False
    
    def _save_performance_results(self, results: List[PerformanceTestResult]):
        """Save performance test results."""
        results_dir = Path(__file__).parent / "results" / "performance"
        results_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = results_dir / f"performance_results_{timestamp}.json"
        
        with open(results_file, 'w') as f:
            json.dump([asdict(result) for result in results], f, indent=2, default=str)
        
        self.logger.info(f"Performance results saved: {results_file}")
    
    def _generate_performance_report(self, results: List[PerformanceTestResult]):
        """Generate performance test report."""
        results_dir = Path(__file__).parent / "results" / "performance"
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = results_dir / f"performance_report_{timestamp}.md"
        
        with open(report_file, 'w') as f:
            f.write("# Ultimate Agentic RAG - Performance Test Report\n\n")
            f.write(f"**Test Date:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("## Executive Summary\n\n")
            
            # Overall metrics
            total_requests = sum(r.total_requests for r in results)
            total_successful = sum(r.successful_requests for r in results)
            overall_success_rate = total_successful / total_requests if total_requests > 0 else 0
            
            f.write(f"- **Total Requests:** {total_requests:,}\n")
            f.write(f"- **Success Rate:** {overall_success_rate:.1%}\n")
            f.write(f"- **Test Configurations:** {len(results)}\n\n")
            
            f.write("## Detailed Results\n\n")
            
            for result in results:
                f.write(f"### {result.test_name}\n\n")
                f.write(f"**Configuration:**\n")
                f.write(f"- Concurrent Users: {result.concurrent_users}\n")
                f.write(f"- Duration: {result.test_duration_seconds:.1f}s\n")
                f.write(f"- Sophistication: {result.test_config.get('sophistication', 'unknown')}\n\n")
                
                f.write(f"**Performance Metrics:**\n")
                f.write(f"- Total Requests: {result.total_requests}\n")
                f.write(f"- Success Rate: {result.successful_requests/result.total_requests:.1%}\n")
                f.write(f"- Throughput: {result.requests_per_second:.2f} req/s\n")
                f.write(f"- Average Latency: {result.avg_latency:.0f}ms\n")
                f.write(f"- P95 Latency: {result.p95_latency:.0f}ms\n")
                f.write(f"- P99 Latency: {result.p99_latency:.0f}ms\n\n")
                
                f.write(f"**Quality Metrics:**\n")
                f.write(f"- Average Quality Score: {result.avg_quality_score:.3f}\n")
                f.write(f"- Minimum Quality Score: {result.min_quality_score:.3f}\n")
                f.write(f"- Quality Consistency: {result.quality_consistency:.3f}\n\n")
                
                f.write(f"**System Resources:**\n")
                f.write(f"- Average Memory Usage: {result.avg_memory_usage_mb:.1f}MB\n")
                f.write(f"- Peak Memory Usage: {result.peak_memory_usage_mb:.1f}MB\n")
                f.write(f"- Average CPU Usage: {result.avg_cpu_usage_percent:.1f}%\n")
                f.write(f"- Peak CPU Usage: {result.peak_cpu_usage_percent:.1f}%\n\n")
                
                # Performance assessment
                f.write(f"**Assessment:**\n")
                if result.avg_latency < 1000 and result.avg_quality_score > 0.7:
                    f.write("✅ Excellent performance\n")
                elif result.avg_latency < 3000 and result.avg_quality_score > 0.6:
                    f.write("✅ Good performance\n")
                elif result.avg_latency < 5000:
                    f.write("⚠️ Acceptable performance\n")
                else:
                    f.write("❌ Performance needs improvement\n")
                
                f.write("\n---\n\n")
        
        self.logger.info(f"Performance report generated: {report_file}")

async def main():
    """Main performance testing runner."""
    print("🚀 Starting Ultimate Agentic RAG Performance Stress Testing")
    
    tester = PerformanceStressTester()
    
    try:
        results = await tester.run_comprehensive_performance_tests()
        
        print(f"\n✅ Performance Testing Complete!")
        print(f"📊 Test Configurations: {len(results)}")
        
        # Summary statistics
        total_requests = sum(r.total_requests for r in results)
        total_successful = sum(r.successful_requests for r in results)
        success_rate = total_successful / total_requests if total_requests > 0 else 0
        
        avg_latency = statistics.mean([r.avg_latency for r in results])
        avg_quality = statistics.mean([r.avg_quality_score for r in results])
        max_throughput = max([r.requests_per_second for r in results])
        
        print(f"📈 Overall Statistics:")
        print(f"   - Total Requests: {total_requests:,}")
        print(f"   - Success Rate: {success_rate:.1%}")
        print(f"   - Average Latency: {avg_latency:.0f}ms")
        print(f"   - Average Quality: {avg_quality:.3f}")
        print(f"   - Max Throughput: {max_throughput:.1f} req/s")
        
        # Performance assessment
        if success_rate >= 0.95 and avg_latency <= 2000 and avg_quality >= 0.7:
            print("🏆 Outstanding performance across all test scenarios!")
            return 0
        elif success_rate >= 0.9 and avg_latency <= 3000 and avg_quality >= 0.6:
            print("✅ Good performance with room for optimization")
            return 0
        else:
            print("⚠️ Performance improvements needed")
            return 1
        
    except Exception as e:
        print(f"❌ Performance testing failed: {e}")
        import traceback
        print(traceback.format_exc())
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
