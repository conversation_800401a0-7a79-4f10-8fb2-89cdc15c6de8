#!/usr/bin/env python3
"""
Ultimate RAG Evaluation Test Runner

Simple interface for running different types of evaluations on the Ultimate Agentic RAG system.
This script provides easy access to all evaluation capabilities.
"""

import asyncio
import argparse
import sys
import time
from pathlib import Path
from typing import List, Optional

# Add the project root to Python path
sys.path.append(str(Path(__file__).parent.parent.parent))

from comprehensive_evaluator import UltimateRAGEvaluator
from performance_stress_tester import PerformanceStressTester
from cicd_integration import CICDEvaluationRunner

def print_banner():
    """Print evaluation framework banner."""
    print("""
╔══════════════════════════════════════════════════════════════════════════════╗
║                     Ultimate Agentic RAG Evaluation Framework               ║
║                                                                              ║
║  🧠 Test all sophistication levels: Basic → Standard → Advanced → Expert    ║
║  🚀 Validate ultimate intelligence features and LLM orchestration           ║
║  📊 Comprehensive performance and stress testing                            ║
║  🔍 Business scenario validation and quality assessment                     ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """)

async def run_quick_test():
    """Run a quick smoke test to validate basic functionality."""
    print("🚀 Running Quick Smoke Test...")
    print("   Testing basic functionality across sophistication levels\n")
    
    evaluator = UltimateRAGEvaluator()
    
    # Quick test queries
    test_queries = [
        {"query": "authentication", "sophistication": "basic", "expected_time": 500},
        {"query": "How does our authentication work?", "sophistication": "standard", "expected_time": 1500},
        {"query": "Analyze our security posture and recent discussions", "sophistication": "advanced", "expected_time": 3000},
        {"query": "Comprehensive security audit with recommendations", "sophistication": "expert", "expected_time": 5000}
    ]
    
    results = []
    for i, test in enumerate(test_queries, 1):
        print(f"   [{i}/4] Testing {test['sophistication']} level...")
        
        try:
            start_time = time.time()
            
            # Execute based on sophistication
            if test['sophistication'] == 'basic':
                response = evaluator.search_service.search(test['query'])
            else:
                from apps.core.retrieval import SophisticationLevel
                soph_level = getattr(SophisticationLevel, test['sophistication'].upper())
                response = evaluator.search_service.agentic_search(test['query'], soph_level)
            
            end_time = time.time()
            latency = (end_time - start_time) * 1000
            
            # Basic quality checks
            answer_length = len(response.get('answer', ''))
            citation_count = len(response.get('citations', []))
            has_error = 'error' in response.get('metadata', {})
            
            passed = (
                not has_error and 
                answer_length > 20 and 
                citation_count > 0 and 
                latency < test['expected_time']
            )
            
            status = "✅ PASS" if passed else "❌ FAIL"
            print(f"      {status} - {latency:.0f}ms - {answer_length} chars - {citation_count} citations")
            
            results.append({
                'sophistication': test['sophistication'],
                'passed': passed,
                'latency_ms': latency,
                'answer_length': answer_length,
                'citation_count': citation_count
            })
            
        except Exception as e:
            print(f"      ❌ FAIL - Error: {str(e)}")
            results.append({
                'sophistication': test['sophistication'],
                'passed': False,
                'error': str(e)
            })
    
    # Summary
    passed_tests = sum(1 for r in results if r.get('passed', False))
    total_tests = len(results)
    
    print(f"\n📊 Quick Test Results: {passed_tests}/{total_tests} passed ({passed_tests/total_tests:.1%})")
    
    if passed_tests == total_tests:
        print("🎉 All sophistication levels working correctly!")
        return True
    else:
        print("⚠️  Some tests failed - check system configuration")
        return False

async def run_comprehensive_evaluation(categories: Optional[List[str]] = None):
    """Run comprehensive evaluation across all test categories."""
    print("🔬 Running Comprehensive Evaluation...")
    print("   Testing all intelligence features and sophistication levels\n")
    
    evaluator = UltimateRAGEvaluator()
    
    try:
        report = await evaluator.run_comprehensive_evaluation(categories)
        
        print("✅ Comprehensive Evaluation Complete!\n")
        print("📊 Summary Results:")
        print(f"   • Total Tests: {report.total_tests}")
        print(f"   • Pass Rate: {report.pass_rate:.1%}")
        print(f"   • Avg Quality: {report.avg_quality_score:.3f}")
        print(f"   • Avg Latency: {report.avg_response_time_ms:.0f}ms")
        print(f"   • Intelligence Metrics:")
        print(f"     - Strategy Accuracy: {report.strategy_accuracy:.1%}")
        print(f"     - Domain Precision: {report.domain_routing_precision:.1%}")
        
        print(f"\n📈 Sophistication Level Performance:")
        for level, metrics in report.sophistication_results.items():
            print(f"   • {level.title()}: {metrics['pass_rate']:.1%} pass rate, {metrics['avg_response_time_ms']:.0f}ms avg")
        
        return report.pass_rate >= 0.8
        
    except Exception as e:
        print(f"❌ Comprehensive evaluation failed: {e}")
        return False

async def run_performance_test(quick: bool = False):
    """Run performance stress testing."""
    if quick:
        print("⚡ Running Quick Performance Test...")
        print("   Testing basic performance with limited load\n")
    else:
        print("🚀 Running Comprehensive Performance Test...")
        print("   Testing performance under various load conditions\n")
    
    tester = PerformanceStressTester()
    
    try:
        if quick:
            # Quick performance test - single configuration
            result = await tester._run_performance_test(
                test_name="quick_performance",
                concurrent_users=3,
                duration_seconds=30,
                sophistication="standard",
                query_type="simple"
            )
            
            print("✅ Quick Performance Test Complete!\n")
            print("📊 Performance Results:")
            print(f"   • Requests: {result.total_requests}")
            print(f"   • Success Rate: {result.successful_requests/result.total_requests:.1%}")
            print(f"   • Avg Latency: {result.avg_latency:.0f}ms")
            print(f"   • Throughput: {result.requests_per_second:.1f} req/s")
            print(f"   • Quality Score: {result.avg_quality_score:.3f}")
            
            return result.avg_latency < 2000 and result.avg_quality_score > 0.6
            
        else:
            # Comprehensive performance testing
            results = await tester.run_comprehensive_performance_tests()
            
            print("✅ Comprehensive Performance Testing Complete!\n")
            
            # Calculate summary metrics
            import statistics
            total_requests = sum(r.total_requests for r in results)
            avg_success_rate = statistics.mean([r.successful_requests/r.total_requests for r in results])
            avg_latency = statistics.mean([r.avg_latency for r in results])
            max_throughput = max([r.requests_per_second for r in results])
            
            print("📊 Performance Summary:")
            print(f"   • Test Configurations: {len(results)}")
            print(f"   • Total Requests: {total_requests:,}")
            print(f"   • Avg Success Rate: {avg_success_rate:.1%}")
            print(f"   • Avg Latency: {avg_latency:.0f}ms")
            print(f"   • Max Throughput: {max_throughput:.1f} req/s")
            
            return avg_success_rate >= 0.9 and avg_latency <= 3000
            
    except Exception as e:
        print(f"❌ Performance testing failed: {e}")
        return False

async def run_business_scenarios():
    """Run business scenario validation."""
    print("💼 Running Business Scenario Validation...")
    print("   Testing real-world enterprise use cases\n")
    
    evaluator = UltimateRAGEvaluator()
    
    # Business scenario queries
    business_queries = [
        {
            "scenario": "Security Audit Preparation",
            "query": "We have a SOC 2 audit next week. Review our security controls, identify gaps, and provide remediation priorities.",
            "sophistication": "expert"
        },
        {
            "scenario": "Customer Churn Risk",
            "query": "Enterprise customer is threatening to churn due to reliability issues. Analyze their experience and create retention strategy.",
            "sophistication": "expert"
        },
        {
            "scenario": "Performance Crisis",
            "query": "System performance degraded 40% last month. Conduct root cause analysis and provide optimization recommendations.",
            "sophistication": "expert"
        },
        {
            "scenario": "Technology Strategy",
            "query": "Analyze our technology stack and create strategic roadmap for next 12 months with ROI analysis.",
            "sophistication": "expert"
        }
    ]
    
    results = []
    for i, scenario in enumerate(business_queries, 1):
        print(f"   [{i}/4] Testing {scenario['scenario']}...")
        
        try:
            from apps.core.retrieval import SophisticationLevel
            
            start_time = time.time()
            response = evaluator.llama_manager.ultimate_search(
                query=scenario['query'],
                sophistication=SophisticationLevel.EXPERT
            )
            end_time = time.time()
            
            latency = (end_time - start_time) * 1000
            answer_length = len(response.get('answer', ''))
            
            # Business value assessment
            business_keywords = ['recommend', 'strategy', 'action', 'roi', 'risk', 'priority']
            business_score = sum(1 for word in business_keywords if word in response.get('answer', '').lower())
            
            passed = (
                answer_length > 200 and
                business_score >= 3 and
                latency < 10000
            )
            
            status = "✅ PASS" if passed else "❌ FAIL"
            print(f"      {status} - {latency:.0f}ms - Business value: {business_score}/6")
            
            results.append({
                'scenario': scenario['scenario'],
                'passed': passed,
                'latency_ms': latency,
                'business_score': business_score
            })
            
        except Exception as e:
            print(f"      ❌ FAIL - Error: {str(e)}")
            results.append({
                'scenario': scenario['scenario'],
                'passed': False,
                'error': str(e)
            })
    
    # Summary
    passed_scenarios = sum(1 for r in results if r.get('passed', False))
    total_scenarios = len(results)
    
    print(f"\n📊 Business Scenario Results: {passed_scenarios}/{total_scenarios} passed ({passed_scenarios/total_scenarios:.1%})")
    
    if passed_scenarios >= 3:
        print("🏆 Business scenarios demonstrate enterprise readiness!")
        return True
    else:
        print("📈 Business scenario performance needs improvement")
        return False

async def run_cicd_simulation(test_type: str = "smoke"):
    """Run CI/CD pipeline simulation."""
    print(f"🔄 Running CI/CD Pipeline Simulation ({test_type})...")
    print("   Simulating automated deployment validation\n")
    
    runner = CICDEvaluationRunner()
    
    try:
        success, results = await runner.run_cicd_evaluation(test_type)
        
        print("✅ CI/CD Simulation Complete!\n")
        
        if success:
            print("🎉 CI/CD Pipeline: PASSED")
            print("   System ready for deployment!")
        else:
            print("❌ CI/CD Pipeline: FAILED")
            print("   Quality gates not met - deployment blocked")
        
        return success
        
    except Exception as e:
        print(f"❌ CI/CD simulation failed: {e}")
        return False

def print_help():
    """Print detailed help information."""
    print("""
🔧 Ultimate RAG Evaluation Framework - Usage Guide

AVAILABLE TEST TYPES:

1. 🚀 quick
   Fast smoke test to validate basic functionality across all sophistication levels.
   Duration: ~2 minutes

2. 🔬 comprehensive  
   Complete evaluation of all features, intelligence capabilities, and test scenarios.
   Duration: ~15-30 minutes

3. ⚡ performance-quick
   Quick performance test with limited load to check basic performance metrics.
   Duration: ~2 minutes

4. 🚀 performance-full
   Comprehensive performance testing under various load conditions and stress scenarios.
   Duration: ~20-45 minutes

5. 💼 business
   Validation of real-world enterprise business scenarios and use cases.
   Duration: ~10 minutes

6. 🔄 cicd-smoke, cicd-full, cicd-performance, cicd-regression
   CI/CD pipeline simulations for different deployment validation scenarios.
   Duration: 2-30 minutes depending on type

EXAMPLES:

  # Quick validation (recommended for development)
  python test_runner.py quick

  # Full system validation (recommended for releases)  
  python test_runner.py comprehensive

  # Performance validation
  python test_runner.py performance-quick
  python test_runner.py performance-full

  # Business readiness validation
  python test_runner.py business

  # CI/CD pipeline simulation
  python test_runner.py cicd-smoke
  python test_runner.py cicd-full

CONFIGURATION:

The evaluation framework can be configured through:
- eval/configs/evaluation_config.yaml (main configuration)
- Environment variables for CI/CD integration
- Command line arguments for specific test runs

RESULTS:

All test results are saved in the eval/results/ directory with timestamps:
- Detailed JSON reports for analysis
- Executive summaries in Markdown
- CSV files for data analysis
- Performance metrics and trends

For more information, see the documentation in eval/README.md
    """)

async def main():
    """Main test runner function."""
    parser = argparse.ArgumentParser(
        description="Ultimate RAG Evaluation Test Runner",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="Run 'python test_runner.py help' for detailed usage guide"
    )
    
    parser.add_argument(
        "test_type",
        nargs='?',
        choices=[
            "quick", "comprehensive", "performance-quick", "performance-full",
            "business", "cicd-smoke", "cicd-full", "cicd-performance", 
            "cicd-regression", "help"
        ],
        default="help",
        help="Type of evaluation to run"
    )
    
    parser.add_argument(
        "--categories",
        nargs="+",
        help="Specific test categories to run (for comprehensive tests)"
    )
    
    args = parser.parse_args()
    
    if args.test_type == "help":
        print_help()
        return 0
    
    print_banner()
    
    start_time = time.time()
    success = False
    
    try:
        if args.test_type == "quick":
            success = await run_quick_test()
            
        elif args.test_type == "comprehensive":
            success = await run_comprehensive_evaluation(args.categories)
            
        elif args.test_type == "performance-quick":
            success = await run_performance_test(quick=True)
            
        elif args.test_type == "performance-full":
            success = await run_performance_test(quick=False)
            
        elif args.test_type == "business":
            success = await run_business_scenarios()
            
        elif args.test_type.startswith("cicd-"):
            cicd_type = args.test_type.replace("cicd-", "")
            success = await run_cicd_simulation(cicd_type)
            
        else:
            print(f"❌ Unknown test type: {args.test_type}")
            return 1
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n⏱️  Total Duration: {duration:.1f} seconds")
        
        if success:
            print("🎉 All tests completed successfully!")
            print("✅ System performance meets requirements")
            return 0
        else:
            print("⚠️  Some tests failed")
            print("📋 Check detailed reports in eval/results/ for analysis")
            return 1
            
    except KeyboardInterrupt:
        print("\n⚠️  Tests interrupted by user")
        return 1
        
    except Exception as e:
        print(f"\n❌ Test execution failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
