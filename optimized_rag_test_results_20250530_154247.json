{"timestamp": "2025-05-30T15:42:47.722556", "tenant": "stride", "summary": {"total_tests": 12, "passed_tests": 0, "success_rate": 0.0}, "detailed_results": [{"category": "Core Functionality", "name": "Basic Search", "query": "authentication", "passed": false, "response_time": 4.7811667919158936, "result_count": 0, "details": "Search failed: apps.core.llama_index_manager.LlamaIndexManager.search() got multiple values for keyword argument 'top_k'", "timestamp": "2025-05-30T15:42:46.625811"}, {"category": "Core Functionality", "name": "Technical Query", "query": "API endpoint configuration", "passed": false, "response_time": 0.09798097610473633, "result_count": 0, "details": "Search failed: apps.core.llama_index_manager.LlamaIndexManager.search() got multiple values for keyword argument 'top_k'", "timestamp": "2025-05-30T15:42:46.723819"}, {"category": "Core Functionality", "name": "Complex Query", "query": "How do we handle user authentication in our system?", "passed": false, "response_time": 0.10296821594238281, "result_count": 0, "details": "Search failed: apps.core.llama_index_manager.LlamaIndexManager.search() got multiple values for keyword argument 'top_k'", "timestamp": "2025-05-30T15:42:46.826810"}, {"category": "Core Functionality", "name": "Multiple Results", "query": "engineering discussions", "passed": false, "response_time": 0.11377716064453125, "result_count": 0, "details": "Search failed: apps.core.llama_index_manager.LlamaIndexManager.search() got multiple values for keyword argument 'top_k'", "timestamp": "2025-05-30T15:42:46.940604"}, {"category": "Result Quality", "name": "Content Quality", "query": "authentication implementation details", "passed": false, "response_time": 0.09984111785888672, "result_count": 0, "details": "Search failed: apps.core.llama_index_manager.LlamaIndexManager.search() got multiple values for keyword argument 'top_k'", "timestamp": "2025-05-30T15:42:47.040481"}, {"category": "Result Quality", "name": "Metadata Quality", "query": "API security best practices", "passed": false, "response_time": 0.0984959602355957, "result_count": 0, "details": "Search failed: apps.core.llama_index_manager.LlamaIndexManager.search() got multiple values for keyword argument 'top_k'", "timestamp": "2025-05-30T15:42:47.138998"}, {"category": "Result Quality", "name": "Relevance Quality", "query": "login issues", "passed": false, "response_time": 0.09945797920227051, "result_count": 0, "details": "Search failed: apps.core.llama_index_manager.LlamaIndexManager.search() got multiple values for keyword argument 'top_k'", "timestamp": "2025-05-30T15:42:47.238476"}, {"category": "Performance", "name": "Standard Performance", "query": "authentication system overview", "passed": false, "response_time": 0.09877514839172363, "result_count": 0, "details": "Search failed: apps.core.llama_index_manager.LlamaIndexManager.search() got multiple values for keyword argument 'top_k'", "timestamp": "2025-05-30T15:42:47.337282"}, {"category": "Performance", "name": "Complex Query Performance", "query": "What are the architectural patterns for implementing scalable authentication?", "passed": false, "response_time": 0.09796595573425293, "result_count": 0, "details": "Search failed: apps.core.llama_index_manager.LlamaIndexManager.search() got multiple values for keyword argument 'top_k'", "timestamp": "2025-05-30T15:42:47.435266"}, {"category": "Advanced Features", "name": "Intent Classification", "query": "How to debug connection issues?", "passed": false, "response_time": 0.0975642204284668, "result_count": 0, "details": "Search failed: apps.core.llama_index_manager.LlamaIndexManager.search() got multiple values for keyword argument 'top_k'", "timestamp": "2025-05-30T15:42:47.532860"}, {"category": "Advanced Features", "name": "Query Enhancement", "query": "auth", "passed": false, "response_time": 0.09461569786071777, "result_count": 0, "details": "Search failed: apps.core.llama_index_manager.LlamaIndexManager.search() got multiple values for keyword argument 'top_k'", "timestamp": "2025-05-30T15:42:47.627495"}, {"category": "Advanced Features", "name": "Hybrid Search", "query": "authentication best practices", "passed": false, "response_time": 0.09481287002563477, "result_count": 0, "details": "Search failed: apps.core.llama_index_manager.LlamaIndexManager.search() got multiple values for keyword argument 'top_k'", "timestamp": "2025-05-30T15:42:47.722325"}]}