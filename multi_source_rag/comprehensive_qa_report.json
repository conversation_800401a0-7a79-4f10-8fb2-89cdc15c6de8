{"timestamp": "2025-05-29T12:04:16.250218", "test_results": {"Data Integrity Tests": {"results": {"sources_tested": 13, "integrity_issues": [], "success": true}, "duration": 0.06336116790771484, "status": "PASSED"}, "Cross-Platform Search Tests": {"results": {"queries_tested": 4, "cross_platform_results": [{"query": "What are the recent technical discussions about security?", "search_time": 4.890830993652344, "total_citations": 10, "source_types_found": ["local_slack"], "expected_sources": ["slack", "github"], "cross_platform_success": false, "answer_length": 1306}, {"query": "What pull requests were discussed in Slack?", "search_time": 7.152688980102539, "total_citations": 10, "source_types_found": ["local_slack"], "expected_sources": ["slack", "github"], "cross_platform_success": false, "answer_length": 3443}, {"query": "What are the latest dependency updates?", "search_time": 10.498424053192139, "total_citations": 10, "source_types_found": ["github", "local_slack"], "expected_sources": ["slack", "github"], "cross_platform_success": true, "answer_length": 3118}, {"query": "What workflow issues have been reported?", "search_time": 3.2683701515197754, "total_citations": 10, "source_types_found": ["github", "local_slack"], "expected_sources": ["slack", "github"], "cross_platform_success": true, "answer_length": 694}], "success": true}, "duration": 25.877825021743774, "status": "PASSED"}, "Targeted Search Tests": {"results": {"queries_tested": 4, "targeted_results": [{"query": "What are the recent pull requests?", "filter": {"source_type": "github"}, "search_time": 10.325530052185059, "total_citations": 8, "source_types_found": ["github"], "filter_compliance": true, "answer_length": 3181}, {"query": "What technical discussions happened today?", "filter": {"source_type": "local_slack"}, "search_time": 16.80342411994934, "total_citations": 10, "source_types_found": ["local_slack"], "filter_compliance": true, "answer_length": 9736}, {"query": "What issues were reported?", "filter": {"source_type": "github"}, "search_time": 4.465616941452026, "total_citations": 0, "source_types_found": [], "filter_compliance": true, "answer_length": 805}, {"query": "What conversations happened about deployment?", "filter": {"source_type": "local_slack"}, "search_time": 11.919963836669922, "total_citations": 10, "source_types_found": ["local_slack"], "filter_compliance": true, "answer_length": 6739}], "success": true}, "duration": 43.56699228286743, "status": "PASSED"}, "Performance Tests": {"results": {"queries_tested": 5, "performance_results": [{"query": "What are the recent technical discussions?", "search_time": 10.916136026382446, "citations_count": 10, "answer_length": 5421, "performance_acceptable": false}, {"query": "What pull requests need review?", "search_time": 7.895845890045166, "citations_count": 10, "answer_length": 2359, "performance_acceptable": true}, {"query": "What security issues were reported?", "search_time": 3.6945040225982666, "citations_count": 10, "answer_length": 850, "performance_acceptable": true}, {"query": "What deployment problems occurred?", "search_time": 4.530925035476685, "citations_count": 10, "answer_length": 1004, "performance_acceptable": true}, {"query": "What are the latest updates?", "search_time": 12.686085224151611, "citations_count": 10, "answer_length": 6624, "performance_acceptable": false}], "success": false}, "duration": 39.729382038116455, "status": "FAILED"}, "Service Integration Tests": {"results": {"tests_run": 8, "integration_issues": [], "success": true}, "duration": 84.04217600822449, "status": "PASSED"}, "Bug Detection Tests": {"results": {"tests_run": 6, "bugs_detected": [], "success": true}, "duration": 46.08855223655701, "status": "PASSED"}}, "bugs_found": [], "summary": {"total_categories": 6, "passed": 5, "failed": 1, "errors": 0, "success_rate": 83.33333333333334}}