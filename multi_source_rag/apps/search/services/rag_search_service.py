"""
Production Search Service - Unified RAG System

This is the single, production-ready search service that consolidates all search capabilities:
- Standard semantic search
- Agentic search with sophistication levels
- Cross-domain search with intelligent routing
- Ultimate search with full optimization
- Slack-aware search with metadata filtering
- Enterprise query routing and citation

This service replaces all other search services and provides a clean, unified interface.
"""

import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

from django.contrib.auth.models import User

from apps.core.llama_index_manager import llama_index_manager_context
from apps.core.performance_cache import get_performance_cache
from apps.core.retrieval import (
    SophisticationLevel,
    RetrievalMode,
    DataDomain,
    FusionStrategy
)
from apps.search.models import SearchQuery, SearchResult
from apps.accounts.models import Tenant

logger = logging.getLogger(__name__)


class RAGSearchService:
    """
    Production-ready unified search service.

    This service provides all search capabilities in a single, clean interface:
    - Standard search for basic queries
    - Agentic search with sophistication levels
    - Cross-domain search with intelligent routing
    - Ultimate search with full optimization
    - Specialized search for different data types
    """

    def __init__(self, tenant_slug: str, user: Optional[User] = None):
        """
        Initialize the production search service.

        Args:
            tenant_slug: Tenant slug for multi-tenancy
            user: Optional user for tracking and personalization
        """
        self.tenant_slug = tenant_slug
        self.user = user

        # Get tenant object
        try:
            self.tenant = Tenant.objects.get(slug=tenant_slug)
        except Tenant.DoesNotExist:
            raise ValueError(f"Tenant with slug '{tenant_slug}' not found")

        # Initialize performance cache
        self.cache = get_performance_cache(tenant_slug)

        # Processing statistics
        self.stats = {
            "queries_processed": 0,
            "total_processing_time": 0.0,
            "average_processing_time": 0.0
        }

    def search(self, query: str, user_id: Optional[int] = None, **kwargs) -> Dict[str, Any]:
        """
        Standard semantic search with basic features.

        Args:
            query: Search query
            user_id: Optional user ID for tracking
            **kwargs: Additional search parameters

        Returns:
            Dict containing search results and metadata
        """
        logger.info(f"Standard search: '{query}' for tenant: {self.tenant_slug}")

        start_time = datetime.now()

        try:
            # Classify query intent
            intent = self._classify_intent(query, kwargs.get('intent'))

            # Get feature configuration for this intent
            features = self._get_feature_config(intent, kwargs)

            # Check cache first
            cached_result = self.cache.get_search_results(query, intent, features)
            if cached_result:
                logger.info(f"Returning cached result for query: {query[:50]}...")
                return cached_result

            # Log search attempt
            search_query = self._log_search_query(query, intent, user_id)

            # Perform search using unified manager
            with llama_index_manager_context(self.tenant_slug) as manager:
                raw_results = manager.search(
                    query=query,
                    intent=intent,
                    top_k=kwargs.get('limit', 20),  # Pass limit as top_k
                    **features
                )

            # Format response
            formatted_results = self._format_results(raw_results, intent, query)

            # Cache the results
            self.cache.set_search_results(query, intent, features, formatted_results)

            # Log successful search
            self._log_search_result(search_query, formatted_results, start_time)

            return formatted_results

        except Exception as e:
            logger.error(f"Error in standard search: {str(e)}", exc_info=True)
            return self._create_error_response(str(e), query, intent if 'intent' in locals() else 'default')

    def agentic_search(self, query: str, user_id: Optional[int] = None,
                      sophistication: Optional[SophisticationLevel] = None,
                      explicit_mode: Optional[RetrievalMode] = None,
                      **kwargs) -> Dict[str, Any]:
        """
        Agentic search with intelligent strategy selection and sophistication levels.

        Args:
            query: Search query
            user_id: Optional user ID for tracking
            sophistication: Optional sophistication level override
            explicit_mode: Optional explicit retrieval mode
            **kwargs: Additional search parameters

        Returns:
            Dict containing enhanced search results with agentic metadata
        """
        logger.info(f"Agentic search: '{query}' with sophistication: {sophistication}")

        start_time = datetime.now()

        try:
            # Classify query intent
            intent = self._classify_intent(query, kwargs.get('intent'))

            # Log search attempt
            search_query = self._log_search_query(query, intent, user_id)

            # Perform agentic search using unified manager
            with llama_index_manager_context(self.tenant_slug) as manager:
                raw_results = manager.agentic_search(
                    query=query,
                    intent=intent,
                    sophistication=sophistication,
                    explicit_mode=explicit_mode,
                    **kwargs
                )

            # Format response with agentic information
            formatted_results = self._format_agentic_results(raw_results, intent, query)

            # Log successful search
            self._log_search_result(search_query, formatted_results, start_time)

            return formatted_results

        except Exception as e:
            logger.error(f"Error in agentic search: {str(e)}", exc_info=True)
            return self._create_error_response(str(e), query, intent if 'intent' in locals() else 'default')

    def cross_domain_search(self, query: str, user_id: Optional[int] = None,
                           available_domains: Optional[List[DataDomain]] = None,
                           sophistication: Optional[SophisticationLevel] = None,
                           fusion_strategy: Optional[FusionStrategy] = None,
                           **kwargs) -> Dict[str, Any]:
        """
        Cross-domain search with intelligent domain routing and result fusion.

        Args:
            query: Search query
            user_id: Optional user ID for tracking
            available_domains: Optional list of available domains to search
            sophistication: Optional sophistication level override
            fusion_strategy: Optional result fusion strategy
            **kwargs: Additional search parameters

        Returns:
            Enhanced search results with cross-domain intelligence
        """
        logger.info(f"Cross-domain search: '{query}' across domains: {available_domains}")

        start_time = datetime.now()

        try:
            # Classify query intent
            intent = self._classify_intent(query, kwargs.get('intent'))

            # Log search attempt
            search_query = self._log_search_query(query, intent, user_id)

            # Perform cross-domain search using unified manager
            with llama_index_manager_context(self.tenant_slug) as manager:
                raw_results = manager.cross_domain_search(
                    query=query,
                    intent=intent,
                    available_domains=available_domains,
                    sophistication=sophistication,
                    fusion_strategy=fusion_strategy,
                    **kwargs
                )

            # Format response with cross-domain information
            formatted_results = self._format_cross_domain_results(raw_results, intent, query)

            # Log successful search
            self._log_search_result(search_query, formatted_results, start_time)

            return formatted_results

        except Exception as e:
            logger.error(f"Error in cross-domain search: {str(e)}", exc_info=True)
            return self._create_error_response(str(e), query, intent if 'intent' in locals() else 'default')

    def ultimate_search(self, query: str, user_id: Optional[int] = None,
                       sophistication: Optional[SophisticationLevel] = None,
                       user_context: Optional[Dict[str, Any]] = None,
                       **kwargs) -> Dict[str, Any]:
        """
        Ultimate search with full agentic intelligence and automatic optimization.

        This is the most advanced search method that provides:
        - Automatic query complexity analysis
        - Intelligent strategy orchestration
        - Cross-domain search with optimal fusion
        - Real-time quality monitoring and adaptation
        - Comprehensive metadata and reasoning

        Args:
            query: Search query
            user_id: Optional user ID for tracking
            sophistication: Optional sophistication level override
            user_context: Optional user context for personalization
            **kwargs: Additional search parameters

        Returns:
            Comprehensive search results with full optimization
        """
        logger.info(f"Ultimate search: '{query}' with full optimization")

        start_time = datetime.now()

        try:
            # Classify query intent
            intent = self._classify_intent(query, kwargs.get('intent'))

            # Log search attempt
            search_query = self._log_search_query(query, intent, user_id)

            # Perform ultimate search using unified manager
            with llama_index_manager_context(self.tenant_slug) as manager:
                raw_results = manager.ultimate_search(
                    query=query,
                    intent=intent,
                    sophistication=sophistication,
                    user_context=user_context,
                    **kwargs
                )

            # Format response with ultimate search information
            formatted_results = self._format_ultimate_results(raw_results, intent, query)

            # Log successful search
            self._log_search_result(search_query, formatted_results, start_time)

            return formatted_results

        except Exception as e:
            logger.error(f"Error in ultimate search: {str(e)}", exc_info=True)
            return self._create_error_response(str(e), query, intent if 'intent' in locals() else 'default')

    def slack_search(self, query: str, user_id: Optional[int] = None,
                    days_back: Optional[int] = None,
                    participants: Optional[List[str]] = None,
                    has_threads: Optional[bool] = None,
                    has_code: Optional[bool] = None,
                    quality_tier: Optional[str] = None,
                    **kwargs) -> Dict[str, Any]:
        """
        Slack-aware search with enhanced metadata filtering.

        Args:
            query: Search query
            user_id: Optional user ID for tracking
            days_back: Number of days to look back
            participants: List of participant usernames
            has_threads: Filter for threaded conversations
            has_code: Filter for conversations with code
            quality_tier: Minimum quality tier
            **kwargs: Additional search parameters

        Returns:
            Slack-specific search results with enhanced metadata
        """
        logger.info(f"Slack search: '{query}' with filters")

        # Create Slack-specific search parameters
        slack_params = {
            'intent': 'conversation',
            'source_filter': 'slack',
            'days_back': days_back,
            'participants': participants,
            'has_threads': has_threads,
            'has_code': has_code,
            'quality_tier': quality_tier
        }

        # Merge with additional kwargs
        search_params = {**slack_params, **kwargs}

        # Use agentic search with conversation intent
        return self.agentic_search(
            query=query,
            user_id=user_id,
            sophistication=SophisticationLevel.ADVANCED,
            **search_params
        )

    # Helper Methods
    def _classify_intent(self, query: str, explicit_intent: Optional[str] = None) -> str:
        """Classify query intent or use explicit intent."""
        if explicit_intent:
            return explicit_intent

        # Simple intent classification logic
        query_lower = query.lower()

        if any(word in query_lower for word in ['code', 'function', 'class', 'method', 'bug', 'error']):
            return 'code'
        elif any(word in query_lower for word in ['conversation', 'discussion', 'chat', 'message']):
            return 'conversation'
        elif any(word in query_lower for word in ['document', 'file', 'pdf', 'doc']):
            return 'document'
        else:
            return 'general'

    def _get_feature_config(self, intent: str, kwargs: Dict[str, Any]) -> Dict[str, Any]:
        """Get feature configuration based on intent and kwargs."""
        base_config = {
            'min_relevance_score': kwargs.get('min_relevance_score', 0.1),
            'use_hybrid_search': kwargs.get('use_hybrid_search', True),
            'use_context_aware': kwargs.get('use_context_aware', True)
        }

        # Intent-specific configurations
        if intent == 'code':
            base_config.update({
                'min_relevance_score': kwargs.get('min_relevance_score', 0.15)
            })
        elif intent == 'conversation':
            base_config.update({
                'min_relevance_score': kwargs.get('min_relevance_score', 0.08)
            })

        return base_config

    def _log_search_query(self, query: str, intent: str, _user_id: Optional[int]) -> Optional[SearchQuery]:
        """Log search query to database."""
        try:
            if self.user:
                return SearchQuery.objects.create(
                    user=self.user,
                    tenant=self.tenant,
                    query_text=query,
                    search_params={'intent': intent}
                )
        except Exception as e:
            logger.warning(f"Failed to log search query: {e}")
        return None

    def _log_search_result(self, search_query: Optional[SearchQuery],
                          results: Dict[str, Any], start_time: datetime) -> None:
        """Log search result to database."""
        try:
            if search_query and self.user:
                processing_time = (datetime.now() - start_time).total_seconds()

                SearchResult.objects.create(
                    search_query=search_query,
                    user=self.user,
                    generated_answer=results.get('answer', ''),
                    retriever_score_avg=results.get('metadata', {}).get('avg_score', 0.0),
                    llm_confidence_score=results.get('metadata', {}).get('confidence', 0.0),
                    total_processing_time=processing_time
                )

                # Update statistics
                self.stats["queries_processed"] += 1
                self.stats["total_processing_time"] += processing_time
                self.stats["average_processing_time"] = (
                    self.stats["total_processing_time"] / self.stats["queries_processed"]
                )

        except Exception as e:
            logger.warning(f"Failed to log search result: {e}")

    def _format_results(self, raw_results: Dict[str, Any], intent: str, query: str) -> Dict[str, Any]:
        """Format standard search results."""
        # Handle both 'sources' and 'source_nodes' keys for compatibility
        sources = raw_results.get('sources', [])
        if not sources and 'source_nodes' in raw_results:
            # Convert source_nodes to sources format
            sources = self._convert_source_nodes_to_sources(raw_results['source_nodes'])

        return {
            'status': 'success',
            'answer': raw_results.get('answer', 'No answer found.'),
            'results': sources,  # Use 'results' instead of 'sources' for testing compatibility
            'sources': sources,  # Keep 'sources' for backward compatibility
            'metadata': {
                'intent': intent,
                'query': query,
                'search_type': 'standard',
                'source_count': len(sources),
                'avg_score': raw_results.get('avg_score', 0.0)
            }
        }

    def _format_agentic_results(self, raw_results: Dict[str, Any], intent: str, query: str) -> Dict[str, Any]:
        """Format agentic search results with enhanced metadata."""
        return {
            'answer': raw_results.get('answer', 'No answer found.'),
            'sources': raw_results.get('sources', []),
            'metadata': {
                'intent': intent,
                'query': query,
                'search_type': 'agentic',
                'sophistication_level': raw_results.get('sophistication_level'),
                'strategy_used': raw_results.get('strategy_used'),
                'reasoning': raw_results.get('reasoning', []),
                'source_count': len(raw_results.get('sources', [])),
                'avg_score': raw_results.get('avg_score', 0.0),
                'confidence': raw_results.get('confidence', 0.0)
            }
        }

    def _format_cross_domain_results(self, raw_results: Dict[str, Any], intent: str, query: str) -> Dict[str, Any]:
        """Format cross-domain search results."""
        return {
            'answer': raw_results.get('answer', 'No answer found.'),
            'sources': raw_results.get('sources', []),
            'metadata': {
                'intent': intent,
                'query': query,
                'search_type': 'cross_domain',
                'domains_searched': raw_results.get('domains_searched', []),
                'fusion_strategy': raw_results.get('fusion_strategy'),
                'domain_scores': raw_results.get('domain_scores', {}),
                'source_count': len(raw_results.get('sources', [])),
                'avg_score': raw_results.get('avg_score', 0.0)
            }
        }

    def _format_ultimate_results(self, raw_results: Dict[str, Any], intent: str, query: str) -> Dict[str, Any]:
        """Format ultimate search results with comprehensive metadata."""
        return {
            'answer': raw_results.get('answer', 'No answer found.'),
            'sources': raw_results.get('sources', []),
            'metadata': {
                'intent': intent,
                'query': query,
                'search_type': 'ultimate',
                'complexity_analysis': raw_results.get('complexity_analysis', {}),
                'strategy_orchestration': raw_results.get('strategy_orchestration', {}),
                'quality_monitoring': raw_results.get('quality_monitoring', {}),
                'optimization_applied': raw_results.get('optimization_applied', []),
                'source_count': len(raw_results.get('sources', [])),
                'avg_score': raw_results.get('avg_score', 0.0),
                'confidence': raw_results.get('confidence', 0.0)
            }
        }

    def _create_error_response(self, error_message: str, query: str, intent: str) -> Dict[str, Any]:
        """Create standardized error response."""
        return {
            'status': 'error',
            'message': error_message,
            'answer': f'An error occurred while processing your query: {error_message}',
            'results': [],
            'sources': [],
            'metadata': {
                'intent': intent,
                'query': query,
                'search_type': 'error',
                'error': error_message,
                'source_count': 0,
                'avg_score': 0.0
            }
        }

    def _convert_source_nodes_to_sources(self, source_nodes: List[Any]) -> List[Dict[str, Any]]:
        """Convert LlamaIndex source_nodes to sources format."""
        sources = []

        for i, node in enumerate(source_nodes):
            try:
                # Extract information from the node
                source = {
                    'text': getattr(node, 'text', '') or getattr(node, 'get_content', lambda: '')(),
                    'relevance': getattr(node, 'score', 0.0),
                    'confidence': getattr(node, 'score', 0.0),
                    'rank': i + 1,
                    'metadata': {}
                }

                # Extract metadata if available
                if hasattr(node, 'metadata') and node.metadata:
                    source['metadata'] = node.metadata
                elif hasattr(node, 'node') and hasattr(node.node, 'metadata'):
                    source['metadata'] = node.node.metadata

                # Extract node ID if available
                if hasattr(node, 'node_id'):
                    source['node_id'] = node.node_id
                elif hasattr(node, 'node') and hasattr(node.node, 'node_id'):
                    source['node_id'] = node.node.node_id

                sources.append(source)

            except Exception as e:
                logger.warning(f"Error converting source node {i}: {e}")
                # Add a minimal source entry
                sources.append({
                    'text': str(node) if node else '',
                    'relevance': 0.0,
                    'confidence': 0.0,
                    'rank': i + 1,
                    'metadata': {}
                })

        return sources

    def search_with_database_result(self, query: str, user_id: Optional[int] = None, **kwargs) -> SearchResult:
        """
        Perform search and return a proper database SearchResult object.

        This method is designed for web views that need database objects
        with proper relationships and citations.

        Args:
            query: Search query
            user_id: Optional user ID for tracking
            **kwargs: Additional search parameters

        Returns:
            SearchResult: Database object with proper relationships
        """
        # Perform the search using agentic search
        search_results = self.agentic_search(query, user_id, **kwargs)

        # Create search query record
        search_query = SearchQuery.objects.create(
            user=self.user,
            tenant=self.tenant,
            query_text=query,
            search_params=kwargs
        )

        # Create search result record
        search_result = SearchResult.objects.create(
            search_query=search_query,
            user=self.user,
            generated_answer=search_results.get('answer', 'No answer found.'),
            retriever_score_avg=search_results.get('metadata', {}).get('avg_score', 0.0),
            llm_confidence_score=search_results.get('metadata', {}).get('confidence', 0.0)
        )

        # Create citations from sources if available
        sources = search_results.get('sources', [])
        if sources:
            self._create_citations_from_sources(search_result, sources)

        return search_result

    def _create_citations_from_sources(self, search_result: SearchResult, sources: List[Dict[str, Any]]) -> None:
        """
        Create citation objects from source data with optimized database queries.

        Args:
            search_result: SearchResult object to create citations for
            sources: List of source dictionaries
        """
        from apps.search.models import ResultCitation
        from apps.documents.models import DocumentChunk

        # Extract all chunk IDs first
        chunk_ids = []
        source_by_chunk_id = {}

        for i, source in enumerate(sources):
            chunk_id = source.get('metadata', {}).get('chunk_id')
            if chunk_id:
                chunk_ids.append(chunk_id)
                source_by_chunk_id[chunk_id] = {
                    'source': source,
                    'rank': i + 1
                }

        if not chunk_ids:
            logger.warning("No valid chunk IDs found in sources")
            return

        # OPTIMIZED: Single query to fetch all chunks with related data
        chunks = DocumentChunk.objects.filter(
            id__in=chunk_ids
        ).select_related(
            'document',
            'document__source',
            'profile'
        ).prefetch_related(
            'embedding_metadata'
        )

        # Create a mapping of chunk_id to chunk object
        chunk_map = {chunk.id: chunk for chunk in chunks}

        # OPTIMIZED: Bulk create citations
        citations_to_create = []

        for chunk_id, source_data in source_by_chunk_id.items():
            if chunk_id in chunk_map:
                chunk = chunk_map[chunk_id]
                source = source_data['source']

                citation = ResultCitation(
                    result=search_result,
                    document_chunk=chunk,
                    relevance_score=source.get('relevance', 0.0),
                    rank=source_data['rank'],
                    citation_text=source.get('text', '')[:500]  # Store citation text
                )
                citations_to_create.append(citation)
            else:
                logger.warning(f"Document chunk {chunk_id} not found")

        # OPTIMIZED: Single bulk create operation
        if citations_to_create:
            try:
                ResultCitation.objects.bulk_create(
                    citations_to_create,
                    ignore_conflicts=True  # Handle duplicates gracefully
                )
                logger.info(f"✅ Created {len(citations_to_create)} citations with optimized queries")
            except Exception as e:
                logger.error(f"Error bulk creating citations: {str(e)}")
                # Fallback to individual creation if bulk fails
                self._create_citations_individually(citations_to_create)

    def _create_citations_individually(self, citations_to_create: List) -> None:
        """Fallback method to create citations individually."""
        for citation in citations_to_create:
            try:
                citation.save()
            except Exception as e:
                logger.error(f"Error creating individual citation: {str(e)}")
                continue


# Convenience Functions for Quick Access
def quick_search(tenant_slug: str, query: str, **kwargs) -> str:
    """Perform quick search and return just the answer."""
    service = RAGSearchService(tenant_slug)
    results = service.search(query, **kwargs)
    return results.get('answer', 'No answer found.')


def quick_agentic_search(tenant_slug: str, query: str,
                        sophistication: SophisticationLevel = SophisticationLevel.ADVANCED,
                        **kwargs) -> str:
    """Perform quick agentic search and return just the answer."""
    service = RAGSearchService(tenant_slug)
    results = service.agentic_search(query, sophistication=sophistication, **kwargs)
    return results.get('answer', 'No answer found.')


def quick_slack_search(tenant_slug: str, query: str, **kwargs) -> str:
    """Perform quick Slack search and return just the answer."""
    service = RAGSearchService(tenant_slug)
    results = service.slack_search(query, **kwargs)
    return results.get('answer', 'No answer found.')


def quick_cross_domain_search(tenant_slug: str, query: str,
                             domains: Optional[List[DataDomain]] = None,
                             sophistication: SophisticationLevel = SophisticationLevel.ADVANCED,
                             **kwargs) -> str:
    """Perform quick cross-domain search and return just the answer."""
    service = RAGSearchService(tenant_slug)
    results = service.cross_domain_search(
        query,
        available_domains=domains,
        sophistication=sophistication,
        **kwargs
    )
    return results.get('answer', 'No answer found.')