"""
Production Search Service - Universal RAG System

This is the single, production-ready search service that provides intelligent,
universal search capabilities with automatic strategy selection:

- Automatic query complexity analysis and strategy selection
- Intelligent sophistication level determination
- Cross-domain search with optimal routing
- Slack-aware search with metadata filtering
- Enterprise-grade performance and monitoring
- Comprehensive citation and result formatting

This service replaces multiple search methods with one universal interface
that automatically optimizes search strategy based on query characteristics.
"""

import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

from django.contrib.auth.models import User

from apps.core.llama_index_manager import llama_index_manager_context
from apps.core.performance_cache import get_performance_cache
from apps.core.retrieval import (
    SophisticationLevel,
    RetrievalMode
)
from apps.search.models import SearchQuery, SearchResult
from apps.accounts.models import Tenant

logger = logging.getLogger(__name__)


class RAGSearchService:
    """
    Production-ready universal search service.

    This service provides intelligent, universal search capabilities that automatically
    determine the optimal search strategy based on query analysis and parameters.

    Key Features:
    - Universal search method with automatic strategy selection
    - Intelligent sophistication level determination
    - Cross-domain routing and result fusion
    - Slack-aware filtering and metadata handling
    - Enterprise-grade performance monitoring
    - Comprehensive citation and result formatting

    The service analyzes each query and automatically selects the best combination of:
    - Search sophistication level (Basic, Advanced, Expert)
    - Retrieval mode (Semantic, Hybrid, Cross-domain)
    - Domain-specific optimizations
    - Result fusion strategies
    """

    def __init__(self, tenant_slug: str, user: Optional[User] = None):
        """
        Initialize the universal search service.

        Args:
            tenant_slug: Tenant slug for multi-tenancy
            user: Optional user for tracking and personalization
        """
        self.tenant_slug = tenant_slug
        self.user = user

        # Get tenant object
        try:
            self.tenant = Tenant.objects.get(slug=tenant_slug)
        except Tenant.DoesNotExist:
            raise ValueError(f"Tenant with slug '{tenant_slug}' not found")

        # Initialize performance cache
        self.cache = get_performance_cache(tenant_slug)

        # Processing statistics
        self.stats = {
            "queries_processed": 0,
            "total_processing_time": 0.0,
            "average_processing_time": 0.0
        }

    def search(self, query: str, user_id: Optional[int] = None, **kwargs) -> Dict[str, Any]:
        """
        Universal intelligent search with automatic strategy selection.

        This method automatically analyzes the query and selects the optimal search strategy,
        sophistication level, and parameters based on query characteristics and user preferences.

        Args:
            query: Search query text
            user_id: Optional user ID for tracking and personalization

            # Search Strategy Parameters (optional - auto-detected if not provided)
            sophistication: Override automatic sophistication level detection
            retrieval_mode: Override automatic retrieval mode selection
            available_domains: Limit search to specific domains
            fusion_strategy: Override automatic fusion strategy selection

            # Search Configuration Parameters
            top_k: Number of results to return (default: 20)
            limit: Alias for top_k for backward compatibility
            min_relevance_score: Minimum relevance threshold (auto-adjusted by intent)

            # Feature Toggles
            use_hybrid_search: Enable hybrid search (default: True)
            use_context_aware: Enable context-aware search (default: True)
            use_query_expansion: Enable query expansion (default: auto-detected)
            use_multi_step_reasoning: Enable multi-step reasoning (default: auto-detected)

            # Filtering Parameters
            filter: General filter parameters
            source_filter: Filter by source type (e.g., 'slack', 'github')
            intent: Override automatic intent classification

            # Slack-specific Parameters (auto-detected for Slack queries)
            days_back: Number of days to look back for Slack messages
            participants: List of participant usernames for Slack filtering
            has_threads: Filter for threaded conversations
            has_code: Filter for conversations with code
            quality_tier: Minimum quality tier for results

            # Output Parameters
            output_format: Response format ('text', 'json', 'structured')

        Returns:
            Dict containing comprehensive search results with metadata:
            {
                'answer': str,              # Generated answer
                'sources': List[Dict],      # Source documents with metadata
                'metadata': {               # Search metadata
                    'strategy_used': str,   # Selected search strategy
                    'sophistication_level': str,  # Applied sophistication level
                    'intent': str,          # Detected or provided intent
                    'search_type': str,     # Type of search performed
                    'processing_time': float,  # Processing time in seconds
                    'source_count': int,    # Number of sources found
                    'confidence': float,    # Overall confidence score
                    'avg_score': float,     # Average relevance score
                    # Additional strategy-specific metadata
                }
            }

        Examples:
            # Simple search - automatically optimized
            results = service.search("What is the latest update on project X?")

            # Slack-specific search - automatically detected
            results = service.search("Curana customer feedback from last week")

            # Code search - automatically optimized for code intent
            results = service.search("How to implement authentication in Django?")

            # Advanced search with explicit parameters
            results = service.search(
                "Complex technical analysis",
                sophistication=SophisticationLevel.EXPERT,
                use_multi_step_reasoning=True,
                min_relevance_score=0.3
            )
        """
        logger.info(f"Universal search: '{query}' for tenant: {self.tenant_slug}")

        start_time = datetime.now()

        try:
            # Step 1: Analyze query and determine optimal strategy
            search_strategy = self._analyze_query_and_select_strategy(query, kwargs)

            # Step 2: Check cache with strategy-aware key
            cached_result = self._get_cached_result(query, search_strategy)
            if cached_result:
                logger.info(f"Returning cached result for query: {query[:50]}...")
                return cached_result

            # Step 3: Log search attempt
            search_query = self._log_search_query(query, search_strategy['intent'], user_id)

            # Step 4: Execute search using selected strategy
            raw_results = self._execute_search_strategy(query, search_strategy)

            # Step 5: Format results with strategy metadata
            formatted_results = self._format_universal_results(raw_results, search_strategy, query)

            # Step 6: Cache the results
            self._cache_search_results(query, search_strategy, formatted_results)

            # Step 7: Log successful search
            self._log_search_result(search_query, formatted_results, start_time)

            return formatted_results

        except Exception as e:
            logger.error(f"Error in universal search: {str(e)}", exc_info=True)
            intent = kwargs.get('intent', 'general')
            return self._create_error_response(str(e), query, intent)



    # Universal Search Helper Methods
    def _analyze_query_and_select_strategy(self, query: str, kwargs: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze query characteristics and select optimal search strategy.

        This method performs intelligent analysis of the query and user parameters
        to determine the best search approach, sophistication level, and configuration.
        """
        # Step 1: Classify intent (explicit or automatic)
        intent = self._classify_intent(query, kwargs.get('intent'))

        # Step 2: Determine sophistication level
        sophistication = self._determine_sophistication_level(query, kwargs)

        # Step 3: Select retrieval mode
        retrieval_mode = self._select_retrieval_mode(query, intent, kwargs)

        # Step 4: Configure search features
        features = self._configure_search_features(query, intent, kwargs)

        # Step 5: Detect domain-specific requirements
        domain_config = self._detect_domain_requirements(query, intent, kwargs)

        return {
            'intent': intent,
            'sophistication': sophistication,
            'retrieval_mode': retrieval_mode,
            'features': features,
            'domain_config': domain_config,
            'strategy_name': self._get_strategy_name(sophistication, retrieval_mode, domain_config)
        }

    def _determine_sophistication_level(self, query: str, kwargs: Dict[str, Any]) -> SophisticationLevel:
        """Determine optimal sophistication level based on query complexity."""
        # Check for explicit override
        if 'sophistication' in kwargs:
            return kwargs['sophistication']

        query_lower = query.lower()

        # Expert level indicators
        expert_indicators = [
            'analyze', 'compare', 'evaluate', 'synthesize', 'comprehensive',
            'detailed analysis', 'in-depth', 'complex', 'multi-step'
        ]

        # Advanced level indicators
        advanced_indicators = [
            'explain', 'summarize', 'how to', 'what is', 'why', 'when',
            'curana', 'customer feedback', 'conversation', 'discussion'
        ]

        if any(indicator in query_lower for indicator in expert_indicators):
            return SophisticationLevel.EXPERT
        elif any(indicator in query_lower for indicator in advanced_indicators):
            return SophisticationLevel.ADVANCED
        else:
            return SophisticationLevel.BASIC

    def _select_retrieval_mode(self, query: str, intent: str, kwargs: Dict[str, Any]) -> RetrievalMode:
        """Select optimal retrieval mode based on query and intent."""
        # Check for explicit override
        if 'explicit_mode' in kwargs:
            return kwargs['explicit_mode']

        # TODO: Add sophisticated logic based on query and intent analysis
        # Parameters query and intent are reserved for future enhancement
        # For now, default to semantic for most queries
        return RetrievalMode.SEMANTIC

    def _configure_search_features(self, query: str, intent: str, kwargs: Dict[str, Any]) -> Dict[str, Any]:
        """Configure search features based on query analysis."""
        query_lower = query.lower()

        # Base configuration
        features = {
            'top_k': kwargs.get('top_k', kwargs.get('limit', 20)),
            'min_relevance_score': kwargs.get('min_relevance_score'),
            'use_hybrid_search': kwargs.get('use_hybrid_search', True),
            'use_context_aware': kwargs.get('use_context_aware', True),
            'use_query_expansion': kwargs.get('use_query_expansion'),
            'use_multi_step_reasoning': kwargs.get('use_multi_step_reasoning'),
            'output_format': kwargs.get('output_format', 'text')
        }

        # Auto-detect query expansion need
        if features['use_query_expansion'] is None:
            features['use_query_expansion'] = any(word in query_lower for word in [
                'similar', 'related', 'like', 'alternatives', 'options'
            ])

        # Auto-detect multi-step reasoning need
        if features['use_multi_step_reasoning'] is None:
            features['use_multi_step_reasoning'] = any(word in query_lower for word in [
                'step by step', 'how to', 'process', 'workflow', 'procedure'
            ])

        # Auto-adjust relevance score based on intent and query
        if features['min_relevance_score'] is None:
            if 'curana' in query_lower and 'summarize' in query_lower:
                features['min_relevance_score'] = 0.2
            elif 'customer feedback' in query_lower:
                features['min_relevance_score'] = 0.2
            elif intent == 'conversation':
                features['min_relevance_score'] = 0.08
            elif intent == 'code':
                features['min_relevance_score'] = 0.15
            else:
                features['min_relevance_score'] = 0.1

        return features

    def _detect_domain_requirements(self, query: str, intent: str, kwargs: Dict[str, Any]) -> Dict[str, Any]:
        """Detect domain-specific requirements and filtering."""
        query_lower = query.lower()

        domain_config = {
            'is_slack_query': False,
            'is_code_query': False,
            'is_cross_domain': False,
            'filters': {}
        }

        # Detect Slack-specific queries
        slack_indicators = ['slack', 'message', 'conversation', 'chat', 'curana', 'customer feedback']
        if any(indicator in query_lower for indicator in slack_indicators) or intent == 'conversation':
            domain_config['is_slack_query'] = True
            domain_config['filters'].update({
                'source_filter': 'slack',
                'days_back': kwargs.get('days_back'),
                'participants': kwargs.get('participants'),
                'has_threads': kwargs.get('has_threads'),
                'has_code': kwargs.get('has_code'),
                'quality_tier': kwargs.get('quality_tier')
            })

        # Detect code-specific queries
        code_indicators = ['code', 'function', 'class', 'method', 'bug', 'error', 'implementation']
        if any(indicator in query_lower for indicator in code_indicators) or intent == 'code':
            domain_config['is_code_query'] = True

        # Add general filters
        if 'filter' in kwargs:
            domain_config['filters'].update(kwargs['filter'])
        if 'source_filter' in kwargs:
            domain_config['filters']['source_filter'] = kwargs['source_filter']

        return domain_config

    def _get_strategy_name(self, sophistication: SophisticationLevel,
                          retrieval_mode: RetrievalMode, domain_config: Dict[str, Any]) -> str:
        """Generate a descriptive name for the selected strategy."""
        parts = []

        if domain_config['is_slack_query']:
            parts.append('Slack-Aware')
        elif domain_config['is_code_query']:
            parts.append('Code-Optimized')
        elif domain_config['is_cross_domain']:
            parts.append('Cross-Domain')
        else:
            parts.append('Universal')

        parts.append(sophistication.value.title())
        parts.append(retrieval_mode.value.title())

        return ' '.join(parts) + ' Search'

    def _get_cached_result(self, query: str, search_strategy: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Get cached result with strategy-aware key."""
        try:
            cache_key = f"{query}_{search_strategy['strategy_name']}_{search_strategy['intent']}"
            return self.cache.get_search_results(cache_key, search_strategy['intent'], search_strategy['features'])
        except Exception as e:
            logger.warning(f"Cache retrieval failed: {e}")
            return None

    def _execute_search_strategy(self, query: str, search_strategy: Dict[str, Any]) -> Dict[str, Any]:
        """Execute search using the selected strategy."""
        with llama_index_manager_context(self.tenant_slug) as manager:
            # Determine which search method to use based on strategy
            if search_strategy['domain_config']['is_slack_query']:
                return manager.agentic_search(
                    query=query,
                    intent=search_strategy['intent'],
                    sophistication=search_strategy['sophistication'],
                    **search_strategy['features'],
                    **search_strategy['domain_config']['filters']
                )
            elif search_strategy['sophistication'] == SophisticationLevel.EXPERT:
                return manager.ultimate_search(
                    query=query,
                    intent=search_strategy['intent'],
                    sophistication=search_strategy['sophistication'],
                    **search_strategy['features']
                )
            elif search_strategy['domain_config']['is_cross_domain']:
                return manager.cross_domain_search(
                    query=query,
                    intent=search_strategy['intent'],
                    sophistication=search_strategy['sophistication'],
                    **search_strategy['features']
                )
            else:
                return manager.agentic_search(
                    query=query,
                    intent=search_strategy['intent'],
                    sophistication=search_strategy['sophistication'],
                    **search_strategy['features']
                )

    def _format_universal_results(self, raw_results: Dict[str, Any],
                                 search_strategy: Dict[str, Any], query: str) -> Dict[str, Any]:
        """Format results with universal search metadata."""
        # Handle both 'sources' and 'source_nodes' keys for compatibility
        sources = raw_results.get('sources', [])
        if not sources and 'source_nodes' in raw_results:
            sources = self._convert_source_nodes_to_sources(raw_results['source_nodes'])

        return {
            'status': 'success',
            'answer': raw_results.get('answer', 'No answer found.'),
            'results': sources,  # Use 'results' for testing compatibility
            'sources': sources,  # Keep 'sources' for backward compatibility
            'metadata': {
                'intent': search_strategy['intent'],
                'query': query,
                'search_type': 'universal',
                'strategy_used': search_strategy['strategy_name'],
                'sophistication_level': search_strategy['sophistication'].value,
                'retrieval_mode': search_strategy['retrieval_mode'].value,
                'domain_config': search_strategy['domain_config'],
                'features_applied': search_strategy['features'],
                'source_count': len(sources),
                'avg_score': raw_results.get('avg_score', 0.0),
                'confidence': raw_results.get('confidence', 0.0),
                'reasoning': raw_results.get('reasoning', []),
                'optimization_applied': raw_results.get('optimization_applied', [])
            }
        }

    def _cache_search_results(self, query: str, search_strategy: Dict[str, Any],
                             formatted_results: Dict[str, Any]) -> None:
        """Cache search results with strategy-aware key."""
        try:
            cache_key = f"{query}_{search_strategy['strategy_name']}_{search_strategy['intent']}"
            self.cache.set_search_results(cache_key, search_strategy['intent'],
                                        search_strategy['features'], formatted_results)
        except Exception as e:
            logger.warning(f"Cache storage failed: {e}")

    # Helper Methods
    def _classify_intent(self, query: str, explicit_intent: Optional[str] = None) -> str:
        """Classify query intent or use explicit intent."""
        if explicit_intent:
            return explicit_intent

        # Simple intent classification logic
        query_lower = query.lower()

        if any(word in query_lower for word in ['code', 'function', 'class', 'method', 'bug', 'error']):
            return 'code'
        elif any(word in query_lower for word in ['conversation', 'discussion', 'chat', 'message']):
            return 'conversation'
        elif any(word in query_lower for word in ['document', 'file', 'pdf', 'doc']):
            return 'document'
        else:
            return 'general'

    def _log_search_query(self, query: str, intent: str, user_id: Optional[int] = None) -> Optional[SearchQuery]:
        """Log search query to database."""
        # Note: user_id parameter is reserved for future use when user context differs from self.user
        try:
            if self.user:
                return SearchQuery.objects.create(
                    user=self.user,
                    tenant=self.tenant,
                    query_text=query,
                    search_params={'intent': intent}
                )
        except Exception as e:
            logger.warning(f"Failed to log search query: {e}")
        return None

    def _log_search_result(self, search_query: Optional[SearchQuery],
                          results: Dict[str, Any], start_time: datetime) -> None:
        """Log search result to database."""
        try:
            if search_query and self.user:
                processing_time = (datetime.now() - start_time).total_seconds()

                SearchResult.objects.create(
                    search_query=search_query,
                    user=self.user,
                    generated_answer=results.get('answer', ''),
                    retriever_score_avg=results.get('metadata', {}).get('avg_score', 0.0),
                    llm_confidence_score=results.get('metadata', {}).get('confidence', 0.0),
                    total_processing_time=processing_time
                )

                # Update statistics
                self.stats["queries_processed"] += 1
                self.stats["total_processing_time"] += processing_time
                self.stats["average_processing_time"] = (
                    self.stats["total_processing_time"] / self.stats["queries_processed"]
                )

        except Exception as e:
            logger.warning(f"Failed to log search result: {e}")

    def _create_error_response(self, error_message: str, query: str, intent: str) -> Dict[str, Any]:
        """Create standardized error response."""
        return {
            'status': 'error',
            'message': error_message,
            'answer': f'An error occurred while processing your query: {error_message}',
            'results': [],
            'sources': [],
            'metadata': {
                'intent': intent,
                'query': query,
                'search_type': 'error',
                'error': error_message,
                'source_count': 0,
                'avg_score': 0.0
            }
        }

    def _convert_source_nodes_to_sources(self, source_nodes: List[Any]) -> List[Dict[str, Any]]:
        """Convert LlamaIndex source_nodes to sources format."""
        sources = []

        for i, node in enumerate(source_nodes):
            try:
                # Extract information from the node
                source = {
                    'text': getattr(node, 'text', '') or getattr(node, 'get_content', lambda: '')(),
                    'relevance': getattr(node, 'score', 0.0),
                    'confidence': getattr(node, 'score', 0.0),
                    'rank': i + 1,
                    'metadata': {}
                }

                # Extract metadata if available
                if hasattr(node, 'metadata') and node.metadata:
                    source['metadata'] = node.metadata
                elif hasattr(node, 'node') and hasattr(node.node, 'metadata'):
                    source['metadata'] = node.node.metadata

                # Extract node ID if available
                if hasattr(node, 'node_id'):
                    source['node_id'] = node.node_id
                elif hasattr(node, 'node') and hasattr(node.node, 'node_id'):
                    source['node_id'] = node.node.node_id

                sources.append(source)

            except Exception as e:
                logger.warning(f"Error converting source node {i}: {e}")
                # Add a minimal source entry
                sources.append({
                    'text': str(node) if node else '',
                    'relevance': 0.0,
                    'confidence': 0.0,
                    'rank': i + 1,
                    'metadata': {}
                })

        return sources

    def search_with_database_result(self, query: str, user_id: Optional[int] = None, **kwargs) -> SearchResult:
        """
        Perform search and return a proper database SearchResult object.

        This method is designed for web views that need database objects
        with proper relationships and citations.

        Args:
            query: Search query
            user_id: Optional user ID for tracking
            **kwargs: Additional search parameters

        Returns:
            SearchResult: Database object with proper relationships
        """
        # Perform the search using universal search
        search_results = self.search(query, user_id, **kwargs)

        # Create search query record
        search_query = SearchQuery.objects.create(
            user=self.user,
            tenant=self.tenant,
            query_text=query,
            search_params=kwargs
        )

        # Create search result record
        search_result = SearchResult.objects.create(
            search_query=search_query,
            user=self.user,
            generated_answer=search_results.get('answer', 'No answer found.'),
            retriever_score_avg=search_results.get('metadata', {}).get('avg_score', 0.0),
            llm_confidence_score=search_results.get('metadata', {}).get('confidence', 0.0)
        )

        # Create citations from sources if available
        sources = search_results.get('sources', [])
        if sources:
            self._create_citations_from_sources(search_result, sources)

        return search_result

    def _create_citations_from_sources(self, search_result: SearchResult, sources: List[Dict[str, Any]]) -> None:
        """
        Create citation objects from source data with optimized database queries.

        Args:
            search_result: SearchResult object to create citations for
            sources: List of source dictionaries
        """
        from apps.search.models import ResultCitation
        from apps.documents.models import DocumentChunk

        # Extract all chunk IDs first
        chunk_ids = []
        source_by_chunk_id = {}

        for i, source in enumerate(sources):
            chunk_id = source.get('metadata', {}).get('chunk_id')
            if chunk_id:
                chunk_ids.append(chunk_id)
                source_by_chunk_id[chunk_id] = {
                    'source': source,
                    'rank': i + 1
                }

        if not chunk_ids:
            logger.warning("No valid chunk IDs found in sources")
            return

        # OPTIMIZED: Single query to fetch all chunks with related data
        chunks = DocumentChunk.objects.filter(
            id__in=chunk_ids
        ).select_related(
            'document',
            'document__source',
            'profile'
        ).prefetch_related(
            'embedding_metadata'
        )

        # Create a mapping of chunk_id to chunk object
        chunk_map = {chunk.id: chunk for chunk in chunks}

        # OPTIMIZED: Bulk create citations
        citations_to_create = []

        for chunk_id, source_data in source_by_chunk_id.items():
            if chunk_id in chunk_map:
                chunk = chunk_map[chunk_id]
                source = source_data['source']

                citation = ResultCitation(
                    result=search_result,
                    document_chunk=chunk,
                    relevance_score=source.get('relevance', 0.0),
                    rank=source_data['rank'],
                    citation_text=source.get('text', '')[:500]  # Store citation text
                )
                citations_to_create.append(citation)
            else:
                logger.warning(f"Document chunk {chunk_id} not found")

        # OPTIMIZED: Single bulk create operation
        if citations_to_create:
            try:
                ResultCitation.objects.bulk_create(
                    citations_to_create,
                    ignore_conflicts=True  # Handle duplicates gracefully
                )
                logger.info(f"✅ Created {len(citations_to_create)} citations with optimized queries")
            except Exception as e:
                logger.error(f"Error bulk creating citations: {str(e)}")
                # Fallback to individual creation if bulk fails
                self._create_citations_individually(citations_to_create)

    def _create_citations_individually(self, citations_to_create: List) -> None:
        """Fallback method to create citations individually."""
        for citation in citations_to_create:
            try:
                citation.save()
            except Exception as e:
                logger.error(f"Error creating individual citation: {str(e)}")
                continue


# Convenience Functions for Quick Access
def quick_search(tenant_slug: str, query: str, **kwargs) -> str:
    """
    Perform quick universal search and return just the answer.

    This function provides a simple interface for getting search answers
    without dealing with the full response structure.
    """
    service = RAGSearchService(tenant_slug)
    results = service.search(query, **kwargs)
    return results.get('answer', 'No answer found.')


def quick_advanced_search(tenant_slug: str, query: str,
                         sophistication: SophisticationLevel = SophisticationLevel.ADVANCED,
                         **kwargs) -> str:
    """
    Perform quick advanced search and return just the answer.

    This function allows explicit sophistication level control while using
    the universal search method.
    """
    service = RAGSearchService(tenant_slug)
    results = service.search(query, sophistication=sophistication, **kwargs)
    return results.get('answer', 'No answer found.')


def quick_slack_search(tenant_slug: str, query: str, **kwargs) -> str:
    """
    Perform quick Slack-optimized search and return just the answer.

    Automatically detects Slack queries and applies appropriate optimizations.
    """
    service = RAGSearchService(tenant_slug)
    # Add Slack-specific parameters to trigger Slack optimization
    kwargs['source_filter'] = 'slack'
    kwargs['intent'] = 'conversation'
    results = service.search(query, **kwargs)
    return results.get('answer', 'No answer found.')


def quick_code_search(tenant_slug: str, query: str, **kwargs) -> str:
    """
    Perform quick code-optimized search and return just the answer.

    Automatically applies code-specific optimizations and parameters.
    """
    service = RAGSearchService(tenant_slug)
    kwargs['intent'] = 'code'
    results = service.search(query, **kwargs)
    return results.get('answer', 'No answer found.')