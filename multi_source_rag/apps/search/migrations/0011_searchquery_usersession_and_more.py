# Generated by Django 4.2.10 on 2025-05-31 02:25

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("accounts", "0006_alter_userplatformprofile_user"),
        ("search", "0009_merge_20250530_0901"),
    ]

    operations = [
        migrations.CreateModel(
            name="SearchQuery",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("query_text", models.TextField()),
                (
                    "query_hash",
                    models.CharField(db_index=True, editable=False, max_length=64),
                ),
                ("timestamp", models.DateTimeField(auto_now_add=True, db_index=True)),
                ("search_params", models.JSONField(blank=True, default=dict)),
                ("processing_time", models.FloatField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name="UserSession",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("started_at", models.DateTimeField(auto_now_add=True, db_index=True)),
                ("context_metadata", models.JSONField(blank=True, default=dict)),
                ("is_active", models.BooleanField(default=True)),
            ],
        ),
        migrations.AddField(
            model_name="conversation",
            name="average_response_time",
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="conversation",
            name="conversation_quality_score",
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="conversation",
            name="last_activity",
            field=models.DateTimeField(auto_now=True, db_index=True),
        ),
        migrations.AddField(
            model_name="conversation",
            name="total_messages",
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name="feedback",
            name="accuracy_score",
            field=models.IntegerField(
                blank=True, choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)], null=True
            ),
        ),
        migrations.AddField(
            model_name="feedback",
            name="completeness_score",
            field=models.IntegerField(
                blank=True, choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)], null=True
            ),
        ),
        migrations.AddField(
            model_name="feedback",
            name="rating",
            field=models.IntegerField(
                blank=True, choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)], null=True
            ),
        ),
        migrations.AddField(
            model_name="feedback",
            name="relevance_score",
            field=models.IntegerField(
                blank=True, choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)], null=True
            ),
        ),
        migrations.AddField(
            model_name="message",
            name="edit_count",
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name="message",
            name="last_edited",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="message",
            name="processing_time",
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="message",
            name="token_count",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="resultcitation",
            name="citation_text",
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name="resultcitation",
            name="confidence_score",
            field=models.FloatField(default=0.0),
        ),
        migrations.AddField(
            model_name="resultcitation",
            name="end_char",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="resultcitation",
            name="start_char",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="searchresult",
            name="generation_time",
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="searchresult",
            name="relevance_score",
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="searchresult",
            name="retrieval_time",
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="searchresult",
            name="total_processing_time",
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="searchresult",
            name="user_feedback_score",
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="conversation",
            name="is_active",
            field=models.BooleanField(db_index=True, default=True),
        ),
        migrations.AlterField(
            model_name="feedback",
            name="submitted_at",
            field=models.DateTimeField(auto_now_add=True, db_index=True),
        ),
        migrations.AlterField(
            model_name="message",
            name="created_at",
            field=models.DateTimeField(auto_now_add=True, db_index=True),
        ),
        migrations.AlterField(
            model_name="message",
            name="is_user",
            field=models.BooleanField(db_index=True, default=True),
        ),
        migrations.AlterField(
            model_name="resultcitation",
            name="rank",
            field=models.IntegerField(db_index=True, default=0),
        ),
        migrations.AlterField(
            model_name="resultcitation",
            name="relevance_score",
            field=models.FloatField(db_index=True, default=0.0),
        ),
        migrations.AlterField(
            model_name="searchresult",
            name="llm_confidence_score",
            field=models.FloatField(db_index=True, default=0.0),
        ),
        migrations.AlterField(
            model_name="searchresult",
            name="retriever_score_avg",
            field=models.FloatField(db_index=True, default=0.0),
        ),
        migrations.AlterField(
            model_name="searchresult",
            name="timestamp",
            field=models.DateTimeField(auto_now_add=True, db_index=True),
        ),
        migrations.AddIndex(
            model_name="conversation",
            index=models.Index(
                fields=["user", "is_active", "updated_at"],
                name="search_conv_user_id_f3dba5_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="conversation",
            index=models.Index(
                fields=["tenant", "last_activity"],
                name="search_conv_tenant__1f5f9c_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="feedback",
            index=models.Index(
                fields=["user", "submitted_at"], name="search_feed_user_id_af50d5_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="feedback",
            index=models.Index(
                fields=["is_helpful"], name="search_feed_is_help_0212c9_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="message",
            index=models.Index(
                fields=["conversation", "created_at"],
                name="search_mess_convers_2d0bac_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="message",
            index=models.Index(
                fields=["is_user", "created_at"], name="search_mess_is_user_47619f_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="resultcitation",
            index=models.Index(
                fields=["result", "rank"], name="search_resu_result__2675be_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="resultcitation",
            index=models.Index(
                fields=["relevance_score"], name="search_resu_relevan_578519_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="searchresult",
            index=models.Index(
                fields=["user", "timestamp"], name="search_sear_user_id_fd047d_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="searchresult",
            index=models.Index(
                fields=["llm_confidence_score"], name="search_sear_llm_con_726594_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="searchresult",
            index=models.Index(
                fields=["retriever_score_avg"], name="search_sear_retriev_a97aab_idx"
            ),
        ),
        migrations.AddField(
            model_name="usersession",
            name="tenant",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="accounts.tenant"
            ),
        ),
        migrations.AddField(
            model_name="usersession",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="sessions",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="searchquery",
            name="session",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="queries",
                to="search.usersession",
            ),
        ),
        migrations.AddField(
            model_name="searchquery",
            name="tenant",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="accounts.tenant"
            ),
        ),
        migrations.AddField(
            model_name="searchquery",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="search_queries",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="message",
            name="search_query",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="messages",
                to="search.searchquery",
            ),
        ),
        migrations.AlterField(
            model_name="searchresult",
            name="search_query",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="results",
                to="search.searchquery",
            ),
        ),
        migrations.AddIndex(
            model_name="usersession",
            index=models.Index(
                fields=["user", "started_at"], name="search_user_user_id_41c794_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="usersession",
            index=models.Index(
                fields=["tenant", "is_active"], name="search_user_tenant__d77b74_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="searchquery",
            index=models.Index(
                fields=["user", "timestamp"], name="search_sear_user_id_14004e_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="searchquery",
            index=models.Index(
                fields=["tenant", "timestamp"], name="search_sear_tenant__c34f09_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="searchquery",
            index=models.Index(
                fields=["query_hash"], name="search_sear_query_h_a84170_idx"
            ),
        ),
    ]
