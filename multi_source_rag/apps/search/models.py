"""
Production Search Models - Enhanced with Performance Optimizations

This module contains the production-ready models for the RAG search system with:
- Proper database indexes for performance
- Enhanced validation and constraints
- Performance tracking fields
- Quality metrics and analytics
- Comprehensive metadata support
"""

from apps.core.models import TenantAwareModel
from apps.documents.models import DocumentChunk
from django.contrib.auth.models import User
from django.db import models
from django.core.exceptions import ValidationError
from django.utils import timezone
import hashlib


class UserSession(TenantAwareModel):
    """
    Tracks user interaction sessions, allowing for contextual conversations and history-aware responses.
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="sessions")
    started_at = models.DateTimeField(auto_now_add=True, db_index=True)  # Added index
    context_metadata = models.JSONField(default=dict, blank=True)
    is_active = models.BooleanField(default=True)  # Added field

    class Meta:
        indexes = [
            models.Index(fields=['user', 'started_at']),
            models.Index(fields=['tenant', 'is_active']),
        ]

    def __str__(self):
        return f"Session {self.id} - {self.user.username}"


class SearchQuery(TenantAwareModel):
    """
    Records user queries within a tenant's environment, linking to user sessions for conversation context preservation.
    """
    query_text = models.TextField()
    query_hash = models.CharField(max_length=64, db_index=True, editable=False)  # Added for caching
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="search_queries")
    session = models.ForeignKey(
        UserSession,
        on_delete=models.CASCADE,
        related_name="queries",
        null=True,
        blank=True,
    )
    timestamp = models.DateTimeField(auto_now_add=True, db_index=True)  # Added index
    search_params = models.JSONField(default=dict, blank=True)
    processing_time = models.FloatField(null=True, blank=True)  # Added for analytics

    class Meta:
        indexes = [
            models.Index(fields=['user', 'timestamp']),
            models.Index(fields=['tenant', 'timestamp']),
            models.Index(fields=['query_hash']),
        ]

    def save(self, *args, **kwargs):
        # Generate query hash for caching
        if not self.query_hash:
            self.query_hash = hashlib.sha256(
                f"{self.query_text}{self.search_params}".encode()
            ).hexdigest()
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.query_text[:50]}..."


class SearchResult(models.Model):
    """
    Stores generated answers and metadata about retrieval quality, with relevance scoring for analytical purposes.
    """
    search_query = models.ForeignKey(
        SearchQuery, on_delete=models.CASCADE, related_name="results"
    )
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="search_results"
    )
    generated_answer = models.TextField()
    timestamp = models.DateTimeField(auto_now_add=True, db_index=True)  # Added index
    retriever_score_avg = models.FloatField(default=0.0, db_index=True)  # Added index
    llm_confidence_score = models.FloatField(default=0.0, db_index=True)  # Added index

    # Performance tracking
    total_processing_time = models.FloatField(null=True, blank=True)
    retrieval_time = models.FloatField(null=True, blank=True)
    generation_time = models.FloatField(null=True, blank=True)

    # Quality metrics
    user_feedback_score = models.FloatField(null=True, blank=True)
    relevance_score = models.FloatField(null=True, blank=True)

    # Many-to-many relationship with DocumentChunk through ResultCitation
    cited_chunks = models.ManyToManyField(
        DocumentChunk, through="ResultCitation", related_name="cited_in_results"
    )

    class Meta:
        ordering = ["-timestamp"]
        indexes = [
            models.Index(fields=['user', 'timestamp']),
            models.Index(fields=['llm_confidence_score']),
            models.Index(fields=['retriever_score_avg']),
        ]

    def __str__(self):
        return f"Result for {self.search_query}"


class ResultCitation(models.Model):
    """
    Links search results to the document chunks that were used to generate the answer.
    """
    result = models.ForeignKey(
        SearchResult, on_delete=models.CASCADE, related_name="citations"
    )
    document_chunk = models.ForeignKey(
        DocumentChunk, on_delete=models.CASCADE, related_name="result_citations"
    )
    relevance_score = models.FloatField(default=0.0, db_index=True)  # Citation relevance
    rank = models.IntegerField(default=0, db_index=True)  # Added index

    # Enhanced citation metadata
    citation_text = models.TextField(blank=True)  # Actual cited text
    start_char = models.IntegerField(null=True, blank=True)  # Citation position
    end_char = models.IntegerField(null=True, blank=True)

    class Meta:
        ordering = ["result", "rank"]
        unique_together = ("result", "document_chunk")
        indexes = [
            models.Index(fields=['result', 'rank']),
            models.Index(fields=['relevance_score']),
        ]

    def __str__(self):
        return f"Citation {self.rank} for {self.result}"


class Feedback(models.Model):
    """
    Captures user feedback on search results, enabling quality improvement and performance tracking.
    """
    result = models.ForeignKey(
        SearchResult, on_delete=models.CASCADE, related_name="feedback"
    )
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="feedback")
    is_helpful = models.BooleanField()
    rating = models.IntegerField(choices=[(i, i) for i in range(1, 6)], null=True, blank=True)  # 1-5 rating
    comment = models.TextField(blank=True, null=True)
    submitted_at = models.DateTimeField(auto_now_add=True, db_index=True)  # Added index

    # Specific feedback categories
    accuracy_score = models.IntegerField(choices=[(i, i) for i in range(1, 6)], null=True, blank=True)
    relevance_score = models.IntegerField(choices=[(i, i) for i in range(1, 6)], null=True, blank=True)
    completeness_score = models.IntegerField(choices=[(i, i) for i in range(1, 6)], null=True, blank=True)

    class Meta:
        indexes = [
            models.Index(fields=['user', 'submitted_at']),
            models.Index(fields=['is_helpful']),
        ]

    def __str__(self):
        return f"Feedback on {self.result} by {self.user.username}"


class Conversation(TenantAwareModel):
    """
    Model to store conversations with enhanced metadata and validation.
    """
    title = models.CharField(max_length=255, blank=True, null=True)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="conversations")
    is_active = models.BooleanField(default=True, db_index=True)  # Added index
    metadata = models.JSONField(default=dict, blank=True)

    # Enhanced conversation tracking
    total_messages = models.IntegerField(default=0)  # Denormalized for performance
    last_activity = models.DateTimeField(auto_now=True, db_index=True)
    average_response_time = models.FloatField(null=True, blank=True)
    conversation_quality_score = models.FloatField(null=True, blank=True)

    class Meta:
        ordering = ["-updated_at"]
        indexes = [
            models.Index(fields=['user', 'is_active', 'updated_at']),
            models.Index(fields=['tenant', 'last_activity']),
        ]

    def update_message_count(self):
        """Update denormalized message count"""
        self.total_messages = self.messages.count()
        self.save(update_fields=['total_messages'])

    def __str__(self):
        return self.title or f"Conversation {self.id}"

    def save(self, *args, **kwargs):
        if not self.title and self.pk:  # Only for existing conversations
            # Set the title to the first user message
            first_message = self.messages.filter(is_user=True).first()
            if first_message:
                self.title = first_message.content[:50]
        super().save(*args, **kwargs)


class Message(models.Model):
    """
    Model to store messages in a conversation with enhanced validation.
    """
    conversation = models.ForeignKey(
        Conversation, on_delete=models.CASCADE, related_name="messages"
    )
    content = models.TextField()
    is_user = models.BooleanField(default=True, db_index=True)  # Added index
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)  # Added index

    # Fixed: Made these fields required for assistant messages
    search_query = models.ForeignKey(
        SearchQuery,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="messages",
    )
    search_result = models.ForeignKey(
        SearchResult,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="messages",
    )

    # Enhanced message metadata
    processing_time = models.FloatField(null=True, blank=True)
    token_count = models.IntegerField(null=True, blank=True)
    edit_count = models.IntegerField(default=0)
    last_edited = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ["conversation", "created_at"]
        indexes = [
            models.Index(fields=['conversation', 'created_at']),
            models.Index(fields=['is_user', 'created_at']),
        ]

    def clean(self):
        """Validate that assistant messages have required search data"""
        if not self.is_user and (not self.search_query or not self.search_result):
            raise ValidationError(
                "Assistant messages must have both search_query and search_result"
            )

    def save(self, *args, **kwargs):
        self.full_clean()  # Run validation
        super().save(*args, **kwargs)

        # Update conversation message count
        if self.pk:  # Only for saved messages
            self.conversation.update_message_count()

    def __str__(self):
        return f"{'User' if self.is_user else 'Assistant'}: {self.content[:50]}..."
