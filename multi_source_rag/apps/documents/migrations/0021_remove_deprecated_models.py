# Generated by Django 4.2.10 on 2025-05-29 10:03

from django.db import migrations


class Migration(migrations.Migration):
    dependencies = [
        ("documents", "0020_add_content_optimization_fields"),
    ]

    operations = [
        # Remove deprecated RelatedChunk model
        migrations.DeleteModel(
            name='RelatedChunk',
        ),

        # Remove unused fields from DocumentChunk
        migrations.RemoveField(
            model_name='documentchunk',
            name='importance_score',
        ),
        migrations.RemoveField(
            model_name='documentchunk',
            name='technical_entities',
        ),
    ]
