# Generated migration for performance optimization indexes

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('documents', '0023_remove_documentcomplexityprofile_unique_complexity_profile_per_document_tenant_and_more'),
    ]

    operations = [
        # Add composite indexes for common queries
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_document_tenant_type ON documents_rawdocument (tenant_id, content_type);",
            reverse_sql="DROP INDEX IF EXISTS idx_document_tenant_type;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_chunk_document_quality ON documents_documentchunk (document_id, is_high_quality);",
            reverse_sql="DROP INDEX IF EXISTS idx_chunk_document_quality;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_document_source_tenant ON documents_rawdocument (source_id, tenant_id);",
            reverse_sql="DROP INDEX IF EXISTS idx_document_source_tenant;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_chunk_vector_id ON documents_documentchunk (vector_id);",
            reverse_sql="DROP INDEX IF EXISTS idx_chunk_vector_id;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_document_created_at ON documents_rawdocument (created_at DESC);",
            reverse_sql="DROP INDEX IF EXISTS idx_document_created_at;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_document_external_id ON documents_rawdocument (external_id);",
            reverse_sql="DROP INDEX IF EXISTS idx_document_external_id;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_domain_metadata_document ON documents_documentdomainmetadata (document_id);",
            reverse_sql="DROP INDEX IF EXISTS idx_domain_metadata_document;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_complexity_profile_document ON documents_documentcomplexityprofile (document_id);",
            reverse_sql="DROP INDEX IF EXISTS idx_complexity_profile_document;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_document_content_document ON documents_documentcontent (document_id);",
            reverse_sql="DROP INDEX IF EXISTS idx_document_content_document;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_embedding_metadata_chunk ON documents_embeddingmetadata (chunk_id);",
            reverse_sql="DROP INDEX IF EXISTS idx_embedding_metadata_chunk;"
        ),
    ]
