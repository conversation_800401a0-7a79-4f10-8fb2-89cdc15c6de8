# Generated migration for Advanced RAG enhancements

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('documents', '0021_remove_deprecated_models'),
    ]

    operations = [
        # Add new fields to DocumentContent for file-level retrieval
        migrations.AddField(
            model_name='documentcontent',
            name='file_level_embedding_id',
            field=models.CharField(
                blank=True,
                help_text='UUID for file-level embedding in vector database',
                max_length=255,
                null=True
            ),
        ),
        migrations.AddField(
            model_name='documentcontent',
            name='file_summary',
            field=models.TextField(
                blank=True,
                help_text='File-level summary for metadata retrieval',
                null=True
            ),
        ),
        migrations.AddField(
            model_name='documentcontent',
            name='file_keywords',
            field=models.J<PERSON>NField(
                default=list,
                help_text='Keywords extracted for file-level search'
            ),
        ),
        migrations.AddField(
            model_name='documentcontent',
            name='domain_specific_content',
            field=models.J<PERSON><PERSON>ield(
                default=dict,
                help_text='Domain-specific content variations'
            ),
        ),
        migrations.AddField(
            model_name='documentcontent',
            name='cross_domain_links',
            field=models.JSONField(
                default=list,
                help_text='Links to related content in other domains'
            ),
        ),
        
        # Create DocumentDomainMetadata model
        migrations.CreateModel(
            name='DocumentDomainMetadata',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('primary_domain', models.CharField(
                    help_text='Primary data domain (e.g., GITHUB_CODE, SLACK_ENGINEERING)',
                    max_length=50
                )),
                ('domain_confidence', models.FloatField(
                    help_text='Confidence score for domain classification (0.0-1.0)'
                )),
                ('secondary_domains', models.JSONField(
                    default=list,
                    help_text='List of secondary domains for cross-domain routing'
                )),
                ('routing_metadata', models.JSONField(
                    default=dict,
                    help_text='Metadata for intelligent query routing and enhancement'
                )),
                ('cross_domain_references', models.JSONField(
                    default=list,
                    help_text='References to related content in other domains'
                )),
                ('document', models.ForeignKey(
                    on_delete=django.db.models.deletion.CASCADE,
                    related_name='domain_metadata',
                    to='documents.rawdocument'
                )),
                ('tenant', models.ForeignKey(
                    on_delete=django.db.models.deletion.CASCADE,
                    to='accounts.tenant'
                )),
            ],
            options={
                'app_label': 'documents',
            },
        ),
        
        # Create DocumentComplexityProfile model
        migrations.CreateModel(
            name='DocumentComplexityProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('semantic_complexity', models.FloatField(
                    help_text='Semantic complexity score (0.0-1.0)'
                )),
                ('domain_complexity', models.FloatField(
                    help_text='Domain-specific complexity score (0.0-1.0)'
                )),
                ('temporal_complexity', models.FloatField(
                    help_text='Temporal complexity score (0.0-1.0)'
                )),
                ('structural_complexity', models.FloatField(
                    help_text='Structural complexity score (0.0-1.0)'
                )),
                ('contextual_complexity', models.FloatField(
                    help_text='Contextual complexity score (0.0-1.0)'
                )),
                ('overall_complexity_level', models.CharField(
                    choices=[
                        ('SIMPLE', 'Simple'),
                        ('MODERATE', 'Moderate'),
                        ('COMPLEX', 'Complex'),
                        ('ADAPTIVE', 'Adaptive'),
                    ],
                    help_text='Overall complexity classification',
                    max_length=20
                )),
                ('strategy_recommendations', models.JSONField(
                    default=dict,
                    help_text='Recommended retrieval strategies based on complexity'
                )),
                ('performance_hints', models.JSONField(
                    default=dict,
                    help_text='Performance optimization hints for retrieval'
                )),
                ('document', models.ForeignKey(
                    on_delete=django.db.models.deletion.CASCADE,
                    related_name='complexity_profile',
                    to='documents.rawdocument'
                )),
                ('tenant', models.ForeignKey(
                    on_delete=django.db.models.deletion.CASCADE,
                    to='accounts.tenant'
                )),
            ],
            options={
                'app_label': 'documents',
            },
        ),
        
        # Add indexes for performance
        migrations.AddIndex(
            model_name='documentdomainmetadata',
            index=models.Index(fields=['primary_domain'], name='domain_primary_idx'),
        ),
        migrations.AddIndex(
            model_name='documentdomainmetadata',
            index=models.Index(fields=['domain_confidence'], name='domain_confidence_idx'),
        ),
        migrations.AddIndex(
            model_name='documentcomplexityprofile',
            index=models.Index(fields=['overall_complexity_level'], name='complexity_level_idx'),
        ),
        migrations.AddIndex(
            model_name='documentcomplexityprofile',
            index=models.Index(fields=['semantic_complexity'], name='semantic_complexity_idx'),
        ),
        
        # Add unique constraints
        migrations.AddConstraint(
            model_name='documentdomainmetadata',
            constraint=models.UniqueConstraint(
                fields=['document', 'tenant'],
                name='unique_domain_metadata_per_document_tenant'
            ),
        ),
        migrations.AddConstraint(
            model_name='documentcomplexityprofile',
            constraint=models.UniqueConstraint(
                fields=['document', 'tenant'],
                name='unique_complexity_profile_per_document_tenant'
            ),
        ),
    ]
