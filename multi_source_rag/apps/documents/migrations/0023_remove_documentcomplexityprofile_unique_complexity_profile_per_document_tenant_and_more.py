# Generated by Django 4.2.10 on 2025-05-30 08:24

from django.db import migrations


class Migration(migrations.Migration):
    dependencies = [
        ("accounts", "0006_alter_userplatformprofile_user"),
        ("documents", "0022_add_advanced_rag_models"),
    ]

    operations = [
        migrations.RemoveConstraint(
            model_name="documentcomplexityprofile",
            name="unique_complexity_profile_per_document_tenant",
        ),
        migrations.RemoveConstraint(
            model_name="documentdomainmetadata",
            name="unique_domain_metadata_per_document_tenant",
        ),
        migrations.AlterUniqueTogether(
            name="documentcomplexityprofile",
            unique_together={("document", "tenant")},
        ),
        migrations.AlterUniqueTogether(
            name="documentdomainmetadata",
            unique_together={("document", "tenant")},
        ),
    ]
