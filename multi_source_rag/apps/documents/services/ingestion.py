"""
Production Document Ingestion Service

This service provides enterprise-grade document ingestion with intelligent processing:
- Advanced ingestion pipelines for all document processing
- Intelligent node parsers for optimal chunking
- Automatic metadata extraction and enrichment
- Specialized pipelines for different content types
- Production-ready error handling and monitoring
- Technology-agnostic design with pluggable backends
"""

import logging
from typing import Any, Dict, List, Optional, Tuple, Union
from django.utils import timezone

from django.db import transaction
from django.contrib.auth.models import User

# LlamaIndex imports
from llama_index.core import Document
from llama_index.core.ingestion import IngestionPipeline
from llama_index.core.node_parser import (
    SentenceSplitter,
    CodeSplitter,
    SemanticSplitterNodeParser
)
# Removed LLM-dependent extractors to work without OpenAI API key
# from llama_index.core.extractors import (
#     TitleExtractor,
#     KeywordExtractor,
#     SummaryExtractor,
#     QuestionsAnsweredExtractor
# )

# App imports
from apps.accounts.models import Tenant
from apps.core.utils.collection_manager import get_collection_name
from apps.core.utils.llama_index_embeddings import get_embedding_model_for_content
from apps.core.utils.llama_index_vectorstore import get_vector_store
from apps.core.utils.chunking_strategies import (
    get_chunking_strategy_info,
    create_skip_chunking_pipeline
)
from apps.core.utils.slack_metadata_enhancer import enhance_slack_metadata
from apps.documents.interfaces.factory import DocumentSourceFactory

# Enhanced RAG services
from apps.core.services.enhanced_github_ingestion import EnhancedGitHubIngestion
from apps.core.services.enhanced_slack_ingestion import EnhancedSlackIngestion
from apps.documents.models import (
    DocumentChunk,
    DocumentProcessingJob,
    DocumentSource,
    RawDocument,
    DocumentDomainMetadata,
    DocumentComplexityProfile,
)
# from apps.documents.processors.conversation_node_parser import ConversationAwareNodeParser  # Temporarily disabled

logger = logging.getLogger(__name__)


class IngestionService:
    """
    Production-ready LlamaIndex ingestion service that replaces all custom logic.

    Features:
    - Specialized ingestion pipelines for different content types
    - Advanced chunking with LlamaIndex node parsers
    - Automatic metadata extraction and enrichment
    - Comprehensive error handling and monitoring
    - Data integrity guarantees
    """

    def __init__(self, tenant: Union[Tenant, str], user: Optional[User] = None):
        """
        Initialize the unified ingestion service.

        Args:
            tenant: Tenant object or tenant slug
            user: User performing the ingestion
        """
        if isinstance(tenant, str):
            try:
                self.tenant = Tenant.objects.get(slug=tenant)
            except Tenant.DoesNotExist:
                raise ValueError(f"Tenant with slug '{tenant}' not found")
        else:
            self.tenant = tenant

        self.user = user
        self.tenant_slug = self.tenant.slug

        # Initialize LlamaIndex models
        self._initialize_models()

        # Initialize specialized pipelines
        self.pipelines = self._build_content_pipelines()

        # Initialize enhanced ingestion services
        self.enhanced_github = EnhancedGitHubIngestion()
        self.enhanced_slack = EnhancedSlackIngestion()

        # Initialize advanced RAG services (consolidated from enhanced service)
        try:
            from apps.core.services.domain_classification import DomainClassificationService
            from apps.core.services.complexity_analysis import ComplexityAnalysisEngine
            from apps.core.services.file_content_processor import FileContentProcessor

            self.domain_classifier = DomainClassificationService()
            self.complexity_analyzer = ComplexityAnalysisEngine()
            self.file_processor = FileContentProcessor()
            logger.info("✅ Advanced RAG services initialized")
        except ImportError as e:
            logger.warning(f"Advanced RAG services not available: {e}")
            self.domain_classifier = None
            self.complexity_analyzer = None
            self.file_processor = None

        # Processing statistics
        self.stats = {
            "documents_processed": 0,
            "documents_failed": 0,
            "chunks_created": 0,
            "processing_start_time": None,
            "processing_end_time": None
        }

    def _initialize_models(self) -> None:
        """Initialize LlamaIndex models (LLM and embeddings)."""
        try:
            # Initialize embedding models with consistency
            from apps.core.utils.llama_index_embeddings import initialize_embedding_models
            from apps.core.utils.embedding_consistency import set_global_embedding_model

            initialize_embedding_models()
            set_global_embedding_model()
            logger.info("Initialized consistent embedding models")

            # Initialize LLM models
            from apps.core.utils.llama_index_llm import initialize_llms
            initialize_llms()
            logger.info("Initialized LLM models")

        except Exception as e:
            logger.error(f"Failed to initialize models: {str(e)}")
            # Continue without models - will use fallback processing
            pass

    def _build_content_pipelines(self) -> Dict[str, IngestionPipeline]:
        """
        Build specialized ingestion pipelines for different content types.

        Returns:
            Dictionary of content type to IngestionPipeline mappings
        """
        pipelines = {}

        # Conversation pipeline (Slack, chat, etc.)
        pipelines["conversation"] = self._build_conversation_pipeline()

        # Code pipeline (GitHub, code files)
        pipelines["code"] = self._build_code_pipeline()

        # Document pipeline (general documents, markdown, etc.)
        pipelines["document"] = self._build_document_pipeline()

        return pipelines

    def _build_conversation_pipeline(self) -> IngestionPipeline:
        """Build specialized pipeline for conversation data with conversation-aware parsing."""
        collection_name = get_collection_name(self.tenant_slug, intent="conversation")
        vector_store = get_vector_store(collection_name=collection_name)
        embed_model = get_embedding_model_for_content(content_type="conversation")

        return IngestionPipeline(
            transformations=[
                SentenceSplitter(
                    chunk_size=2048,
                    chunk_overlap=200,
                    separator="\n\n"  # Use double newlines to preserve conversation boundaries
                ),
                # Removed LLM-dependent extractors to work without OpenAI API key
                # TitleExtractor(nodes=5),
                # KeywordExtractor(keywords=10),
                # QuestionsAnsweredExtractor(questions=3),
                embed_model,
            ],
            vector_store=vector_store
        )

    def _build_conversation_pipeline_fallback(self) -> IngestionPipeline:
        """Build fallback conversation pipeline using standard sentence splitter."""
        collection_name = get_collection_name(self.tenant_slug, intent="conversation")
        vector_store = get_vector_store(collection_name=collection_name)
        embed_model = get_embedding_model_for_content(content_type="conversation")

        # Create docstore for BM25 hybrid search support
        from llama_index.core.storage.docstore import SimpleDocumentStore
        from llama_index.core.storage import StorageContext

        docstore = SimpleDocumentStore()
        storage_context = StorageContext.from_defaults(
            vector_store=vector_store,
            docstore=docstore
        )

        return IngestionPipeline(
            transformations=[
                SentenceSplitter(
                    chunk_size=1024,
                    chunk_overlap=200,
                    separator=" "
                ),
                embed_model,
            ],
            storage_context=storage_context  # Use storage_context instead of just vector_store
        )

    def _build_code_pipeline(self) -> IngestionPipeline:
        """Build specialized pipeline for code content."""
        collection_name = get_collection_name(self.tenant_slug, intent="code")
        vector_store = get_vector_store(collection_name=collection_name)
        embed_model = get_embedding_model_for_content(content_type="code")

        # Create docstore for BM25 hybrid search support
        from llama_index.core.storage.docstore import SimpleDocumentStore
        from llama_index.core.storage import StorageContext

        docstore = SimpleDocumentStore()
        storage_context = StorageContext.from_defaults(
            vector_store=vector_store,
            docstore=docstore
        )

        # Use a generic code splitter that will be configured per document
        return IngestionPipeline(
            transformations=[
                SentenceSplitter(  # Will be replaced with CodeSplitter per document
                    chunk_size=2000,
                    chunk_overlap=200,
                    separator="\n"
                ),
                # Removed LLM-dependent extractors to work without OpenAI API key
                # TitleExtractor(),
                # KeywordExtractor(keywords=15),
                # SummaryExtractor(summaries=["prev", "self"]),
                embed_model,
            ],
            storage_context=storage_context  # Use storage_context instead of just vector_store
        )

    def _build_document_pipeline(self) -> IngestionPipeline:
        """Build specialized pipeline for general documents."""
        collection_name = get_collection_name(self.tenant_slug, intent="document")
        vector_store = get_vector_store(collection_name=collection_name)
        embed_model = get_embedding_model_for_content(content_type="document")

        # Create docstore for BM25 hybrid search support
        from llama_index.core.storage.docstore import SimpleDocumentStore
        from llama_index.core.storage import StorageContext

        docstore = SimpleDocumentStore()
        storage_context = StorageContext.from_defaults(
            vector_store=vector_store,
            docstore=docstore
        )

        return IngestionPipeline(
            transformations=[
                SemanticSplitterNodeParser(
                    embed_model=embed_model,
                    buffer_size=1,
                    breakpoint_percentile_threshold=95
                ),
                # Removed LLM-dependent extractors to work without OpenAI API key
                # TitleExtractor(),
                # KeywordExtractor(),
                # SummaryExtractor(),
                embed_model,
            ],
            storage_context=storage_context  # Use storage_context instead of just vector_store
        )

    def process_source(
        self,
        source: DocumentSource,
        job: Optional[DocumentProcessingJob] = None,
        batch_size: int = 100,
        **kwargs
    ) -> Tuple[int, int]:
        """
        Process a document source using LlamaIndex pipelines.

        Args:
            source: Document source to process
            job: Processing job to update
            batch_size: Number of documents to process in a batch
            **kwargs: Additional arguments for processing

        Returns:
            Tuple of (processed_count, failed_count)
        """
        self.stats["processing_start_time"] = timezone.now()

        # Update job status
        if job:
            job.status = "processing"
            job.started_at = self.stats["processing_start_time"]
            job.save()

        try:
            # Create source interface
            source_interface = DocumentSourceFactory.create_interface(
                source.source_type, source.config
            )

            # Fetch documents
            documents = source_interface.fetch_documents(**kwargs)

            # Process documents in batches
            processed = 0
            failed = 0
            total_documents = len(documents)

            for i in range(0, total_documents, batch_size):
                batch = documents[i:i + batch_size]
                batch_processed, batch_failed = self._process_document_batch(
                    source, batch, job
                )

                processed += batch_processed
                failed += batch_failed

                # Update job progress
                if job:
                    job.documents_processed = processed
                    job.documents_failed = failed
                    job.save()

                logger.info(f"Batch {i//batch_size + 1}: {batch_processed} processed, {batch_failed} failed")

            # Update final statistics
            self.stats["documents_processed"] = processed
            self.stats["documents_failed"] = failed
            self.stats["processing_end_time"] = timezone.now()

            # Update job status
            if job:
                job.status = "completed"
                job.completed_at = self.stats["processing_end_time"]
                job.documents_processed = processed
                job.documents_failed = failed
                job.save()

            # Update source last synced
            source.last_synced = self.stats["processing_end_time"]
            source.save()

            logger.info(f"Processing completed: {processed} processed, {failed} failed")
            return processed, failed

        except Exception as e:
            self.stats["processing_end_time"] = timezone.now()

            if job:
                job.status = "failed"
                job.error_message = str(e)
                job.completed_at = self.stats["processing_end_time"]
                job.save()

            logger.error(f"Processing failed: {str(e)}")
            raise

    def process_document(
        self,
        document: Dict[str, Any],
        source: DocumentSource
    ) -> Tuple[RawDocument, List[DocumentChunk]]:
        """
        Process a single document and return the raw document and chunks.

        Args:
            document: Document data to process
            source: Document source

        Returns:
            Tuple of (RawDocument, List[DocumentChunk])
        """
        try:
            with transaction.atomic():
                raw_doc = self._process_single_document(source, document)
                # Get the chunks that were created
                chunks = list(DocumentChunk.objects.filter(document=raw_doc))
                return raw_doc, chunks
        except Exception as e:
            logger.error(f"Failed to process document {document.get('id', 'unknown')}: {e}")
            raise

    def _process_document_batch(
        self,
        source: DocumentSource,
        documents: List[Dict[str, Any]],
        job: Optional[DocumentProcessingJob] = None
    ) -> Tuple[int, int]:
        """
        Process a batch of documents using LlamaIndex pipelines.

        Args:
            source: Document source
            documents: List of documents to process
            job: Processing job for error logging

        Returns:
            Tuple of (processed_count, failed_count)
        """
        processed = 0
        failed = 0

        for document in documents:
            try:
                with transaction.atomic():
                    self._process_single_document(source, document)
                processed += 1
            except Exception as e:
                failed += 1
                error_msg = f"Error processing document {document.get('id', 'unknown')}: {str(e)}"
                logger.error(error_msg)

                if job:
                    if job.error_message:
                        job.error_message += f"\n{error_msg}"
                    else:
                        job.error_message = error_msg
                    job.save(update_fields=["error_message"])

        return processed, failed

    def _process_single_document(
        self,
        source: DocumentSource,
        document: Dict[str, Any]
    ) -> RawDocument:
        """
        Process a single document using LlamaIndex pipeline.

        Args:
            source: Document source
            document: Document data

        Returns:
            Processed RawDocument
        """
        # Validate document
        if not document.get("id"):
            raise ValueError("Document must have an 'id' field")

        content = document.get("content", "")
        if not content or not content.strip():
            raise ValueError("Document must have non-empty content")

        # Enhance metadata for Slack sources before processing
        if source.source_type in ["slack", "local_slack"]:
            document = enhance_slack_metadata(document, source.source_type)
            # Apply enhanced Slack ingestion
            document = self.enhanced_slack.enhance_slack_document(document)
            logger.debug(f"Enhanced Slack metadata for document {document.get('id', 'unknown')}")

        # Enhance metadata for GitHub sources
        elif source.source_type == "github":
            document = self.enhanced_github.enhance_github_document(document)
            logger.debug(f"Enhanced GitHub metadata for document {document.get('id', 'unknown')}")

        # Determine content type and chunking strategy
        content_type = self._determine_content_type(document, source)

        # Check if this is a token-based pre-chunked document (from new 500-token strategy)
        is_token_based = document.get("metadata", {}).get("chunking_strategy") == "token_based_500"

        # Check if chunking should be skipped for this source type
        chunking_info = get_chunking_strategy_info(source.source_type)
        skip_chunking = chunking_info["skip_chunking"] or is_token_based

        logger.info(f"Processing document {document.get('id', 'unknown')} with strategy: {chunking_info['strategy']} (token_based: {is_token_based})")

        if skip_chunking:
            # Use skip-chunking pipeline for pre-optimized sources like Slack or token-based chunks
            pipeline = create_skip_chunking_pipeline(self.tenant_slug, content_type)
            if is_token_based:
                logger.info(f"Using skip-chunking pipeline for token-based document from: {source.source_type}")
            else:
                logger.info(f"Using skip-chunking pipeline for source type: {source.source_type}")
        else:
            # Use traditional chunking pipelines
            if content_type == "code":
                language = self._detect_programming_language(content, document.get("metadata", {}))
                pipeline = self._create_dynamic_code_pipeline(language)
                logger.info(f"Using dynamic code pipeline for language: {language}")
            else:
                pipeline = self._get_pipeline_for_content_type(content_type)

        # Create or update raw document
        raw_doc = self._create_or_update_raw_document(source, document, content_type)

        # Convert to LlamaIndex document
        llama_doc = Document(
            text=content,
            metadata={
                "document_id": str(raw_doc.id),
                "source_id": str(source.id),
                "source_type": source.source_type,
                "content_type": content_type,
                "tenant_id": str(self.tenant.id),
                **document.get("metadata", {})
            }
        )

        # Process with LlamaIndex pipeline with error handling
        try:
            nodes = pipeline.run(documents=[llama_doc])

            if not nodes:
                logger.warning(f"Pipeline returned no nodes for document {raw_doc.external_id}")
                # Create a fallback chunk to ensure data integrity
                nodes = self._create_fallback_nodes(llama_doc)

        except Exception as e:
            logger.error(f"Pipeline processing failed for document {raw_doc.external_id}: {str(e)}")

            # For conversation content, try fallback pipeline
            if content_type == "conversation":
                try:
                    logger.info(f"Trying fallback conversation pipeline for document {raw_doc.external_id}")
                    fallback_pipeline = self._build_conversation_pipeline_fallback()
                    nodes = fallback_pipeline.run(documents=[llama_doc])

                    if not nodes:
                        nodes = self._create_fallback_nodes(llama_doc)

                except Exception as fallback_error:
                    logger.error(f"Fallback pipeline also failed: {str(fallback_error)}")
                    nodes = self._create_fallback_nodes(llama_doc)
            else:
                # Create fallback nodes to prevent data loss
                nodes = self._create_fallback_nodes(llama_doc)

        # Store nodes as document chunks
        try:
            self._store_nodes_as_chunks(raw_doc, nodes, skip_chunking=skip_chunking, is_token_based=is_token_based)
        except Exception as e:
            logger.error(f"Failed to store chunks for document {raw_doc.external_id}: {str(e)}")
            raise ValueError(f"Failed to store document chunks: {str(e)}")

        # Create advanced metadata for agentic search features
        try:
            self._create_advanced_metadata(raw_doc, document, content_type)
        except Exception as e:
            logger.warning(f"Failed to create advanced metadata for document {raw_doc.external_id}: {str(e)}")
            # Don't fail the entire ingestion for metadata issues

        return raw_doc

    def _create_fallback_nodes(self, llama_doc: Document) -> List[Any]:
        """
        Create fallback nodes when pipeline processing fails.

        Args:
            llama_doc: LlamaIndex document

        Returns:
            List of fallback nodes
        """
        from llama_index.core.schema import TextNode

        # Simple text splitting as fallback
        text = llama_doc.text
        chunk_size = 1000
        chunks = []

        for i in range(0, len(text), chunk_size):
            chunk_text = text[i:i + chunk_size]
            node = TextNode(
                text=chunk_text,
                metadata={
                    **llama_doc.metadata,
                    "fallback_chunk": True,
                    "chunk_index": len(chunks),
                    "processing_method": "fallback_splitting"
                }
            )
            chunks.append(node)

        logger.info(f"Created {len(chunks)} fallback nodes")
        return chunks

    def _detect_programming_language(self, content: str, metadata: Dict[str, Any]) -> str:
        """
        Detect programming language from content and metadata.

        Args:
            content: Document content
            metadata: Document metadata

        Returns:
            Detected programming language
        """
        # Check file extension first
        filename = metadata.get("filename", "")
        if filename:
            ext = filename.split(".")[-1].lower()
            language_map = {
                "py": "python",
                "js": "javascript",
                "ts": "typescript",
                "java": "java",
                "cpp": "cpp",
                "c": "c",
                "cs": "csharp",
                "go": "go",
                "rs": "rust",
                "rb": "ruby",
                "php": "php",
                "swift": "swift",
                "kt": "kotlin",
                "scala": "scala",
                "sh": "bash",
                "sql": "sql",
                "html": "html",
                "css": "css",
                "xml": "xml",
                "json": "json",
                "yaml": "yaml",
                "yml": "yaml"
            }
            if ext in language_map:
                return language_map[ext]

        # Check content patterns
        if "def " in content and "import " in content:
            return "python"
        elif "function " in content and ("var " in content or "let " in content or "const " in content):
            return "javascript"
        elif "public class " in content and "static void main" in content:
            return "java"
        elif "#include" in content and ("int main" in content or "void main" in content):
            return "cpp"
        elif "package main" in content and "func main" in content:
            return "go"
        elif "fn main" in content and "use " in content:
            return "rust"

        # Default to python for unknown code
        return "python"

    def _create_dynamic_code_pipeline(self, language: str) -> IngestionPipeline:
        """
        Create a dynamic code pipeline based on detected language.

        Args:
            language: Programming language

        Returns:
            IngestionPipeline configured for the language
        """
        collection_name = get_collection_name(self.tenant_slug, intent="code")
        vector_store = get_vector_store(collection_name=collection_name)
        embed_model = get_embedding_model_for_content(content_type="code")

        # Create docstore for BM25 hybrid search support
        from llama_index.core.storage.docstore import SimpleDocumentStore
        from llama_index.core.storage import StorageContext

        docstore = SimpleDocumentStore()
        storage_context = StorageContext.from_defaults(
            vector_store=vector_store,
            docstore=docstore
        )

        return IngestionPipeline(
            transformations=[
                CodeSplitter(
                    language=language,
                    chunk_lines=40,
                    chunk_overlap=15
                ),
                # Removed LLM-dependent extractors to work without OpenAI API key
                # TitleExtractor(),
                # KeywordExtractor(keywords=15),
                # SummaryExtractor(summaries=["prev", "self"]),
                embed_model,
            ],
            storage_context=storage_context  # Use storage_context instead of just vector_store
        )

    def _determine_content_type(
        self,
        document: Dict[str, Any],
        source: DocumentSource
    ) -> str:
        """
        Determine the content type for pipeline selection.

        Args:
            document: Document data
            source: Document source

        Returns:
            Content type string
        """
        # Check explicit content type
        if "content_type" in document:
            content_type = document["content_type"]
            if "slack" in content_type or "conversation" in content_type:
                return "conversation"
            elif "code" in content_type or "python" in content_type:
                return "code"
            else:
                return "document"

        # Infer from source type
        if source.source_type in ["slack", "local_slack"]:
            return "conversation"
        elif source.source_type in ["github", "local_github"]:
            return "code"
        else:
            return "document"

    def _get_pipeline_for_content_type(self, content_type: str) -> IngestionPipeline:
        """
        Get the appropriate pipeline for content type.

        Args:
            content_type: Content type

        Returns:
            IngestionPipeline for the content type
        """
        return self.pipelines.get(content_type, self.pipelines["document"])

    def _create_or_update_raw_document(
        self,
        source: DocumentSource,
        document: Dict[str, Any],
        content_type: str
    ) -> RawDocument:
        """
        Create or update a raw document record.

        Args:
            source: Document source
            document: Document data
            content_type: Determined content type

        Returns:
            RawDocument instance
        """
        external_id = document["id"]
        content = document.get("content", "")
        title = document.get("title", "Untitled Document")
        metadata = document.get("metadata", {})

        # Generate content hash for idempotency
        import hashlib
        content_hash = hashlib.md5(
            f"{content}|{title}|{str(metadata)}".encode()
        ).hexdigest()

        try:
            # Try to get existing document
            raw_doc = RawDocument.objects.get(
                tenant=self.tenant,
                source=source,
                external_id=external_id
            )

            # Check if content changed
            if raw_doc.content_hash == content_hash:
                logger.debug(f"Document {external_id} unchanged, skipping")
                return raw_doc

            # Update existing document
            raw_doc.title = title
            raw_doc.permalink = document.get("url")
            raw_doc.content_hash = content_hash
            raw_doc.content_type = content_type
            raw_doc.metadata = metadata
            raw_doc.save()

            # Update or create document content
            from apps.documents.models import DocumentContent
            content_obj, created = DocumentContent.objects.get_or_create(
                document=raw_doc,
                defaults={
                    'content': content,
                    'content_format': 'slack' if content_type == 'conversation' else 'text'
                }
            )
            if not created:
                content_obj.content = content
                content_obj.save()

        except RawDocument.DoesNotExist:
            # Create new document
            raw_doc = RawDocument.objects.create(
                tenant=self.tenant,
                source=source,
                external_id=external_id,
                title=title,
                permalink=document.get("url"),
                content_hash=content_hash,
                content_type=content_type,
                metadata=metadata
            )

            # Create document content
            from apps.documents.models import DocumentContent
            DocumentContent.objects.create(
                document=raw_doc,
                content=content,
                content_format='slack' if content_type == 'conversation' else 'text'
            )

        return raw_doc

    def _store_nodes_as_chunks(
        self,
        raw_doc: RawDocument,
        nodes: List[Any],
        skip_chunking: bool = False,
        is_token_based: bool = False
    ) -> List[DocumentChunk]:
        """
        Store LlamaIndex nodes as DocumentChunk records and create corresponding EmbeddingMetadata.

        Args:
            raw_doc: Raw document
            nodes: LlamaIndex nodes
            skip_chunking: Whether chunking was skipped (affects chunk naming and metadata)

        Returns:
            List of created DocumentChunk instances
        """
        from apps.documents.models import EmbeddingMetadata

        # Delete existing chunks and their embeddings for this document
        DocumentChunk.objects.filter(document=raw_doc).delete()

        chunks = []
        embedding_metadatas = []

        for i, node in enumerate(nodes):
            # Create chunk metadata with chunking strategy information
            chunk_metadata = {
                "node_id": node.node_id,
                "total_chunks": len(nodes),
                "chunking_skipped": skip_chunking,
                **node.metadata
            }

            # For skip-chunking, mark as complete document
            if skip_chunking:
                chunk_metadata.update({
                    "is_complete_document": True,
                    "chunking_strategy": "skip_chunking",
                    "pre_optimized": True
                })

                # Add token-based specific metadata if present
                if is_token_based:
                    chunk_metadata.update({
                        "is_token_based": True,
                        "original_chunking_strategy": "token_based_500",
                        "has_overlap": raw_doc.metadata.get("has_overlap", False),
                        "estimated_tokens": raw_doc.metadata.get("estimated_tokens", 0),
                        "max_tokens": raw_doc.metadata.get("max_tokens", 500)
                    })
            else:
                chunk_metadata.update({
                    "is_complete_document": False,
                    "chunking_strategy": "llama_index_pipeline"
                })

            chunk = DocumentChunk.objects.create(
                tenant=self.tenant,
                document=raw_doc,
                text=node.text,
                chunk_index=i,
                metadata=chunk_metadata
            )
            chunks.append(chunk)

            # Create EmbeddingMetadata for this chunk using consistent model info
            # The node_id from LlamaIndex is used as the vector_id in Qdrant
            from apps.core.utils.embedding_consistency import get_embedding_model_info

            model_info = get_embedding_model_info()
            embedding_metadata = EmbeddingMetadata(
                chunk=chunk,
                vector_id=node.node_id,
                model_name=model_info.get("model_name", "BAAI/bge-base-en-v1.5"),
                vector_dimensions=model_info.get("dimensions", 768),
                is_synced=True  # LlamaIndex pipeline already stored it in Qdrant
            )
            embedding_metadatas.append(embedding_metadata)

        # Bulk create embedding metadata
        if embedding_metadatas:
            EmbeddingMetadata.objects.bulk_create(embedding_metadatas)
            logger.debug(f"Created {len(embedding_metadatas)} embedding metadata records")

        self.stats["chunks_created"] += len(chunks)

        # Log chunking strategy results
        if skip_chunking:
            if is_token_based:
                logger.info(f"Token-based chunking: Created {len(chunks)} pre-optimized 500-token chunks for {raw_doc.external_id}")
            else:
                logger.info(f"Skip-chunking: Created {len(chunks)} complete document chunks for {raw_doc.external_id}")
        else:
            logger.debug(f"Standard chunking: Created {len(chunks)} chunks for document {raw_doc.external_id}")

        return chunks

    def _create_advanced_metadata(self, raw_doc: RawDocument, document: Dict[str, Any], content_type: str) -> None:
        """
        Create advanced metadata for agentic search features.

        Args:
            raw_doc: The raw document
            document: Original document data
            content_type: Determined content type
        """
        try:
            # Extract enhanced metadata from document
            metadata = document.get("metadata", {})

            # Create DocumentDomainMetadata
            domain = metadata.get("data_domain", self._determine_domain_from_content_type(content_type))

            # Prepare routing metadata with additional fields
            routing_metadata = metadata.get("routing_metadata", {})
            routing_metadata.update({
                "query_patterns": metadata.get("query_patterns", []),
                "workflow_context": metadata.get("workflow_context", {}),
                "technical_level": metadata.get("technical_level", 0.5)
            })

            # Get or create domain metadata
            domain_metadata, created = DocumentDomainMetadata.objects.get_or_create(
                document=raw_doc,
                tenant=self.tenant,
                defaults={
                    "primary_domain": domain,
                    "secondary_domains": metadata.get("cross_domain_hints", []),
                    "domain_confidence": metadata.get("domain_confidence", 0.8),
                    "cross_domain_references": metadata.get("cross_references", []),
                    "routing_metadata": routing_metadata
                }
            )

            if not created:
                # Update existing metadata
                domain_metadata.primary_domain = domain
                domain_metadata.secondary_domains = metadata.get("cross_domain_hints", [])
                domain_metadata.domain_confidence = metadata.get("domain_confidence", 0.8)
                domain_metadata.cross_domain_references = metadata.get("cross_references", [])
                domain_metadata.routing_metadata = routing_metadata
                domain_metadata.save()

            # Prepare complexity data for JSON fields
            performance_hints = {
                "complexity_factors": {
                    "line_count": metadata.get("line_count", 0),
                    "function_count": metadata.get("function_count", 0),
                    "class_count": metadata.get("class_count", 0),
                    "import_count": metadata.get("import_count", 0),
                    "word_count": metadata.get("quality_metrics", {}).get("word_count", 0),
                    "has_code_examples": metadata.get("has_code_examples", False),
                    "technical_indicators": metadata.get("technical_indicators", [])
                },
                "readability_score": metadata.get("readability_score", 0.7),
                "maintenance_score": metadata.get("maintenance_score", 0.7)
            }

            strategy_recommendations = {
                "learning_curve": metadata.get("learning_curve", "medium"),
                "recommended_strategies": ["basic_retrieval"]
            }

            # Determine overall complexity level
            overall_complexity_score = metadata.get("overall_complexity", 0.5)
            if overall_complexity_score < 0.3:
                overall_level = "SIMPLE"
            elif overall_complexity_score < 0.6:
                overall_level = "MODERATE"
            elif overall_complexity_score < 0.8:
                overall_level = "COMPLEX"
            else:
                overall_level = "ADAPTIVE"

            # Create DocumentComplexityProfile
            complexity_profile, created = DocumentComplexityProfile.objects.get_or_create(
                document=raw_doc,
                tenant=self.tenant,
                defaults={
                    "semantic_complexity": metadata.get("file_complexity", metadata.get("technical_complexity", 0.5)),
                    "domain_complexity": metadata.get("domain_complexity", 0.5),
                    "temporal_complexity": metadata.get("temporal_complexity", 0.4),
                    "structural_complexity": metadata.get("structural_complexity", 0.5),
                    "contextual_complexity": metadata.get("contextual_complexity", 0.4),
                    "overall_complexity_level": overall_level,
                    "strategy_recommendations": strategy_recommendations,
                    "performance_hints": performance_hints
                }
            )

            if not created:
                # Update existing complexity profile
                complexity_profile.semantic_complexity = metadata.get("file_complexity", metadata.get("technical_complexity", 0.5))
                complexity_profile.domain_complexity = metadata.get("domain_complexity", 0.5)
                complexity_profile.temporal_complexity = metadata.get("temporal_complexity", 0.4)
                complexity_profile.structural_complexity = metadata.get("structural_complexity", 0.5)
                complexity_profile.contextual_complexity = metadata.get("contextual_complexity", 0.4)
                complexity_profile.overall_complexity_level = overall_level
                complexity_profile.strategy_recommendations = strategy_recommendations
                complexity_profile.performance_hints = performance_hints
                complexity_profile.save()

            logger.debug(f"Created/updated advanced metadata for document {raw_doc.external_id}")

        except Exception as e:
            logger.error(f"Error creating advanced metadata: {str(e)}")
            raise

    def _determine_domain_from_content_type(self, content_type: str) -> str:
        """Determine domain from content type."""
        domain_mapping = {
            "code": "GITHUB_CODE",
            "conversation": "SLACK_CONVERSATIONS",
            "document": "DOCUMENTATION",
            "github_pr": "GITHUB_ISSUES",
            "github_issue": "GITHUB_ISSUES",
            "github_wiki": "GITHUB_WIKI",
            "slack": "SLACK_CONVERSATIONS"
        }
        return domain_mapping.get(content_type, "DOCUMENTATION")

    def get_processing_stats(self) -> Dict[str, Any]:
        """
        Get processing statistics.

        Returns:
            Dictionary of processing statistics
        """
        stats = self.stats.copy()

        if stats["processing_start_time"] and stats["processing_end_time"]:
            duration = stats["processing_end_time"] - stats["processing_start_time"]
            stats["processing_duration_seconds"] = duration.total_seconds()

        return stats

    def get_chunking_strategy_summary(self, source: DocumentSource) -> Dict[str, Any]:
        """
        Get a summary of the chunking strategy for a source.

        Args:
            source: Document source

        Returns:
            Dict[str, Any]: Chunking strategy summary
        """
        chunking_info = get_chunking_strategy_info(source.source_type)

        # Get actual chunk statistics from database
        from apps.documents.models import DocumentChunk

        total_chunks = DocumentChunk.objects.filter(
            document__source=source,
            tenant=self.tenant
        ).count()

        skip_chunking_chunks = DocumentChunk.objects.filter(
            document__source=source,
            tenant=self.tenant,
            metadata__chunking_skipped=True
        ).count()

        token_based_chunks = DocumentChunk.objects.filter(
            document__source=source,
            tenant=self.tenant,
            metadata__is_token_based=True
        ).count()

        return {
            "source_type": source.source_type,
            "source_name": source.name,
            "chunking_strategy": chunking_info["strategy"],
            "skip_chunking": chunking_info["skip_chunking"],
            "total_chunks": total_chunks,
            "skip_chunking_chunks": skip_chunking_chunks,
            "token_based_chunks": token_based_chunks,
            "traditional_chunks": total_chunks - skip_chunking_chunks - token_based_chunks,
            "strategy_description": chunking_info["description"],
            "chunk_size": chunking_info["chunk_size"],
            "chunk_overlap": chunking_info["chunk_overlap"]
        }