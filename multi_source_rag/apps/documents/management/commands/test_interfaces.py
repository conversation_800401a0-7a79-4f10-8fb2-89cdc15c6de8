"""
Management command to test and use the improved document interfaces.
"""

import json
import os
from django.core.management.base import BaseCommand, CommandError
from django.conf import settings

from apps.documents.interfaces.factory import DocumentSourceFactory
from apps.documents.models import Tenant


class Command(BaseCommand):
    help = 'Test and use improved document interfaces for Slack and GitHub'

    def add_arguments(self, parser):
        parser.add_argument(
            '--source-type',
            type=str,
            choices=['slack', 'github'],
            required=True,
            help='Type of document source to test'
        )
        
        parser.add_argument(
            '--tenant',
            type=str,
            required=True,
            help='Tenant slug to associate documents with'
        )
        
        parser.add_argument(
            '--action',
            type=str,
            choices=['fetch', 'search', 'status', 'validate'],
            default='fetch',
            help='Action to perform'
        )
        
        # Slack-specific options
        parser.add_argument(
            '--slack-token',
            type=str,
            help='Slack API token'
        )
        
        parser.add_argument(
            '--slack-channels',
            type=str,
            nargs='+',
            help='Slack channel IDs to fetch from'
        )
        
        # GitHub-specific options
        parser.add_argument(
            '--github-token',
            type=str,
            help='GitHub API token'
        )
        
        parser.add_argument(
            '--github-owner',
            type=str,
            help='GitHub repository owner'
        )
        
        parser.add_argument(
            '--github-repo',
            type=str,
            help='GitHub repository name'
        )
        
        parser.add_argument(
            '--github-content-types',
            type=str,
            nargs='+',
            choices=['pull_request', 'issue', 'wiki', 'discussions'],
            default=['pull_request', 'issue'],
            help='Types of GitHub content to fetch'
        )
        
        # Common options
        parser.add_argument(
            '--days',
            type=int,
            default=7,
            help='Number of days to fetch (default: 7)'
        )
        
        parser.add_argument(
            '--max-items',
            type=int,
            default=50,
            help='Maximum number of items to fetch (default: 50)'
        )
        
        parser.add_argument(
            '--search-query',
            type=str,
            help='Search query (for search action)'
        )
        
        parser.add_argument(
            '--output-file',
            type=str,
            help='File to save results to (JSON format)'
        )
        
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without actually doing it'
        )

    def handle(self, *args, **options):
        """Handle the command execution."""
        try:
            # Get or create tenant
            tenant = self.get_tenant(options['tenant'])
            
            # Create interface
            interface = self.create_interface(options)
            
            # Perform action
            if options['action'] == 'status':
                self.show_status(interface)
            elif options['action'] == 'validate':
                self.validate_interface(interface)
            elif options['action'] == 'search':
                if not options['search_query']:
                    raise CommandError("Search query is required for search action")
                self.search_documents(interface, options)
            else:  # fetch
                self.fetch_documents(interface, options)
                
        except Exception as e:
            raise CommandError(f"Command failed: {e}")

    def get_tenant(self, tenant_slug: str):
        """Get or create tenant."""
        try:
            tenant = Tenant.objects.get(slug=tenant_slug)
            self.stdout.write(f"Using existing tenant: {tenant.name}")
            return tenant
        except Tenant.DoesNotExist:
            if not self.confirm(f"Tenant '{tenant_slug}' does not exist. Create it?"):
                raise CommandError("Tenant is required")
            
            tenant = Tenant.objects.create(
                name=tenant_slug.title(),
                slug=tenant_slug,
                description=f"Auto-created tenant for {tenant_slug}"
            )
            self.stdout.write(f"Created new tenant: {tenant.name}")
            return tenant

    def create_interface(self, options):
        """Create document source interface based on options."""
        source_type = options['source_type']
        
        if source_type == 'slack':
            return self.create_slack_interface(options)
        elif source_type == 'github':
            return self.create_github_interface(options)
        else:
            raise CommandError(f"Unsupported source type: {source_type}")

    def create_slack_interface(self, options):
        """Create Slack interface."""
        token = options.get('slack_token') or os.getenv('SLACK_API_TOKEN')
        if not token:
            raise CommandError("Slack API token is required (--slack-token or SLACK_API_TOKEN env var)")
        
        config = {
            'api_token': token,
            'channel_ids': options.get('slack_channels', []),
            'days_to_fetch': options['days'],
            'max_chunk_size': 1000,
            'chunk_overlap': 100
        }
        
        self.stdout.write(f"Creating Slack interface with {len(config['channel_ids'])} channels")
        return DocumentSourceFactory.create_interface('slack', config)

    def create_github_interface(self, options):
        """Create GitHub interface."""
        token = options.get('github_token') or os.getenv('GITHUB_TOKEN')
        owner = options.get('github_owner')
        repo = options.get('github_repo')
        
        if not token:
            raise CommandError("GitHub token is required (--github-token or GITHUB_TOKEN env var)")
        if not owner or not repo:
            raise CommandError("GitHub owner and repo are required (--github-owner and --github-repo)")
        
        config = {
            'token': token,
            'owner': owner,
            'repo': repo,
            'content_types': options['github_content_types'],
            'max_chunk_size': 2000,
            'chunk_overlap': 200
        }
        
        self.stdout.write(f"Creating GitHub interface for {owner}/{repo}")
        return DocumentSourceFactory.create_interface('github', config)

    def show_status(self, interface):
        """Show interface status."""
        status = interface.get_status()
        
        self.stdout.write("Interface Status:")
        for key, value in status.items():
            self.stdout.write(f"  {key}: {value}")
        
        features = interface.get_supported_features()
        self.stdout.write(f"  supported_features: {', '.join(features)}")

    def validate_interface(self, interface):
        """Validate interface configuration."""
        self.stdout.write("Validating interface configuration...")
        
        if interface.validate_config():
            self.stdout.write(self.style.SUCCESS("✓ Configuration is valid"))
        else:
            self.stdout.write(self.style.ERROR("✗ Configuration is invalid"))
        
        self.show_status(interface)

    def fetch_documents(self, interface, options):
        """Fetch documents from the interface."""
        self.stdout.write("Fetching documents...")
        
        if options['dry_run']:
            self.stdout.write("DRY RUN: Would fetch documents with the following parameters:")
            self.stdout.write(f"  Source: {interface.get_source_type()}")
            self.stdout.write(f"  Days: {options['days']}")
            self.stdout.write(f"  Max items: {options['max_items']}")
            return
        
        try:
            documents = interface.fetch_documents(
                days=options['days'],
                max_items=options['max_items']
            )
            
            self.stdout.write(f"Fetched {len(documents)} documents")
            
            if options['output_file']:
                self.save_results(documents, options['output_file'])
            
            # Show summary
            self.show_document_summary(documents)
            
        except Exception as e:
            raise CommandError(f"Failed to fetch documents: {e}")

    def search_documents(self, interface, options):
        """Search documents using the interface."""
        query = options['search_query']
        self.stdout.write(f"Searching for: {query}")
        
        if options['dry_run']:
            self.stdout.write("DRY RUN: Would search with the following parameters:")
            self.stdout.write(f"  Query: {query}")
            self.stdout.write(f"  Max results: {options['max_items']}")
            return
        
        try:
            documents = interface.search_documents(
                query,
                max_results=options['max_items']
            )
            
            self.stdout.write(f"Found {len(documents)} documents")
            
            if options['output_file']:
                self.save_results(documents, options['output_file'])
            
            # Show summary
            self.show_document_summary(documents)
            
        except Exception as e:
            raise CommandError(f"Failed to search documents: {e}")

    def save_results(self, documents, output_file):
        """Save results to file."""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(documents, f, indent=2, default=str, ensure_ascii=False)
            self.stdout.write(f"Results saved to: {output_file}")
        except Exception as e:
            self.stdout.write(self.style.WARNING(f"Failed to save results: {e}"))

    def show_document_summary(self, documents):
        """Show summary of fetched documents."""
        if not documents:
            self.stdout.write("No documents found")
            return
        
        # Count by content type
        type_counts = {}
        total_tokens = 0
        
        for doc in documents:
            content_type = doc.get('content_type', 'unknown')
            type_counts[content_type] = type_counts.get(content_type, 0) + 1
            
            # Estimate tokens
            metadata = doc.get('metadata', {})
            tokens = metadata.get('estimated_tokens', 0)
            total_tokens += tokens
        
        self.stdout.write("\nDocument Summary:")
        for content_type, count in type_counts.items():
            self.stdout.write(f"  {content_type}: {count} documents")
        
        self.stdout.write(f"\nTotal estimated tokens: {total_tokens:,}")
        
        # Show sample titles
        self.stdout.write("\nSample documents:")
        for i, doc in enumerate(documents[:5]):
            title = doc.get('title', 'Untitled')[:80]
            self.stdout.write(f"  {i+1}. {title}")
        
        if len(documents) > 5:
            self.stdout.write(f"  ... and {len(documents) - 5} more")

    def confirm(self, message):
        """Ask for user confirmation."""
        response = input(f"{message} (y/N): ").lower().strip()
        return response in ['y', 'yes']
