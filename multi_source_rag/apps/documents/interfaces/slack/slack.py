"""
Slack API Interface for Real-time Data Ingestion

This module provides live Slack API access for real-time data ingestion with:
- Production-ready error handling and rate limiting
- Advanced metadata extraction for RAG optimization
- Memory-efficient processing with TTL caching
- Thread-aware conversation processing
- Bot filtering and user resolution
- Token-based chunking with conversation awareness
- Full compatibility with advanced RAG features
"""

import hashlib
import logging
import time
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional
from dataclasses import dataclass, field

from slack_sdk import WebClient
from slack_sdk.errors import SlackApiError

from ..base import DocumentSourceInterface
from apps.core.utils.memory_manager import CacheConfig, memory_manager

logger = logging.getLogger(__name__)


@dataclass
class SlackMessage:
    """
    Enhanced data class representing a Slack message with comprehensive metadata.

    This class encapsulates all relevant information about a Slack message,
    including content, timing, authorship, and engagement metrics for RAG optimization.
    """
    text: str
    user: str
    timestamp: str
    thread_ts: Optional[str] = None
    channel: str = ""
    user_name: str = ""
    reactions: List[str] = field(default_factory=list)
    reply_count: int = 0
    files: List[Dict] = field(default_factory=list)
    edited: bool = False
    subtype: Optional[str] = None

    @property
    def readable_timestamp(self) -> str:
        """Convert timestamp to human-readable format with error handling."""
        try:
            return datetime.fromtimestamp(float(self.timestamp)).strftime("%Y-%m-%d %H:%M:%S")
        except (ValueError, TypeError) as e:
            logger.warning(f"Invalid timestamp {self.timestamp}: {e}")
            return self.timestamp

    @property
    def is_thread_parent(self) -> bool:
        """Check if this message is the parent of a thread."""
        return self.thread_ts is None and self.reply_count > 0

    @property
    def is_thread_reply(self) -> bool:
        """Check if this message is a reply in a thread."""
        return self.thread_ts is not None and self.thread_ts != self.timestamp

    def validate(self) -> bool:
        """Validate message data."""
        if not self.timestamp:
            return False
        try:
            float(self.timestamp)
            return True
        except ValueError:
            return False


class TokenEstimator:
    """Improved token estimation with better accuracy for RAG chunking."""

    @staticmethod
    def estimate_tokens(text: str) -> int:
        """
        Estimate token count with improved accuracy.

        Args:
            text: Text to estimate tokens for

        Returns:
            Estimated token count
        """
        if not text:
            return 0

        # Clean and normalize text
        import re
        text = re.sub(r'\s+', ' ', text.strip())

        # Count words
        words = text.split()
        word_count = len(words)

        # Count special tokens
        urls = len(re.findall(r'https?://\S+', text))
        mentions = len(re.findall(r'<@[A-Z0-9]+>', text))
        channels = len(re.findall(r'<#[A-Z0-9]+>', text))
        emojis = len(re.findall(r':[a-z_]+:', text))
        code_blocks = len(re.findall(r'```.*?```', text, re.DOTALL))

        # Calculate with weights
        total_tokens = word_count + (urls * 3) + (mentions * 2) + (channels * 2) + emojis + (code_blocks * 10)

        return max(1, int(total_tokens * 1.2))  # Add 20% buffer


class SlackSourceInterface(DocumentSourceInterface):
    """
    Enhanced Slack interface for real-time API access with advanced RAG features.

    Features:
    - Production-ready error handling and rate limiting
    - Advanced metadata extraction for RAG optimization
    - Memory-efficient processing with TTL caching
    - Thread-aware conversation processing
    - Bot filtering and user resolution
    - Token-based chunking with conversation awareness
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the Slack source interface with enhanced features.

        Args:
            config: Configuration for the Slack source
                Required keys:
                - api_token: Slack API token
                Optional keys:
                - channel_ids: List of channel IDs to fetch
                - days_to_fetch: Number of days of history to fetch
                - rate_limit_delay: Delay between API calls
                - max_tokens: Maximum tokens per document chunk
                - overlap_tokens: Tokens to overlap between chunks
        """
        super().__init__(config)
        self.api_token = config.get("api_token")
        self.channel_ids = config.get("channel_ids", [])
        self.days_to_fetch = config.get("days_to_fetch", 7)
        self.client = None
        self.pagination_cursor = None
        self.rate_limit_delay = config.get("rate_limit_delay", 1.0)

        # Chunking configuration
        self.max_tokens = config.get("max_tokens", 500)
        self.overlap_tokens = config.get("overlap_tokens", 50)

        if self.api_token:
            self.client = WebClient(token=self.api_token)

        # Enhanced caching with TTL and memory management
        cache_config = CacheConfig(max_size=1000, ttl_seconds=3600)  # 1 hour TTL
        self._user_cache = memory_manager.create_cache(f"slack_users_{id(self)}", cache_config)
        self._channel_cache = memory_manager.create_cache(f"slack_channels_{id(self)}", cache_config)

        # Token estimator for improved chunking
        self.token_estimator = TokenEstimator()

        # Statistics tracking
        self.stats = {
            "api_calls": 0,
            "rate_limits_hit": 0,
            "errors": 0,
            "messages_processed": 0,
            "chunks_created": 0
        }

    def fetch_documents(self, **kwargs) -> List[Dict[str, Any]]:
        """
        Fetch documents from Slack using 500-token chunking strategy.

        Args:
            **kwargs: Additional arguments for fetching documents
                - channel_id: Specific channel ID to fetch (overrides config)
                - channel_ids: List of channel IDs to fetch (overrides config)
                - days: Number of days to fetch (overrides config)
                - oldest: Oldest timestamp to fetch (in seconds since epoch)
                - latest: Latest timestamp to fetch (in seconds since epoch)
                - limit: Number of messages to fetch per page
                - cursor: Pagination cursor for continuing from a previous request
                - max_tokens: Maximum tokens per document (default: 500)
                - overlap_tokens: Tokens to overlap between documents (default: 50)

        Returns:
            List[Dict[str, Any]]: List of documents with 500-token chunks
        """
        if not self.client:
            raise ValueError("Slack API token is required")

        # Extract parameters
        channel_ids = kwargs.get("channel_ids", self.channel_ids)
        if "channel_id" in kwargs and kwargs["channel_id"]:
            channel_ids = [kwargs["channel_id"]]

        if not channel_ids:
            logger.warning("No channel IDs provided")
            return []

        days = kwargs.get("days", self.days_to_fetch)
        oldest = kwargs.get("oldest")
        latest = kwargs.get("latest")
        limit = kwargs.get("limit", 200)
        cursor = kwargs.get("cursor")
        max_tokens = kwargs.get("max_tokens", 500)
        overlap_tokens = kwargs.get("overlap_tokens", 50)

        # Handle pagination
        if cursor:
            if len(channel_ids) != 1:
                raise ValueError("When using cursor, exactly one channel_id must be provided")

            channel_id = channel_ids[0]
            messages = self.fetch_channel_messages(
                channel_id=channel_id,
                days=days,
                oldest=oldest,
                latest=latest,
                limit=limit,
                cursor=cursor,
            )
        else:
            # Fetch from all channels
            all_messages = []
            for channel_id in channel_ids:
                try:
                    channel_messages = self.fetch_channel_messages(
                        channel_id=channel_id,
                        days=days,
                        oldest=oldest,
                        latest=latest,
                        limit=limit,
                    )
                    all_messages.extend(channel_messages)
                except SlackApiError as e:
                    logger.error(f"Error fetching messages from channel {channel_id}: {e}")

            messages = all_messages

        if not messages:
            logger.warning("No messages found")
            return []

        # Create 500-token documents with conversation awareness
        documents = self._create_token_based_documents(messages, max_tokens, overlap_tokens)

        # Update statistics
        self.stats["messages_processed"] += len(messages)
        self.stats["chunks_created"] += len(documents)

        return documents

    def fetch_channel_messages(
        self,
        channel_id: str,
        days: int = 7,
        oldest: Optional[str] = None,
        latest: Optional[str] = None,
        limit: int = 200,
        cursor: Optional[str] = None,
        include_threads: bool = True,
        filter_bots: bool = True,
    ) -> List[SlackMessage]:
        """
        Fetch messages from a Slack channel.

        Args:
            channel_id: Channel ID to fetch
            days: Number of days to fetch (ignored if oldest is provided)
            oldest: Oldest timestamp to fetch (in seconds since epoch)
            latest: Latest timestamp to fetch (in seconds since epoch)
            limit: Number of messages to fetch per page
            cursor: Pagination cursor for continuing from a previous request
            include_threads: Whether to include thread replies
            filter_bots: Whether to filter out bot messages

        Returns:
            List[SlackMessage]: List of Slack messages
        """
        try:
            # Get channel info
            channel_info = self.client.conversations_info(channel=channel_id)
            channel_name = channel_info["channel"]["name"]
            logger.info(f"Fetching messages from channel: {channel_name}")

            # Calculate oldest timestamp if not provided
            if not oldest:
                oldest_time = datetime.now() - timedelta(days=days)
                oldest_ts = str(oldest_time.timestamp())
            else:
                oldest_ts = oldest

            # Prepare API parameters
            api_params = {
                "channel": channel_id,
                "oldest": oldest_ts,
                "limit": min(limit, 200),
            }

            if latest:
                api_params["latest"] = latest
            if cursor:
                api_params["cursor"] = cursor

            # Get messages
            result = self.client.conversations_history(**api_params)
            messages = result["messages"]

            # Store pagination info
            has_more = result.get("has_more", False)
            next_cursor = result.get("response_metadata", {}).get("next_cursor")
            self.pagination_cursor = next_cursor if has_more else None

            logger.info(f"Retrieved {len(messages)} messages. Has more: {has_more}")

            # Convert to SlackMessage objects
            slack_messages = []
            for msg in messages:
                slack_msg = self._create_slack_message(msg, channel_id, channel_name, filter_bots)
                if slack_msg:
                    slack_messages.append(slack_msg)

            # Include thread replies if requested
            if include_threads:
                thread_messages = self._load_thread_messages(slack_messages, channel_id, filter_bots)
                slack_messages.extend(thread_messages)

            # Sort by timestamp
            slack_messages.sort(key=lambda x: float(x.timestamp))

            logger.info(f"Processed {len(slack_messages)} messages total")
            return slack_messages

        except SlackApiError as e:
            logger.error(f"Error fetching messages from channel {channel_id}: {e}")
            return []

    def _create_slack_message(self, msg_data: Dict[str, Any], channel_id: str, channel_name: str, filter_bots: bool) -> Optional[SlackMessage]:
        """Create SlackMessage object from Slack API message data."""
        # Filter bots if requested
        if filter_bots and self._is_bot_message(msg_data):
            return None

        # Skip non-message types
        if msg_data.get("type") != "message":
            return None

        # Get user information
        user_id = msg_data.get("user", "")
        user_name = self._get_user_name(user_id)

        # Extract reactions
        reactions = []
        if "reactions" in msg_data:
            reactions = [r["name"] for r in msg_data["reactions"]]

        # Extract file information
        files = []
        if "files" in msg_data:
            files = [{
                "id": f.get("id", ""),
                "name": f.get("name", ""),
                "mimetype": f.get("mimetype", ""),
                "size": f.get("size", 0),
                "url": f.get("url_private", "")
            } for f in msg_data["files"]]

        return SlackMessage(
            text=msg_data.get("text", ""),
            user=user_id,
            timestamp=msg_data.get("ts", ""),
            thread_ts=msg_data.get("thread_ts"),
            channel=channel_id,
            user_name=user_name,
            reactions=reactions,
            reply_count=msg_data.get("reply_count", 0),
            files=files,
            edited=bool(msg_data.get("edited")),
            subtype=msg_data.get("subtype")
        )

    def _is_bot_message(self, msg_data: Dict[str, Any]) -> bool:
        """Check if message is from a bot."""
        if msg_data.get("subtype") == "bot_message":
            return True
        if msg_data.get("bot_id"):
            return True
        return False

    def _get_user_name(self, user_id: str) -> str:
        """Get user name with enhanced caching and error handling."""
        if not user_id:
            return "Unknown"

        # Check cache first
        cached_name = self._user_cache.get(user_id)
        if cached_name:
            return cached_name

        try:
            self.stats["api_calls"] += 1
            user_info = self.client.users_info(user=user_id)["user"]
            user_name = (user_info.get("profile", {}).get("display_name") or
                        user_info.get("real_name") or
                        user_info.get("name") or
                        f"User_{user_id[:8]}")

            # Store in TTL cache
            self._user_cache.set(user_id, user_name)
            return user_name

        except SlackApiError as e:
            self.stats["errors"] += 1
            logger.warning(f"Failed to get user info for {user_id}: {e}")
            user_name = f"User_{user_id[:8]}"

            # Cache the fallback name too
            self._user_cache.set(user_id, user_name)
            return user_name

    def _load_thread_messages(self, parent_messages: List[SlackMessage], channel_id: str, filter_bots: bool) -> List[SlackMessage]:
        """Load thread replies for messages that have threads."""
        thread_messages = []
        thread_parents = [msg for msg in parent_messages if msg.reply_count > 0]

        if not thread_parents:
            return thread_messages

        logger.info(f"Loading thread replies for {len(thread_parents)} conversations")

        for parent_msg in thread_parents:
            try:
                # Fetch thread replies with pagination
                all_replies = []
                cursor = None
                has_more = True

                while has_more:
                    thread = self.client.conversations_replies(
                        channel=channel_id,
                        ts=parent_msg.timestamp,
                        cursor=cursor,
                        limit=100
                    )

                    replies = thread["messages"]
                    all_replies.extend(replies)

                    has_more = thread.get("has_more", False)
                    cursor = thread.get("response_metadata", {}).get("next_cursor")

                    if has_more:
                        time.sleep(self.rate_limit_delay)

                # Process replies (skip the first message which is the parent)
                for reply_data in all_replies[1:]:
                    slack_msg = self._create_slack_message(reply_data, channel_id, parent_msg.channel, filter_bots)
                    if slack_msg:
                        thread_messages.append(slack_msg)

            except Exception as e:
                logger.error(f"Failed to load thread {parent_msg.timestamp}: {e}")

        logger.info(f"Loaded {len(thread_messages)} thread replies")
        return thread_messages

    def _create_token_based_documents(
        self,
        messages: List[SlackMessage],
        max_tokens: int = 500,
        overlap_tokens: int = 50
    ) -> List[Dict[str, Any]]:
        """
        Create documents by merging messages up to 500 tokens with conversation awareness.

        Args:
            messages: List of SlackMessage objects
            max_tokens: Maximum tokens per document
            overlap_tokens: Tokens to overlap between documents for context

        Returns:
            List of document dictionaries
        """
        if not messages:
            return []

        # Sort messages by timestamp
        sorted_messages = sorted(messages, key=lambda x: float(x.timestamp))

        # Group messages into conversation-aware chunks
        documents = []
        current_chunk = []
        current_tokens = 0
        overlap_content = ""

        for msg in sorted_messages:
            # Format message text with metadata
            msg_text = self._format_message_with_context(msg)
            msg_tokens = self._estimate_tokens(msg_text)

            # Check if adding this message would exceed token limit
            if current_tokens + msg_tokens > max_tokens and current_chunk:
                # Create document from current chunk
                doc = self._create_document_from_message_chunk(
                    current_chunk, overlap_content
                )
                if doc:
                    documents.append(doc)

                # Prepare overlap content from last few messages
                overlap_content = self._create_overlap_content(
                    current_chunk, overlap_tokens
                )

                # Start new chunk with overlap and current message
                current_chunk = [msg]
                current_tokens = self._estimate_tokens(overlap_content + msg_text)
            else:
                # Add message to current chunk
                current_chunk.append(msg)
                current_tokens += msg_tokens

        # Handle remaining messages
        if current_chunk:
            doc = self._create_document_from_message_chunk(
                current_chunk, overlap_content
            )
            if doc:
                documents.append(doc)

        logger.info(f"Created {len(documents)} token-based documents from {len(messages)} messages")
        return documents

    def _estimate_tokens(self, text: str) -> int:
        """
        Estimate token count using enhanced TokenEstimator.

        Args:
            text: Text to estimate tokens for

        Returns:
            Estimated token count
        """
        return self.token_estimator.estimate_tokens(text)

    def _format_message_with_context(self, message: SlackMessage) -> str:
        """
        Format a message with conversation context and metadata.

        Args:
            message: SlackMessage to format

        Returns:
            Formatted message text
        """
        timestamp = datetime.fromtimestamp(float(message.timestamp)).strftime("%Y-%m-%d %H:%M:%S")

        # Base message format
        formatted = f"[{timestamp}] {message.user_name}: {message.text}"

        # Add thread context
        if message.thread_ts and message.thread_ts != message.timestamp:
            formatted += " [Reply]"
        elif message.reply_count > 0:
            formatted += f" [Thread: {message.reply_count} replies]"

        # Add reactions
        if message.reactions:
            reactions_str = ", ".join([f":{r}:" for r in message.reactions])
            formatted += f" [Reactions: {reactions_str}]"

        # Add files
        if message.files:
            files_str = ", ".join([f.get('name', 'file') for f in message.files])
            formatted += f" [Files: {files_str}]"

        return formatted

    def _create_overlap_content(self, messages: List[SlackMessage], overlap_tokens: int) -> str:
        """
        Create overlap content from the end of a message chunk.

        Args:
            messages: List of messages in the chunk
            overlap_tokens: Target tokens for overlap

        Returns:
            Overlap content string
        """
        if not messages or overlap_tokens <= 0:
            return ""

        # Start from the last messages and work backwards
        overlap_parts = []
        current_tokens = 0

        for msg in reversed(messages):
            msg_text = self._format_message_with_context(msg)
            msg_tokens = self._estimate_tokens(msg_text)

            if current_tokens + msg_tokens <= overlap_tokens:
                overlap_parts.insert(0, msg_text)
                current_tokens += msg_tokens
            else:
                break

        return "\n\n".join(overlap_parts)

    def _create_document_from_message_chunk(
        self,
        messages: List[SlackMessage],
        overlap_content: str = ""
    ) -> Dict[str, Any]:
        """
        Create a document from a chunk of messages.

        Args:
            messages: List of messages in the chunk
            overlap_content: Overlap content from previous chunk

        Returns:
            Document dictionary
        """
        if not messages:
            return None

        # Sort messages by timestamp
        sorted_messages = sorted(messages, key=lambda x: float(x.timestamp))

        # Format all messages
        message_texts = []
        if overlap_content:
            message_texts.append(f"[Previous context]\n{overlap_content}\n[/Previous context]")

        for msg in sorted_messages:
            message_texts.append(self._format_message_with_context(msg))

        content = "\n\n".join(message_texts)

        # Create document metadata
        start_time = datetime.fromtimestamp(float(sorted_messages[0].timestamp))
        end_time = datetime.fromtimestamp(float(sorted_messages[-1].timestamp))
        duration_minutes = (end_time - start_time).total_seconds() / 60

        # Extract participants and threads
        participants = list(set(msg.user_name for msg in sorted_messages))
        thread_count = len(set(msg.thread_ts for msg in sorted_messages if msg.thread_ts))

        # Calculate engagement metrics
        total_reactions = sum(len(msg.reactions) for msg in sorted_messages)
        total_files = sum(len(msg.files) for msg in sorted_messages)

        # Generate document ID
        content_hash = hashlib.md5(content.encode()).hexdigest()[:8]
        channel_id = sorted_messages[0].channel
        doc_id = f"slack_{channel_id}_{start_time.strftime('%Y%m%d_%H%M%S')}_{content_hash}"

        # Create title
        channel_name = sorted_messages[0].channel  # Could be enhanced with channel info

        if len(participants) == 1:
            title = f"{channel_name} - {participants[0]} ({len(sorted_messages)} messages)"
        else:
            title = f"{channel_name} - {len(participants)} participants ({len(sorted_messages)} messages)"

        # Create document
        document = {
            'id': doc_id,
            'title': title,
            'content': content,
            'metadata': {
                'channel_id': channel_id,
                'channel_name': channel_name,
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'duration_minutes': duration_minutes,
                'message_count': len(sorted_messages),
                'participants': participants,
                'participant_count': len(participants),
                'thread_count': thread_count,
                'total_reactions': total_reactions,
                'total_files': total_files,
                'has_overlap': bool(overlap_content),
                'estimated_tokens': self._estimate_tokens(content),
                'chunking_strategy': 'token_based_500',
                'max_tokens': 500,
                'has_threads': thread_count > 0,
                'has_files': total_files > 0,
                'engagement_score': min(1.0, (
                    len(participants) * 0.2 +
                    (total_reactions / len(sorted_messages)) * 0.3 +
                    (1.0 if thread_count > 0 else 0.0) * 0.2 +
                    min(len(sorted_messages) / 10.0, 1.0) * 0.3
                ))
            },
            'url': f"https://slack.com/archives/{channel_id}/p{sorted_messages[0].timestamp.replace('.', '')}",
            'content_type': 'text/slack'
        }

        return document

    def get_document(self, document_id: str, **kwargs) -> Dict[str, Any]:
        """
        Get a specific document from Slack.

        Args:
            document_id: ID of the document to get (message timestamp)
            **kwargs: Additional arguments (unused for Slack)

        Returns:
            Dict[str, Any]: Document
        """
        # For Slack, document_id is typically a message timestamp
        logger.warning(f"Individual document retrieval not fully implemented for Slack. ID: {document_id}")
        return {
            "id": document_id,
            "content": "Document retrieval not implemented",
            "metadata": {"source": "slack", "type": "message"}
        }

    def search_documents(self, query: str, **kwargs) -> List[Dict[str, Any]]:
        """
        Search for documents in Slack.

        Args:
            query: Query to search for
            **kwargs: Additional arguments

        Returns:
            List[Dict[str, Any]]: List of documents
        """
        if not self.client:
            raise ValueError("Slack API token is required")

        documents = []

        try:
            # Get search parameters
            channel_id = kwargs.get("channel_id")

            # Build search query
            search_query = query
            if channel_id:
                search_query = f"{search_query} in:#{channel_id}"

            # Search messages
            result = self.client.search_messages(
                query=search_query, sort="timestamp", sort_dir="desc", count=100
            )

            # Convert search results to messages and create token-based documents
            if "messages" in result and "matches" in result["messages"]:
                messages = []
                for match in result["messages"]["matches"]:
                    # Convert match to SlackMessage
                    slack_msg = self._create_slack_message(
                        match, match["channel"]["id"], match["channel"]["name"], True
                    )
                    if slack_msg:
                        messages.append(slack_msg)

                # Create token-based documents from search results
                documents = self._create_token_based_documents(messages, 500, 50)

        except SlackApiError as e:
            logger.error(f"Error searching messages: {e}")

        return documents

    def validate_config(self) -> bool:
        """
        Validate the configuration.

        Returns:
            bool: True if the configuration is valid, False otherwise
        """
        if not self.api_token:
            return False

        try:
            client = WebClient(token=self.api_token)
            client.auth_test()
            return True
        except SlackApiError:
            return False

    def get_source_type(self) -> str:
        """Get the type of the source."""
        return "slack"

    def get_pagination_cursor(self) -> Optional[str]:
        """Get the current pagination cursor."""
        return self.pagination_cursor

    def set_pagination_cursor(self, cursor: str) -> None:
        """Set the pagination cursor."""
        self.pagination_cursor = cursor
