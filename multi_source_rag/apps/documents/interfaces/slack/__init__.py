"""
Slack Document Source Interfaces

This package contains all Slack-related document source interfaces:
- slack.py: API-based Slack interface for real-time data
- local_slack.py: Local file-based Slack interface for staged data
"""

from .slack import SlackSourceInterface
from .local_slack import LocalSlackSourceInterface

# Create alias for consistency
LocalSlackInterface = LocalSlackSourceInterface

__all__ = ['SlackSourceInterface', 'LocalSlackInterface', 'LocalSlackSourceInterface']