"""
GitHub interface for document source.
"""

import hashlib
import json
import logging
import re
import time
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple
from urllib.parse import urlparse

# Django timezone utilities replaced with centralized timezone_utils
from github import Github, GithubException, RateLimitExceededException
from github.Issue import Issue
from github.PullRequest import PullRequest
from github.Repository import Repository

from ..base import DocumentSourceInterface

# Set up logging
logger = logging.getLogger(__name__)


class DateTimeEncoder(json.JSONEncoder):
    """
    Custom JSON encoder that handles datetime objects.
    """

    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        return super().default(obj)


class RateLimitHandler:
    """
    Handler for GitHub API rate limiting.
    """

    def __init__(self, max_retries: int = 3, retry_delay: int = 5):
        """
        Initialize the rate limit handler.

        Args:
            max_retries: Maximum number of retries
            retry_delay: Delay between retries in seconds
        """
        self.max_retries = max_retries
        self.retry_delay = retry_delay

    def handle_rate_limit(self, client: Github) -> bool:
        """
        Handle rate limit by waiting for reset with proper bounds checking.

        Args:
            client: GitHub client

        Returns:
            bool: True if rate limit was handled, False otherwise
        """
        try:
            from apps.core.utils.timezone_utils import get_current_utc

            rate_limit = client.get_rate_limit()
            reset_timestamp = rate_limit.core.reset.timestamp()

            # Use UTC timestamps consistently
            current_utc = get_current_utc()
            current_timestamp = current_utc.timestamp()

            # Calculate wait time with bounds checking
            wait_time = reset_timestamp - current_timestamp

            # Add bounds checking to prevent infinite loops
            MAX_WAIT_TIME = 3600  # Maximum 1 hour wait
            MIN_WAIT_TIME = 1     # Minimum 1 second wait

            if wait_time > 0:
                # Cap wait time to prevent infinite loops
                bounded_wait_time = max(MIN_WAIT_TIME, min(wait_time, MAX_WAIT_TIME))

                logger.warning(
                    f"GitHub API rate limit reached. "
                    f"Waiting {bounded_wait_time:.2f} seconds for reset "
                    f"(original: {wait_time:.2f}s, capped at {MAX_WAIT_TIME}s)"
                )

                # Add 1 second buffer to ensure reset has occurred
                time.sleep(bounded_wait_time + 1)
                return True

            return False
        except Exception as e:
            logger.error(f"Error handling rate limit: {str(e)}")
            return False


class GitHubSourceInterface(DocumentSourceInterface):
    """
    Interface for GitHub document source.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the GitHub source interface.

        Args:
            config: Configuration for the GitHub source
        """
        super().__init__(config)
        self.token = config.get("token")
        self.owner = config.get("owner")
        self.repo = config.get("repo")
        self.client = None
        self.rate_limit_handler = RateLimitHandler()
        self.initialize_client()

    def initialize_client(self) -> None:
        """
        Initialize the GitHub client.
        """
        if not self.token:
            raise ValueError("GitHub token is required")

        try:
            self.client = Github(self.token, per_page=100)
        except Exception as e:
            raise ValueError(f"Failed to initialize GitHub client: {str(e)}")

    def validate_config(self) -> bool:
        """
        Validate the configuration.

        Returns:
            bool: True if the configuration is valid, False otherwise
        """
        required_fields = ["token", "owner", "repo"]
        for field in required_fields:
            if not self.config.get(field):
                return False

        return True

    def get_source_type(self) -> str:
        """
        Get the type of the source.

        Returns:
            str: Type of the source
        """
        return "github"

    def fetch_documents(self, **kwargs) -> List[Dict[str, Any]]:
        """
        Fetch documents from GitHub.

        Args:
            **kwargs: Additional arguments for fetching documents
                - days: Number of days to fetch (default: 30)
                - content_types: Types of content to fetch (default: ['pull_request', 'issue'])
                - state: State of PRs/issues to fetch (default: 'all')
                - since: Fetch documents since this date (datetime object)

        Returns:
            List[Dict[str, Any]]: List of documents
        """
        if not self.client:
            self.initialize_client()

        # Parse arguments
        days = kwargs.get("days", 30)
        content_types = kwargs.get("content_types", ["pull_request", "issue"])
        state = kwargs.get("state", "all")
        since = kwargs.get("since")

        # Calculate since date if not provided
        if not since:
            since = datetime.now() - timedelta(days=days)
        # Convert string date to datetime if needed
        elif isinstance(since, str):
            try:
                since = datetime.fromisoformat(since.replace("Z", "+00:00"))
            except ValueError:
                # Try different format if ISO format fails
                try:
                    since = datetime.strptime(since, "%Y-%m-%d")
                except ValueError:
                    logger.warning(
                        f"Could not parse since date: {since}, using default"
                    )
                    since = datetime.now() - timedelta(days=days)

        documents = []

        try:
            # Get repository
            repo = self.client.get_repo(f"{self.owner}/{self.repo}")

            # Fetch pull requests
            if "pull_request" in content_types:
                prs = self._fetch_pull_requests(repo, state, since)
                documents.extend(prs)

            # Fetch issues (excluding PRs)
            if "issue" in content_types:
                issues = self._fetch_issues(repo, state, since)
                documents.extend(issues)

            # Fetch wiki pages
            if "wiki" in content_types:
                wiki_pages = self._fetch_wiki_pages(repo, since)
                documents.extend(wiki_pages)

            # Fetch discussions
            if "discussion" in content_types:
                discussions = self._fetch_discussions(repo, since)
                documents.extend(discussions)

            # Fetch releases
            if "release" in content_types:
                releases = self._fetch_releases(repo, since)
                documents.extend(releases)

            # Fetch project boards
            if "project" in content_types:
                projects = self._fetch_projects(repo, since)
                documents.extend(projects)

            # Fetch workflow runs (metadata only)
            if "workflow" in content_types:
                workflows = self._fetch_workflows(repo, since)
                documents.extend(workflows)

            return documents

        except RateLimitExceededException:
            # Handle rate limit
            if self.rate_limit_handler.handle_rate_limit(self.client):
                # Retry after waiting
                return self.fetch_documents(**kwargs)
            else:
                raise ValueError(
                    "GitHub API rate limit exceeded and could not be handled"
                )

        except GithubException as e:
            raise ValueError(f"GitHub API error: {str(e)}")

        except Exception as e:
            raise ValueError(f"Error fetching documents from GitHub: {str(e)}")

    def get_document(self, document_id: str, **kwargs) -> Dict[str, Any]:
        """
        Get a specific document from GitHub.

        Args:
            document_id: ID of the document to get
            **kwargs: Additional arguments for getting the document
                - content_type: Type of content ('pull_request' or 'issue')

        Returns:
            Dict[str, Any]: Document
        """
        if not self.client:
            self.initialize_client()

        content_type = kwargs.get("content_type")

        try:
            # Get repository
            repo = self.client.get_repo(f"{self.owner}/{self.repo}")

            # Parse document ID
            parts = document_id.split("/")
            if len(parts) != 2:
                raise ValueError(
                    f"Invalid document ID: {document_id}. Expected format: 'type/number'"
                )

            doc_type, number = parts
            number = int(number)

            # Get document based on type
            if doc_type == "pr" or (not content_type and doc_type == "pull"):
                return self._process_pull_request(repo.get_pull(number))
            elif doc_type == "issue" or (not content_type and doc_type == "issues"):
                return self._process_issue(repo.get_issue(number))
            else:
                raise ValueError(f"Unsupported document type: {doc_type}")

        except RateLimitExceededException:
            # Handle rate limit
            if self.rate_limit_handler.handle_rate_limit(self.client):
                # Retry after waiting
                return self.get_document(document_id, **kwargs)
            else:
                raise ValueError(
                    "GitHub API rate limit exceeded and could not be handled"
                )

        except GithubException as e:
            raise ValueError(f"GitHub API error: {str(e)}")

        except Exception as e:
            raise ValueError(f"Error getting document from GitHub: {str(e)}")

    def search_documents(self, query: str, **kwargs) -> List[Dict[str, Any]]:
        """
        Search for documents in GitHub.

        Args:
            query: Query to search for
            **kwargs: Additional arguments for searching documents
                - content_types: Types of content to search (default: ['pull_request', 'issue'])
                - state: State of PRs/issues to search (default: 'all')
                - max_results: Maximum number of results to return (default: 100)

        Returns:
            List[Dict[str, Any]]: List of documents
        """
        if not self.client:
            self.initialize_client()

        # Parse arguments
        content_types = kwargs.get("content_types", ["pull_request", "issue"])
        state = kwargs.get("state", "all")
        max_results = kwargs.get("max_results", 100)

        documents = []

        try:
            # Construct search query
            search_query = f"{query} repo:{self.owner}/{self.repo}"
            if state != "all":
                search_query += f" state:{state}"

            # Search for issues and PRs
            search_results = self.client.search_issues(search_query)

            # Process results
            count = 0
            for item in search_results:
                if count >= max_results:
                    break

                # Check if it's a PR or issue
                is_pr = hasattr(item, "pull_request") and item.pull_request is not None

                if (is_pr and "pull_request" in content_types) or (
                    not is_pr and "issue" in content_types
                ):
                    # Get full PR or issue
                    if is_pr:
                        pr_number = item.number
                        repo = self.client.get_repo(f"{self.owner}/{self.repo}")
                        document = self._process_pull_request(repo.get_pull(pr_number))
                    else:
                        document = self._process_issue(item)

                    documents.append(document)
                    count += 1

            return documents

        except RateLimitExceededException:
            # Handle rate limit
            if self.rate_limit_handler.handle_rate_limit(self.client):
                # Retry after waiting
                return self.search_documents(query, **kwargs)
            else:
                raise ValueError(
                    "GitHub API rate limit exceeded and could not be handled"
                )

        except GithubException as e:
            raise ValueError(f"GitHub API error: {str(e)}")

        except Exception as e:
            raise ValueError(f"Error searching documents in GitHub: {str(e)}")

    def _fetch_pull_requests(
        self, repo: Repository, state: str, since: datetime
    ) -> List[Dict[str, Any]]:
        """
        Fetch pull requests from the repository.

        Args:
            repo: GitHub repository
            state: State of PRs to fetch ('open', 'closed', 'all')
            since: Fetch PRs updated since this date

        Returns:
            List[Dict[str, Any]]: List of processed pull requests
        """
        documents = []

        try:
            # Get pull requests
            pulls = repo.get_pulls(state=state, sort="updated", direction="desc")

            # Process each PR
            for pr in pulls:
                # Skip if PR was last updated before since date
                # Use consistent UTC timezone handling
                from apps.core.utils.timezone_utils import ensure_utc, safe_datetime_comparison

                pr_updated_at = ensure_utc(pr.updated_at)
                since_utc = ensure_utc(since)

                # Use safe comparison that handles timezone conversion
                comparison_result = safe_datetime_comparison(since_utc, pr_updated_at)
                if comparison_result is True:  # since > pr_updated_at, so skip
                    break

                document = self._process_pull_request(pr)
                documents.append(document)

            return documents

        except Exception as e:
            logger.error(f"Error fetching pull requests: {str(e)}")
            raise

    def _fetch_issues(
        self, repo: Repository, state: str, since: datetime
    ) -> List[Dict[str, Any]]:
        """
        Fetch issues from the repository (excluding PRs).

        Args:
            repo: GitHub repository
            state: State of issues to fetch ('open', 'closed', 'all')
            since: Fetch issues updated since this date

        Returns:
            List[Dict[str, Any]]: List of processed issues
        """
        documents = []

        try:
            # Get issues
            issues = repo.get_issues(state=state, sort="updated", direction="desc")

            # Process each issue
            for issue in issues:
                # Skip if issue was last updated before since date
                # Use consistent UTC timezone handling
                from apps.core.utils.timezone_utils import ensure_utc, safe_datetime_comparison

                issue_updated_at = ensure_utc(issue.updated_at)
                since_utc = ensure_utc(since)

                # Use safe comparison that handles timezone conversion
                comparison_result = safe_datetime_comparison(since_utc, issue_updated_at)
                if comparison_result is True:  # since > issue_updated_at, so skip
                    break

                # Skip if it's a PR (issues endpoint returns PRs too)
                if hasattr(issue, "pull_request") and issue.pull_request is not None:
                    continue

                document = self._process_issue(issue)
                documents.append(document)

            return documents

        except Exception as e:
            logger.error(f"Error fetching issues: {str(e)}")
            raise

    def _fetch_wiki_pages(self, repo: Repository, since: datetime) -> List[Dict[str, Any]]:
        """
        Fetch wiki pages from the repository.

        Args:
            repo: GitHub repository
            since: Fetch wiki pages updated since this date

        Returns:
            List[Dict[str, Any]]: List of processed wiki pages
        """
        documents = []

        try:
            # Get wiki repository
            wiki_repo_name = f"{repo.full_name}.wiki"
            try:
                wiki_repo = self.client.get_repo(wiki_repo_name)
            except Exception:
                logger.info(f"Wiki repository {wiki_repo_name} not found or not accessible")
                return documents

            # Get wiki pages (contents of the wiki repository)
            try:
                wiki_contents = wiki_repo.get_contents("")
            except Exception:
                logger.info(f"No wiki contents found for {wiki_repo_name}")
                return documents

            # Process wiki pages
            for content in wiki_contents:
                # Skip non-markdown files
                if not content.name.endswith(".md"):
                    continue

                try:
                    # Get last update date
                    commits = list(wiki_repo.get_commits(path=content.path))
                    if not commits:
                        continue

                    last_commit = commits[0]
                    last_updated = last_commit.commit.author.date

                    # Skip if older than specified date
                    from apps.core.utils.timezone_utils import ensure_utc, safe_datetime_comparison

                    last_updated_utc = ensure_utc(last_updated)
                    since_utc = ensure_utc(since)

                    comparison_result = safe_datetime_comparison(since_utc, last_updated_utc)
                    if comparison_result is True:  # since > last_updated, so skip
                        continue

                    # Get file content
                    import base64
                    file_content = base64.b64decode(content.content).decode("utf-8")

                    # Create document
                    document = self._process_wiki_page(content, file_content, last_commit, repo)
                    documents.append(document)

                except Exception as e:
                    logger.error(f"Error processing wiki page {content.name}: {str(e)}")
                    continue

            return documents

        except Exception as e:
            logger.error(f"Error fetching wiki pages: {str(e)}")
            return documents

    def _fetch_discussions(self, repo: Repository, since: datetime) -> List[Dict[str, Any]]:
        """
        Fetch discussions from the repository.

        Args:
            repo: GitHub repository
            since: Fetch discussions updated since this date

        Returns:
            List[Dict[str, Any]]: List of processed discussions
        """
        documents = []

        try:
            # GitHub Discussions require GraphQL API
            # For now, we'll use a simple approach with the REST API search
            # This is a limitation of PyGithub - full discussions support would need GraphQL

            # Search for discussions using the search API
            search_query = f"repo:{repo.full_name} type:discussions"

            try:
                # Note: This is a simplified approach. Full discussions support would require GraphQL
                search_results = self.client.search_issues(search_query)

                for item in search_results:
                    # Check if it's actually a discussion (this is a workaround)
                    if hasattr(item, 'state_reason') and 'discussion' in str(item.html_url):
                        try:
                            # Skip if older than specified date
                            from apps.core.utils.timezone_utils import ensure_utc, safe_datetime_comparison

                            item_updated_at = ensure_utc(item.updated_at)
                            since_utc = ensure_utc(since)

                            comparison_result = safe_datetime_comparison(since_utc, item_updated_at)
                            if comparison_result is True:  # since > item_updated_at, so skip
                                continue

                            document = self._process_discussion(item, repo)
                            documents.append(document)
                        except Exception as e:
                            logger.error(f"Error processing discussion {item.number}: {str(e)}")
                            continue

            except Exception as e:
                logger.info(f"Discussions not available or accessible for {repo.full_name}: {str(e)}")

            return documents

        except Exception as e:
            logger.error(f"Error fetching discussions: {str(e)}")
            return documents

    def _fetch_releases(self, repo: Repository, since: datetime) -> List[Dict[str, Any]]:
        """
        Fetch releases from the repository.

        Args:
            repo: GitHub repository
            since: Fetch releases published since this date

        Returns:
            List[Dict[str, Any]]: List of processed releases
        """
        documents = []

        try:
            # Get releases
            releases = repo.get_releases()

            # Process each release
            for release in releases:
                try:
                    # Skip if release was published before since date
                    from apps.core.utils.timezone_utils import ensure_utc, safe_datetime_comparison

                    release_published_at = ensure_utc(release.published_at) if release.published_at else ensure_utc(release.created_at)
                    since_utc = ensure_utc(since)

                    comparison_result = safe_datetime_comparison(since_utc, release_published_at)
                    if comparison_result is True:  # since > release_published_at, so skip
                        continue

                    document = self._process_release(release, repo)
                    documents.append(document)

                except Exception as e:
                    logger.error(f"Error processing release {release.tag_name}: {str(e)}")
                    continue

            return documents

        except Exception as e:
            logger.error(f"Error fetching releases: {str(e)}")
            return documents

    def _fetch_projects(self, repo: Repository, since: datetime) -> List[Dict[str, Any]]:
        """
        Fetch project boards from the repository.

        Args:
            repo: GitHub repository
            since: Fetch projects updated since this date

        Returns:
            List[Dict[str, Any]]: List of processed project boards
        """
        documents = []

        try:
            # Get projects (both repository and organization projects)
            try:
                projects = repo.get_projects(state='all')

                for project in projects:
                    try:
                        # Skip if project was updated before since date
                        from apps.core.utils.timezone_utils import ensure_utc, safe_datetime_comparison

                        project_updated_at = ensure_utc(project.updated_at)
                        since_utc = ensure_utc(since)

                        comparison_result = safe_datetime_comparison(since_utc, project_updated_at)
                        if comparison_result is True:  # since > project_updated_at, so skip
                            continue

                        document = self._process_project(project, repo)
                        documents.append(document)

                    except Exception as e:
                        logger.error(f"Error processing project {project.name}: {str(e)}")
                        continue

            except Exception as e:
                logger.info(f"Projects not available or accessible for {repo.full_name}: {str(e)}")

            return documents

        except Exception as e:
            logger.error(f"Error fetching projects: {str(e)}")
            return documents

    def _fetch_workflows(self, repo: Repository, since: datetime) -> List[Dict[str, Any]]:
        """
        Fetch workflow runs from the repository (metadata only).

        Args:
            repo: GitHub repository
            since: Fetch workflow runs since this date

        Returns:
            List[Dict[str, Any]]: List of processed workflow runs
        """
        documents = []

        try:
            # Get workflow runs
            try:
                workflows = repo.get_workflows()

                for workflow in workflows:
                    try:
                        # Get recent runs for this workflow
                        runs = workflow.get_runs()

                        for run in runs:
                            try:
                                # Skip if run was created before since date
                                from apps.core.utils.timezone_utils import ensure_utc, safe_datetime_comparison

                                run_created_at = ensure_utc(run.created_at)
                                since_utc = ensure_utc(since)

                                comparison_result = safe_datetime_comparison(since_utc, run_created_at)
                                if comparison_result is True:  # since > run_created_at, so skip
                                    break  # Runs are ordered by date, so we can break

                                document = self._process_workflow_run(run, workflow, repo)
                                documents.append(document)

                            except Exception as e:
                                logger.error(f"Error processing workflow run {run.id}: {str(e)}")
                                continue

                    except Exception as e:
                        logger.error(f"Error processing workflow {workflow.name}: {str(e)}")
                        continue

            except Exception as e:
                logger.info(f"Workflows not available or accessible for {repo.full_name}: {str(e)}")

            return documents

        except Exception as e:
            logger.error(f"Error fetching workflows: {str(e)}")
            return documents

    def _process_pull_request(self, pr: PullRequest) -> Dict[str, Any]:
        """
        Process a pull request into a document.

        Args:
            pr: GitHub pull request

        Returns:
            Dict[str, Any]: Processed document

        Raises:
            ValueError: If there's an error processing the pull request
        """
        try:
            # Extract PR data
            pr_id = f"pr/{pr.number}"
            title = pr.title
            body = pr.body or ""

            # Prepare PR data for cleaning
            pr_data = {
                "id": pr.id,
                "number": pr.number,
                "title": title,
                "body": body,
                "state": pr.state,
                "created_at": pr.created_at.isoformat() if pr.created_at else None,
                "updated_at": pr.updated_at.isoformat() if pr.updated_at else None,
                "closed_at": pr.closed_at.isoformat() if pr.closed_at else None,
                "merged_at": pr.merged_at.isoformat() if pr.merged_at else None,
                "html_url": pr.html_url,
                "user": {
                    "id": pr.user.id,
                    "login": pr.user.login,
                    "avatar_url": pr.user.avatar_url,
                    "html_url": pr.user.html_url,
                },
                "labels": [
                    {"name": label.name, "color": label.color} for label in pr.labels
                ],
                "draft": pr.draft,
                "merged": pr.merged,
                "mergeable": pr.mergeable,
                "mergeable_state": pr.mergeable_state,
                "comments": pr.comments,
                "review_comments": pr.review_comments,
                "commits": pr.commits,
                "additions": pr.additions,
                "deletions": pr.deletions,
                "changed_files": pr.changed_files,
                "type": "pull_request",
            }

            # Add assignees if available
            if pr.assignees:
                pr_data["assignees"] = [
                    {
                        "id": assignee.id,
                        "login": assignee.login,
                        "avatar_url": assignee.avatar_url,
                        "html_url": assignee.html_url,
                    }
                    for assignee in pr.assignees
                ]

            # Add requested reviewers if available
            if hasattr(pr, "get_review_requests"):
                reviewers, _ = pr.get_review_requests()
                pr_data["requested_reviewers"] = [
                    {
                        "id": reviewer.id,
                        "login": reviewer.login,
                        "avatar_url": reviewer.avatar_url,
                        "html_url": reviewer.html_url,
                    }
                    for reviewer in reviewers
                ]

            # Process comments
            comments_data = []
            for comment in pr.get_comments():
                if not self._is_bot_comment(comment):
                    comment_data = {
                        "id": comment.id,
                        "body": comment.body,
                        "created_at": (
                            comment.created_at.isoformat()
                            if comment.created_at
                            else None
                        ),
                        "updated_at": (
                            comment.updated_at.isoformat()
                            if comment.updated_at
                            else None
                        ),
                        "html_url": comment.html_url,
                        "user": {
                            "id": comment.user.id,
                            "login": comment.user.login,
                            "avatar_url": comment.user.avatar_url,
                            "html_url": comment.user.html_url,
                        },
                    }
                    comments_data.append(comment_data)

            pr_data["comments"] = comments_data

            # Process review comments
            review_comments_data = []
            for comment in pr.get_review_comments():
                if not self._is_bot_comment(comment):
                    comment_data = {
                        "id": comment.id,
                        "body": comment.body,
                        "created_at": (
                            comment.created_at.isoformat()
                            if comment.created_at
                            else None
                        ),
                        "updated_at": (
                            comment.updated_at.isoformat()
                            if comment.updated_at
                            else None
                        ),
                        "html_url": comment.html_url,
                        "user": {
                            "id": comment.user.id,
                            "login": comment.user.login,
                            "avatar_url": comment.user.avatar_url,
                            "html_url": comment.user.html_url,
                        },
                        "path": comment.path,
                        "position": comment.position,
                        "commit_id": comment.commit_id,
                        "diff_hunk": comment.diff_hunk,
                    }
                    review_comments_data.append(comment_data)

            pr_data["review_comments"] = review_comments_data

            # Process files
            files_data = []
            for file in pr.get_files():
                file_data = {
                    "filename": file.filename,
                    "status": file.status,
                    "additions": file.additions,
                    "deletions": file.deletions,
                    "changes": file.changes,
                    "blob_url": file.blob_url,
                    "raw_url": file.raw_url,
                    "contents_url": file.contents_url,
                    "patch": file.patch,
                }
                files_data.append(file_data)

            pr_data["files"] = files_data

            # Use the GitHub cleaners if available
            try:
                from ..cleaners.chunking import SemanticChunker
                from ..cleaners.github import (GitHubContentQualityAssessor,
                                               GitHubPRCleaner)

                # Initialize cleaners
                pr_cleaner = GitHubPRCleaner()
                quality_assessor = GitHubContentQualityAssessor()
                # Note: Chunking disabled for GitHub PRs - they are pre-structured documents

                # Clean and enhance the PR
                cleaned_pr = pr_cleaner.clean_pr(pr_data)

                # Assess quality
                quality = quality_assessor.assess_quality(cleaned_pr)
                cleaned_pr["quality"] = quality

                # Create single chunk (no chunking for GitHub PRs)
                chunks = [cleaned_pr]

                # Create document with enhanced data
                document = {
                    "id": pr_id,
                    "title": title,
                    "content": cleaned_pr.get("body", body),
                    "content_type": "github_pr",
                    "metadata": {
                        "number": pr.number,
                        "state": pr.state,
                        "created_at": pr.created_at.isoformat() if pr.created_at else None,
                        "updated_at": pr.updated_at.isoformat() if pr.updated_at else None,
                        "closed_at": pr.closed_at.isoformat() if pr.closed_at else None,
                        "merged_at": pr.merged_at.isoformat() if pr.merged_at else None,
                        "merged": pr.merged,
                        "mergeable": pr.mergeable,
                        "author": self._get_user_info(pr.user),
                        "assignees": [
                            self._get_user_info(assignee) for assignee in pr.assignees
                        ],
                        "requested_reviewers": (
                            [
                                self._get_user_info(reviewer)
                                for reviewer in pr.get_review_requests()[0]
                            ]
                            if hasattr(pr, "get_review_requests")
                            else []
                        ),
                        "labels": [label.name for label in pr.labels],
                        "comments": cleaned_pr.get("comments", []),
                        "review_comments": cleaned_pr.get("review_comments", []),
                        "commits": pr.commits,
                        "additions": pr.additions,
                        "deletions": pr.deletions,
                        "changed_files": pr.changed_files,
                        "quality_score": quality.get("quality_score", 0.0),
                        "is_high_quality": quality.get("is_high_quality", False),
                        "quality_reasons": quality.get("reasons", []),
                        "technical_entities": cleaned_pr.get("technical_entities", {}),
                        "chunks": chunks,
                    },
                    "source": "github",
                    "url": pr.html_url,
                }
            except ImportError:
                # Fall back to basic processing
                cleaned_content = self._clean_markdown_content(body)
                template_fields = self._extract_pr_template_fields(cleaned_content)
                references = self._extract_references(cleaned_content)

                # Process comments for basic document
                comments = []
                try:
                    for comment in pr.get_issue_comments():
                        if not self._is_bot_comment(comment):
                            comments.append(
                                {
                                    "id": comment.id,
                                    "user": self._get_user_info(comment.user),
                                    "body": comment.body,
                                    "created_at": comment.created_at,
                                    "updated_at": comment.updated_at,
                                }
                            )
                except Exception as e:
                    logger.warning(f"Error fetching PR comments: {e}")

                # Process reviews for basic document
                reviews = []
                for review in pr.get_reviews():
                    if not self._is_bot_comment(review):
                        reviews.append(
                            {
                                "id": review.id,
                                "user": self._get_user_info(review.user),
                                "body": review.body,
                                "state": review.state,
                                "submitted_at": review.submitted_at.isoformat() if review.submitted_at else None,
                            }
                        )

                # Create basic document
                document = {
                    "id": pr_id,
                    "title": title,
                    "content": cleaned_content,
                    "content_type": "github_pr",
                    "metadata": {
                        "number": pr.number,
                        "state": pr.state,
                        "created_at": pr.created_at.isoformat() if pr.created_at else None,
                        "updated_at": pr.updated_at.isoformat() if pr.updated_at else None,
                        "closed_at": pr.closed_at.isoformat() if pr.closed_at else None,
                        "merged_at": pr.merged_at.isoformat() if pr.merged_at else None,
                        "merged": pr.merged,
                        "mergeable": pr.mergeable,
                        "author": self._get_user_info(pr.user),
                        "assignees": [
                            self._get_user_info(assignee) for assignee in pr.assignees
                        ],
                        "requested_reviewers": (
                            [
                                self._get_user_info(reviewer)
                                for reviewer in pr.get_review_requests()[0]
                            ]
                            if hasattr(pr, "get_review_requests")
                            else []
                        ),
                        "labels": [label.name for label in pr.labels],
                        "template_fields": template_fields,
                        "references": references,
                        "comments": comments,
                        "reviews": reviews,
                        "commits": pr.commits,
                        "additions": pr.additions,
                        "deletions": pr.deletions,
                        "changed_files": pr.changed_files,
                        "quality_score": self._calculate_quality_score(pr),
                    },
                    "source": "github",
                    "url": pr.html_url,
                }

            return document

        except Exception as e:
            logger.error(f"Error processing pull request {pr.number}: {str(e)}")
            raise

    def _process_issue(self, issue: Issue) -> Dict[str, Any]:
        """
        Process an issue into a document.

        Args:
            issue: GitHub issue

        Returns:
            Dict[str, Any]: Processed document

        Raises:
            ValueError: If there's an error processing the issue
        """
        try:
            # Extract issue data
            issue_id = f"issue/{issue.number}"
            title = issue.title
            body = issue.body or ""

            # Prepare issue data for cleaning
            issue_data = {
                "id": issue.id,
                "number": issue.number,
                "title": title,
                "body": body,
                "state": issue.state,
                "created_at": (
                    issue.created_at.isoformat() if issue.created_at else None
                ),
                "updated_at": (
                    issue.updated_at.isoformat() if issue.updated_at else None
                ),
                "closed_at": issue.closed_at.isoformat() if issue.closed_at else None,
                "html_url": issue.html_url,
                "user": {
                    "id": issue.user.id,
                    "login": issue.user.login,
                    "avatar_url": issue.user.avatar_url,
                    "html_url": issue.user.html_url,
                },
                "labels": [
                    {"name": label.name, "color": label.color} for label in issue.labels
                ],
                "comments": issue.comments,
                "locked": issue.locked,
                "type": "issue",
            }

            # Add assignees if available
            if issue.assignees:
                issue_data["assignees"] = [
                    {
                        "id": assignee.id,
                        "login": assignee.login,
                        "avatar_url": assignee.avatar_url,
                        "html_url": assignee.html_url,
                    }
                    for assignee in issue.assignees
                ]

            # Add milestone if available
            if issue.milestone:
                issue_data["milestone"] = {
                    "id": issue.milestone.id,
                    "number": issue.milestone.number,
                    "title": issue.milestone.title,
                    "state": issue.milestone.state,
                    "created_at": (
                        issue.milestone.created_at.isoformat()
                        if issue.milestone.created_at
                        else None
                    ),
                    "updated_at": (
                        issue.milestone.updated_at.isoformat()
                        if issue.milestone.updated_at
                        else None
                    ),
                    "due_on": (
                        issue.milestone.due_on.isoformat()
                        if issue.milestone.due_on
                        else None
                    ),
                }

            # Process comments
            comments_data = []
            for comment in issue.get_comments():
                if not self._is_bot_comment(comment):
                    comment_data = {
                        "id": comment.id,
                        "body": comment.body,
                        "created_at": (
                            comment.created_at.isoformat()
                            if comment.created_at
                            else None
                        ),
                        "updated_at": (
                            comment.updated_at.isoformat()
                            if comment.updated_at
                            else None
                        ),
                        "html_url": comment.html_url,
                        "user": {
                            "id": comment.user.id,
                            "login": comment.user.login,
                            "avatar_url": comment.user.avatar_url,
                            "html_url": comment.user.html_url,
                        },
                    }

                    # Add reactions if available
                    if hasattr(comment, "reactions") and comment.reactions:
                        comment_data["reactions"] = {
                            "total_count": comment.reactions.total_count,
                            "+1": comment.reactions.plus1,
                            "-1": comment.reactions.minus1,
                            "laugh": comment.reactions.laugh,
                            "hooray": comment.reactions.hooray,
                            "confused": comment.reactions.confused,
                            "heart": comment.reactions.heart,
                            "rocket": comment.reactions.rocket,
                            "eyes": comment.reactions.eyes,
                        }

                    comments_data.append(comment_data)

            issue_data["comments_data"] = comments_data

            # Use the GitHub cleaners if available
            try:
                from ..cleaners.chunking import SemanticChunker
                from ..cleaners.github import (GitHubContentQualityAssessor,
                                               GitHubIssueCleaner)

                # Initialize cleaners
                issue_cleaner = GitHubIssueCleaner()
                quality_assessor = GitHubContentQualityAssessor()
                chunker = SemanticChunker(max_chunk_size=1000, overlap_size=100)

                # Clean and enhance the issue
                cleaned_issue = issue_cleaner.clean_issue(issue_data)

                # Assess quality
                quality = quality_assessor.assess_quality(cleaned_issue)
                cleaned_issue["quality"] = quality

                # Create chunks
                chunks = chunker.chunk_content(cleaned_issue)

                # Create document with enhanced data
                document = {
                    "id": issue_id,
                    "title": title,
                    "content": cleaned_issue.get("body", body),
                    "content_type": "github_issue",
                    "metadata": {
                        "number": issue.number,
                        "state": issue.state,
                        "created_at": issue.created_at.isoformat() if issue.created_at else None,
                        "updated_at": issue.updated_at.isoformat() if issue.updated_at else None,
                        "closed_at": issue.closed_at.isoformat() if issue.closed_at else None,
                        "author": self._get_user_info(issue.user),
                        "assignees": [
                            self._get_user_info(assignee)
                            for assignee in issue.assignees
                        ],
                        "labels": [label.name for label in issue.labels],
                        "comments": cleaned_issue.get("comments", []),
                        "quality_score": quality.get("quality_score", 0.0),
                        "is_high_quality": quality.get("is_high_quality", False),
                        "quality_reasons": quality.get("reasons", []),
                        "technical_entities": cleaned_issue.get(
                            "technical_entities", {}
                        ),
                        "chunks": chunks,
                    },
                    "source": "github",
                    "url": issue.html_url,
                }
            except ImportError:
                # Fall back to basic processing
                cleaned_content = self._clean_markdown_content(body)
                template_fields = self._extract_issue_template_fields(cleaned_content)
                references = self._extract_references(cleaned_content)

                # Process comments for basic document
                comments = []
                for comment in issue.get_comments():
                    if not self._is_bot_comment(comment):
                        comments.append(
                            {
                                "id": comment.id,
                                "user": self._get_user_info(comment.user),
                                "body": comment.body,
                                "created_at": comment.created_at.isoformat() if comment.created_at else None,
                                "updated_at": comment.updated_at.isoformat() if comment.updated_at else None,
                            }
                        )

                # Create basic document
                document = {
                    "id": issue_id,
                    "title": title,
                    "content": cleaned_content,
                    "content_type": "github_issue",
                    "metadata": {
                        "number": issue.number,
                        "state": issue.state,
                        "created_at": issue.created_at.isoformat() if issue.created_at else None,
                        "updated_at": issue.updated_at.isoformat() if issue.updated_at else None,
                        "closed_at": issue.closed_at.isoformat() if issue.closed_at else None,
                        "author": self._get_user_info(issue.user),
                        "assignees": [
                            self._get_user_info(assignee)
                            for assignee in issue.assignees
                        ],
                        "labels": [label.name for label in issue.labels],
                        "template_fields": template_fields,
                        "references": references,
                        "comments": comments,
                        "quality_score": self._calculate_quality_score(issue),
                    },
                    "source": "github",
                    "url": issue.html_url,
                }

            return document

        except Exception as e:
            logger.error(f"Error processing issue {issue.number}: {str(e)}")
            raise

    def _clean_markdown_content(self, content: str) -> str:
        """
        Clean and normalize markdown content.

        Args:
            content: Markdown content to clean

        Returns:
            str: Cleaned content
        """
        if not content:
            return ""

        # Remove HTML comments
        content = re.sub(r"<!--.*?-->", "", content, flags=re.DOTALL)

        # Normalize line endings
        content = content.replace("\r\n", "\n")

        # Remove excessive whitespace
        content = re.sub(r"\n{3,}", "\n\n", content)

        # Normalize code blocks
        content = re.sub(r"```\s*(\w+)\s*\n", r"```\1\n", content)

        return content.strip()

    def _extract_pr_template_fields(self, description: str) -> dict:
        """
        Extract structured fields from PR template.

        Args:
            description: PR description

        Returns:
            dict: Extracted template fields
        """
        fields = {}

        # Common PR template patterns
        patterns = {
            "summary": r"(?:## Summary|# Summary|### Summary)\s*\n(.*?)(?=\n##|\n#|\Z)",
            "type": r"(?:## Type|# Type|### Type).*?\n(.*?)(?=\n##|\n#|\Z)",
            "breaking_changes": r"(?:## Breaking Changes|# Breaking Changes).*?\n(.*?)(?=\n##|\n#|\Z)",
            "testing": r"(?:## Testing|# Testing|### Testing).*?\n(.*?)(?=\n##|\n#|\Z)",
            "checklist": r"(?:## Checklist|# Checklist).*?\n(.*?)(?=\n##|\n#|\Z)",
        }

        for field, pattern in patterns.items():
            match = re.search(pattern, description, re.MULTILINE | re.DOTALL)
            if match:
                fields[field] = match.group(1).strip()

        return fields

    def _extract_issue_template_fields(self, description: str) -> dict:
        """
        Extract structured fields from issue template.

        Args:
            description: Issue description

        Returns:
            dict: Extracted template fields
        """
        fields = {}

        # Detect template type
        template_type = self._detect_template_type(description)

        # Common issue template patterns
        patterns = {
            "bug_report": {
                "expected_behavior": r"(?:## Expected behavior|# Expected behavior).*?\n(.*?)(?=\n##|\n#|\Z)",
                "actual_behavior": r"(?:## Actual behavior|# Actual behavior).*?\n(.*?)(?=\n##|\n#|\Z)",
                "steps_to_reproduce": r"(?:## Steps to reproduce|# Steps to reproduce).*?\n(.*?)(?=\n##|\n#|\Z)",
                "environment": r"(?:## Environment|# Environment).*?\n(.*?)(?=\n##|\n#|\Z)",
            },
            "feature_request": {
                "problem": r"(?:## Problem|# Problem|### What problem).*?\n(.*?)(?=\n##|\n#|\Z)",
                "solution": r"(?:## Solution|# Solution|### Describe the solution).*?\n(.*?)(?=\n##|\n#|\Z)",
                "alternatives": r"(?:## Alternatives|# Alternatives).*?\n(.*?)(?=\n##|\n#|\Z)",
            },
        }

        if template_type in patterns:
            for field, pattern in patterns[template_type].items():
                match = re.search(pattern, description, re.MULTILINE | re.DOTALL)
                if match:
                    fields[field] = match.group(1).strip()

        return fields

    def _detect_template_type(self, description: str) -> str:
        """
        Detect the type of template used in the description.

        Args:
            description: Issue/PR description

        Returns:
            str: Template type
        """
        # Check for bug report template
        bug_indicators = [
            "expected behavior",
            "actual behavior",
            "steps to reproduce",
            "bug report",
        ]
        for indicator in bug_indicators:
            if indicator.lower() in description.lower():
                return "bug_report"

        # Check for feature request template
        feature_indicators = [
            "feature request",
            "proposed solution",
            "describe the solution",
        ]
        for indicator in feature_indicators:
            if indicator.lower() in description.lower():
                return "feature_request"

        # Default to generic template
        return "generic"

    def _extract_references(self, text: str) -> List[dict]:
        """
        Extract various types of references from text.

        Args:
            text: Text to extract references from

        Returns:
            List[dict]: List of references
        """
        references = []

        # Issue references (#123, fixes #456)
        issue_refs = re.findall(
            r"(?:closes|fixes|resolves|fix|close|resolve)\s*#(\d+)", text, re.IGNORECASE
        )
        for ref in issue_refs:
            references.append({"type": "fixes_issue", "id": ref, "url": f"#{ref}"})

        # PR references (#789)
        pr_refs = re.findall(r"(?<!fixes\s)(?<!closes\s)(?<!resolves\s)#(\d+)", text)
        for ref in pr_refs:
            references.append({"type": "references_pr", "id": ref, "url": f"#{ref}"})

        # External links
        urls = re.findall(r'https?://[^\s<>"{}|\\^`\[\]]+', text)
        for url in urls:
            references.append(
                {"type": "external_link", "url": url, "domain": urlparse(url).netloc}
            )

        return references

    def _is_bot_comment(self, comment) -> bool:
        """
        Check if a comment is from a bot.

        Args:
            comment: GitHub comment

        Returns:
            bool: True if the comment is from a bot, False otherwise
        """
        # Check if comment is None
        if comment is None:
            return False

        # Check if user is a bot
        if hasattr(comment, "user") and comment.user:
            # Check if user.login exists
            if not hasattr(comment.user, "login"):
                return False

            # GitHub marks bot users with '[bot]' in their login
            if "[bot]" in comment.user.login:
                return True

            # Common bot usernames
            bot_usernames = ["github-actions", "dependabot", "codecov", "renovate"]
            if any(bot in comment.user.login.lower() for bot in bot_usernames):
                return True

        # Check comment content for bot signatures
        if hasattr(comment, "body") and comment.body:
            bot_signatures = [
                "This PR was automatically created",
                "This issue was automatically created",
                "Automated pull request",
                "Created by GitHub Action",
            ]
            if any(sig in comment.body for sig in bot_signatures):
                return True

        return False

    def _get_user_info(self, user) -> dict:
        """
        Get user information.

        Args:
            user: GitHub user

        Returns:
            dict: User information
        """
        if not user:
            return {"login": "unknown", "name": "Unknown User", "avatar_url": None}

        # Initialize with default values
        user_info = {"login": "unknown", "name": "Unknown User", "avatar_url": None}

        # Add login if available
        if hasattr(user, "login"):
            user_info["login"] = user.login
            # Default name to login if name not available
            user_info["name"] = user.login

        # Add avatar_url if available
        if hasattr(user, "avatar_url"):
            user_info["avatar_url"] = user.avatar_url

        # Add name if available
        if hasattr(user, "name") and user.name:
            user_info["name"] = user.name

        return user_info

    def _calculate_quality_score(self, item) -> float:
        """
        Calculate a quality score for the item.

        Args:
            item: GitHub item (PR or issue)

        Returns:
            float: Quality score between 0 and 1
        """
        quality_score = 0.0

        # Content length and completeness
        body = item.body or ""
        if len(body) > 100:
            quality_score += 0.2

        # Has proper formatting (headers, lists, etc.)
        if re.search(r"#{1,6}\s+\w+", body) or re.search(r"[-*]\s+\w+", body):
            quality_score += 0.2

        # Contains technical details
        technical_indicators = [
            "code",
            "implementation",
            "bug",
            "error",
            "function",
            "method",
            "class",
            "api",
        ]
        if any(indicator in body.lower() for indicator in technical_indicators):
            quality_score += 0.2

        # Has engagement (comments)
        if hasattr(item, "comments") and item.comments > 0:
            quality_score += 0.1

        # Has labels
        if hasattr(item, "labels") and len(item.labels) > 0:
            quality_score += 0.1

        # Is recent (within last 30 days)
        if hasattr(item, "updated_at") and item.updated_at:
            try:
                # Use consistent UTC timezone handling
                from apps.core.utils.timezone_utils import ensure_utc, is_recent

                updated_at = ensure_utc(item.updated_at)
                if updated_at and is_recent(updated_at, days=30):
                    quality_score += 0.1
            except Exception as e:
                logger.warning(f"Error calculating recency score: {e}")

        return min(quality_score, 1.0)

    def _process_wiki_page(self, content, file_content: str, last_commit, repo: Repository) -> Dict[str, Any]:
        """
        Process a wiki page into a document.

        Args:
            content: GitHub content object
            file_content: Wiki page content
            last_commit: Last commit for the wiki page
            repo: GitHub repository

        Returns:
            Dict[str, Any]: Processed document
        """
        try:
            # Extract page name from filename
            page_name = content.name.replace('.md', '').replace('-', ' ').title()

            # Create document ID
            wiki_id = f"wiki/{content.name}"

            # Clean content
            cleaned_content = self._clean_markdown_content(file_content)

            # Extract metadata
            metadata = {
                "filename": content.name,
                "page_name": page_name,
                "size": content.size,
                "last_updated": last_commit.commit.author.date.isoformat() if last_commit.commit.author.date else None,
                "last_author": {
                    "name": last_commit.commit.author.name,
                    "email": last_commit.commit.author.email
                } if last_commit.commit.author else None,
                "commit_sha": last_commit.sha,
                "commit_message": last_commit.commit.message,
                "quality_score": self._calculate_wiki_quality_score(cleaned_content),
                "word_count": len(cleaned_content.split()),
                "has_code_blocks": bool(re.search(r'```', cleaned_content)),
                "has_links": bool(re.search(r'\[.*?\]\(.*?\)', cleaned_content)),
                "section_count": len(re.findall(r'^#{1,6}\s+', cleaned_content, re.MULTILINE))
            }

            # Create document
            document = {
                "id": wiki_id,
                "title": page_name,
                "content": cleaned_content,
                "content_type": "github_wiki",
                "metadata": metadata,
                "source": "github",
                "url": f"https://github.com/{repo.full_name}/wiki/{content.name.replace('.md', '')}"
            }

            return document

        except Exception as e:
            logger.error(f"Error processing wiki page {content.name}: {str(e)}")
            raise

    def _process_discussion(self, discussion, repo: Repository) -> Dict[str, Any]:
        """
        Process a discussion into a document.

        Args:
            discussion: GitHub discussion object
            repo: GitHub repository

        Returns:
            Dict[str, Any]: Processed document
        """
        try:
            # Create document ID
            discussion_id = f"discussion/{discussion.number}"

            # Extract content
            title = discussion.title
            body = discussion.body or ""
            cleaned_content = self._clean_markdown_content(body)

            # Extract metadata
            metadata = {
                "number": discussion.number,
                "state": getattr(discussion, 'state', 'open'),
                "created_at": discussion.created_at.isoformat() if discussion.created_at else None,
                "updated_at": discussion.updated_at.isoformat() if discussion.updated_at else None,
                "author": self._get_user_info(discussion.user),
                "labels": [label.name for label in getattr(discussion, 'labels', [])],
                "comments": getattr(discussion, 'comments', 0),
                "quality_score": self._calculate_quality_score(discussion),
                "category": getattr(discussion, 'category', {}).get('name', 'General') if hasattr(discussion, 'category') else 'General'
            }

            # Create document
            document = {
                "id": discussion_id,
                "title": title,
                "content": cleaned_content,
                "content_type": "github_discussion",
                "metadata": metadata,
                "source": "github",
                "url": discussion.html_url
            }

            return document

        except Exception as e:
            logger.error(f"Error processing discussion {discussion.number}: {str(e)}")
            raise

    def _process_release(self, release, repo: Repository) -> Dict[str, Any]:
        """
        Process a release into a document.

        Args:
            release: GitHub release object
            repo: GitHub repository

        Returns:
            Dict[str, Any]: Processed document
        """
        try:
            # Create document ID
            release_id = f"release/{release.tag_name}"

            # Extract content
            title = release.name or release.tag_name
            body = release.body or ""
            cleaned_content = self._clean_markdown_content(body)

            # Extract assets information
            assets = []
            for asset in release.get_assets():
                assets.append({
                    "name": asset.name,
                    "size": asset.size,
                    "download_count": asset.download_count,
                    "content_type": asset.content_type,
                    "browser_download_url": asset.browser_download_url,
                    "created_at": asset.created_at.isoformat() if asset.created_at else None,
                    "updated_at": asset.updated_at.isoformat() if asset.updated_at else None
                })

            # Extract metadata
            metadata = {
                "tag_name": release.tag_name,
                "name": release.name,
                "draft": release.draft,
                "prerelease": release.prerelease,
                "created_at": release.created_at.isoformat() if release.created_at else None,
                "published_at": release.published_at.isoformat() if release.published_at else None,
                "author": self._get_user_info(release.author),
                "target_commitish": release.target_commitish,
                "assets": assets,
                "assets_count": len(assets),
                "quality_score": self._calculate_release_quality_score(cleaned_content, assets),
                "has_changelog": bool(re.search(r'(changelog|changes|what.*new)', cleaned_content, re.IGNORECASE)),
                "has_breaking_changes": bool(re.search(r'breaking.*change', cleaned_content, re.IGNORECASE))
            }

            # Create document
            document = {
                "id": release_id,
                "title": title,
                "content": cleaned_content,
                "content_type": "github_release",
                "metadata": metadata,
                "source": "github",
                "url": release.html_url
            }

            return document

        except Exception as e:
            logger.error(f"Error processing release {release.tag_name}: {str(e)}")
            raise

    def _process_project(self, project, repo: Repository) -> Dict[str, Any]:
        """
        Process a project board into a document.

        Args:
            project: GitHub project object
            repo: GitHub repository

        Returns:
            Dict[str, Any]: Processed document
        """
        try:
            # Create document ID
            project_id = f"project/{project.id}"

            # Extract content
            title = project.name
            body = project.body or ""
            cleaned_content = self._clean_markdown_content(body)

            # Get project columns and cards
            columns_data = []
            try:
                columns = project.get_columns()
                for column in columns:
                    cards_data = []
                    try:
                        cards = column.get_cards()
                        for card in cards:
                            card_data = {
                                "id": card.id,
                                "note": card.note,
                                "created_at": card.created_at.isoformat() if card.created_at else None,
                                "updated_at": card.updated_at.isoformat() if card.updated_at else None,
                                "content_url": card.content_url,
                                "archived": card.archived
                            }
                            cards_data.append(card_data)
                    except Exception as e:
                        logger.warning(f"Error fetching cards for column {column.name}: {e}")

                    column_data = {
                        "id": column.id,
                        "name": column.name,
                        "created_at": column.created_at.isoformat() if column.created_at else None,
                        "updated_at": column.updated_at.isoformat() if column.updated_at else None,
                        "cards": cards_data,
                        "cards_count": len(cards_data)
                    }
                    columns_data.append(column_data)
            except Exception as e:
                logger.warning(f"Error fetching columns for project {project.name}: {e}")

            # Extract metadata
            metadata = {
                "project_id": project.id,
                "number": project.number,
                "state": project.state,
                "created_at": project.created_at.isoformat() if project.created_at else None,
                "updated_at": project.updated_at.isoformat() if project.updated_at else None,
                "creator": self._get_user_info(project.creator),
                "columns": columns_data,
                "columns_count": len(columns_data),
                "total_cards": sum(col.get('cards_count', 0) for col in columns_data),
                "quality_score": self._calculate_project_quality_score(cleaned_content, columns_data)
            }

            # Create document
            document = {
                "id": project_id,
                "title": title,
                "content": cleaned_content,
                "content_type": "github_project",
                "metadata": metadata,
                "source": "github",
                "url": project.html_url
            }

            return document

        except Exception as e:
            logger.error(f"Error processing project {project.name}: {str(e)}")
            raise

    def _process_workflow_run(self, run, workflow, repo: Repository) -> Dict[str, Any]:
        """
        Process a workflow run into a document (metadata only).

        Args:
            run: GitHub workflow run object
            workflow: GitHub workflow object
            repo: GitHub repository

        Returns:
            Dict[str, Any]: Processed document
        """
        try:
            # Create document ID
            workflow_id = f"workflow/{workflow.id}/run/{run.id}"

            # Extract content (metadata summary)
            title = f"{workflow.name} - Run #{run.run_number}"

            # Create content summary
            content_parts = [
                f"# {title}",
                f"**Status**: {run.status}",
                f"**Conclusion**: {run.conclusion or 'N/A'}",
                f"**Triggered by**: {run.event}",
                f"**Branch**: {run.head_branch}",
                f"**Commit**: {run.head_sha[:8]}",
                ""
            ]

            if run.conclusion == 'failure':
                content_parts.append("⚠️ **This workflow run failed**")
            elif run.conclusion == 'success':
                content_parts.append("✅ **This workflow run succeeded**")

            content = "\n".join(content_parts)

            # Get jobs information using the GitHub API directly
            jobs_data = []
            try:
                # Use the GitHub API directly to get jobs for this workflow run
                # This is a workaround since PyGithub doesn't have get_jobs() method
                import requests

                jobs_url = f"https://api.github.com/repos/{repo.full_name}/actions/runs/{run.id}/jobs"
                headers = {"Authorization": f"token {self.token}"}

                response = requests.get(jobs_url, headers=headers)
                if response.status_code == 200:
                    jobs_response = response.json()
                    for job in jobs_response.get('jobs', []):
                        job_data = {
                            "id": job.get('id'),
                            "name": job.get('name'),
                            "status": job.get('status'),
                            "conclusion": job.get('conclusion'),
                            "started_at": job.get('started_at'),
                            "completed_at": job.get('completed_at'),
                            "runner_name": job.get('runner_name'),
                            "runner_group_name": job.get('runner_group_name')
                        }
                        jobs_data.append(job_data)
                else:
                    logger.warning(f"Could not fetch jobs for workflow run {run.id}: HTTP {response.status_code}")
            except Exception as e:
                logger.warning(f"Error fetching jobs for workflow run {run.id}: {e}")

            # Extract metadata
            metadata = {
                "workflow_id": workflow.id,
                "workflow_name": workflow.name,
                "workflow_path": workflow.path,
                "run_id": run.id,
                "run_number": run.run_number,
                "status": run.status,
                "conclusion": run.conclusion,
                "event": run.event,
                "created_at": run.created_at.isoformat() if run.created_at else None,
                "updated_at": run.updated_at.isoformat() if run.updated_at else None,
                "run_started_at": run.run_started_at.isoformat() if run.run_started_at else None,
                "head_branch": run.head_branch,
                "head_sha": run.head_sha,
                "actor": self._get_user_info(run.actor),
                "triggering_actor": self._get_user_info(run.triggering_actor),
                "jobs": jobs_data,
                "jobs_count": len(jobs_data),
                "failed_jobs": len([j for j in jobs_data if j.get('conclusion') == 'failure']),
                "quality_score": self._calculate_workflow_quality_score(run, jobs_data)
            }

            # Create document
            document = {
                "id": workflow_id,
                "title": title,
                "content": content,
                "content_type": "github_workflow",
                "metadata": metadata,
                "source": "github",
                "url": run.html_url
            }

            return document

        except Exception as e:
            logger.error(f"Error processing workflow run {run.id}: {str(e)}")
            raise

    def _calculate_wiki_quality_score(self, content: str) -> float:
        """Calculate quality score for wiki pages."""
        score = 0.0

        # Content length
        if len(content) > 500:
            score += 0.3
        elif len(content) > 100:
            score += 0.1

        # Has proper structure
        if re.search(r'^#{1,6}\s+', content, re.MULTILINE):
            score += 0.2

        # Has code examples
        if re.search(r'```', content):
            score += 0.2

        # Has links
        if re.search(r'\[.*?\]\(.*?\)', content):
            score += 0.1

        # Has lists
        if re.search(r'^[-*+]\s+', content, re.MULTILINE):
            score += 0.1

        # Has tables
        if re.search(r'\|.*\|', content):
            score += 0.1

        return min(score, 1.0)

    def _calculate_release_quality_score(self, content: str, assets: list) -> float:
        """Calculate quality score for releases."""
        score = 0.0

        # Has release notes
        if len(content) > 100:
            score += 0.3

        # Has changelog format
        if re.search(r'(changelog|changes|what.*new)', content, re.IGNORECASE):
            score += 0.2

        # Has assets
        if assets:
            score += 0.2

        # Has version structure
        if re.search(r'v?\d+\.\d+\.\d+', content):
            score += 0.1

        # Has breaking changes section
        if re.search(r'breaking.*change', content, re.IGNORECASE):
            score += 0.1

        # Has proper formatting
        if re.search(r'^#{1,6}\s+', content, re.MULTILINE):
            score += 0.1

        return min(score, 1.0)

    def _calculate_project_quality_score(self, content: str, columns: list) -> float:
        """Calculate quality score for project boards."""
        score = 0.0

        # Has description
        if len(content) > 50:
            score += 0.2

        # Has multiple columns
        if len(columns) >= 3:
            score += 0.3
        elif len(columns) >= 1:
            score += 0.1

        # Has cards
        total_cards = sum(col.get('cards_count', 0) for col in columns)
        if total_cards >= 5:
            score += 0.3
        elif total_cards >= 1:
            score += 0.1

        # Has organized structure
        if any('done' in col.get('name', '').lower() for col in columns):
            score += 0.1

        return min(score, 1.0)

    def _calculate_workflow_quality_score(self, run, jobs: list) -> float:
        """Calculate quality score for workflow runs."""
        score = 0.0

        # Successful runs get higher score
        if run.conclusion == 'success':
            score += 0.4
        elif run.conclusion == 'failure':
            score += 0.1  # Failed runs still have value for debugging

        # Multiple jobs indicate complexity
        if len(jobs) >= 3:
            score += 0.2
        elif len(jobs) >= 1:
            score += 0.1

        # Recent runs are more valuable
        if run.created_at:
            from apps.core.utils.timezone_utils import ensure_utc, is_recent
            try:
                created_at = ensure_utc(run.created_at)
                if is_recent(created_at, days=7):
                    score += 0.2
                elif is_recent(created_at, days=30):
                    score += 0.1
            except Exception:
                pass

        # Important events get higher score
        important_events = ['push', 'pull_request', 'release']
        if run.event in important_events:
            score += 0.1

        return min(score, 1.0)
