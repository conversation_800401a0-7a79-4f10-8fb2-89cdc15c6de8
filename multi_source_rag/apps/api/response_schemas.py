"""
Standardized API response schemas for consistent API responses.

Provides type-safe, consistent response formats across all API endpoints
with proper error handling and metadata inclusion.
"""

from typing import TypedDict, Optional, List, Dict, Any, Union
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum


class ResponseStatus(Enum):
    """Standard response status values."""
    SUCCESS = "success"
    ERROR = "error"
    WARNING = "warning"
    PARTIAL = "partial"


class ErrorCode(Enum):
    """Standard error codes for API responses."""
    VALIDATION_ERROR = "VALIDATION_ERROR"
    INPUT_VALIDATION_ERROR = "INPUT_VALIDATION_ERROR"
    REQUEST_PROCESSING_ERROR = "REQUEST_PROCESSING_ERROR"
    AUTHENTICATION_ERROR = "AUTHENTICATION_ERROR"
    AUTHORIZATION_ERROR = "AUTHORIZATION_ERROR"
    RATE_LIMIT_ERROR = "RATE_LIMIT_ERROR"
    SEARCH_ERROR = "SEARCH_ERROR"
    INTERNAL_ERROR = "INTERNAL_ERROR"
    SERVICE_UNAVAILABLE = "SERVICE_UNAVAILABLE"


@dataclass
class Citation:
    """Standardized citation format."""
    id: str
    text: str
    relevance: float
    metadata: Dict[str, Any]
    rank: Optional[int] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)


@dataclass
class SearchMetrics:
    """Standardized search metrics."""
    retriever_score: float
    confidence_score: float
    processing_time: str
    sources_count: int
    is_fallback: bool
    cache_hit: Optional[bool] = None
    strategy_used: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)


@dataclass
class IntelligenceInfo:
    """Unified intelligence information for advanced search features."""
    strategy_used: Optional[str] = None
    domains_searched: Optional[List[str]] = None
    query_enhancements: Optional[Dict[str, str]] = None
    fusion_strategy: Optional[str] = None
    reasoning_steps: Optional[List[str]] = None
    cross_domain_routing: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {k: v for k, v in asdict(self).items() if v is not None}


@dataclass
class ResponseMetadata:
    """Standardized response metadata."""
    timestamp: str
    request_id: Optional[str] = None
    version: str = "1.0.0"
    tenant_slug: Optional[str] = None
    user_id: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {k: v for k, v in asdict(self).items() if v is not None}


class SearchResponse(TypedDict):
    """Standardized search response format."""
    status: str
    data: Dict[str, Any]
    metadata: Dict[str, Any]
    error: Optional[Dict[str, Any]]


class ErrorResponse(TypedDict):
    """Standardized error response format."""
    status: str
    message: str
    error_code: str
    details: Optional[Union[str, Dict[str, Any]]]
    metadata: Dict[str, Any]


class HealthResponse(TypedDict):
    """Standardized health check response format."""
    status: str
    timestamp: str
    metrics: Dict[str, Any]
    services: Optional[Dict[str, str]]


class ResponseBuilder:
    """
    Builder class for creating standardized API responses.
    
    Ensures consistent response formats across all API endpoints
    while providing flexibility for different response types.
    """
    
    @staticmethod
    def success_response(
        data: Dict[str, Any],
        metadata: Optional[ResponseMetadata] = None,
        intelligence_info: Optional[IntelligenceInfo] = None
    ) -> SearchResponse:
        """
        Build a successful search response.
        
        Args:
            data: Response data containing answer, citations, etc.
            metadata: Response metadata
            intelligence_info: Advanced intelligence information
            
        Returns:
            Standardized success response
        """
        if metadata is None:
            metadata = ResponseMetadata(timestamp=datetime.now().isoformat())
        
        response_data = data.copy()
        
        # Add intelligence info if provided
        if intelligence_info:
            response_data['intelligence_info'] = intelligence_info.to_dict()
        
        return SearchResponse(
            status=ResponseStatus.SUCCESS.value,
            data=response_data,
            metadata=metadata.to_dict(),
            error=None
        )
    
    @staticmethod
    def error_response(
        message: str,
        error_code: ErrorCode,
        details: Optional[Union[str, Dict[str, Any]]] = None,
        metadata: Optional[ResponseMetadata] = None,
        status_code: int = 400
    ) -> ErrorResponse:
        """
        Build an error response.
        
        Args:
            message: Human-readable error message
            error_code: Standardized error code
            details: Additional error details
            metadata: Response metadata
            status_code: HTTP status code
            
        Returns:
            Standardized error response
        """
        if metadata is None:
            metadata = ResponseMetadata(timestamp=datetime.now().isoformat())
        
        return ErrorResponse(
            status=ResponseStatus.ERROR.value,
            message=message,
            error_code=error_code.value,
            details=details,
            metadata=metadata.to_dict()
        )
    
    @staticmethod
    def search_response(
        query: str,
        answer: str,
        citations: List[Citation],
        metrics: SearchMetrics,
        intelligence_info: Optional[IntelligenceInfo] = None,
        metadata: Optional[ResponseMetadata] = None
    ) -> SearchResponse:
        """
        Build a standardized search response.
        
        Args:
            query: Original search query
            answer: Generated answer
            citations: List of citations
            metrics: Search performance metrics
            intelligence_info: Advanced intelligence information
            metadata: Response metadata
            
        Returns:
            Standardized search response
        """
        if metadata is None:
            metadata = ResponseMetadata(timestamp=datetime.now().isoformat())
        
        data = {
            "query": query,
            "answer": answer,
            "citations": [citation.to_dict() for citation in citations],
            "metrics": metrics.to_dict()
        }
        
        return ResponseBuilder.success_response(
            data=data,
            metadata=metadata,
            intelligence_info=intelligence_info
        )
    
    @staticmethod
    def health_response(
        status: str,
        metrics: Dict[str, Any],
        services: Optional[Dict[str, str]] = None
    ) -> HealthResponse:
        """
        Build a health check response.
        
        Args:
            status: Overall system status
            metrics: System metrics
            services: Service status information
            
        Returns:
            Standardized health response
        """
        return HealthResponse(
            status=status,
            timestamp=datetime.now().isoformat(),
            metrics=metrics,
            services=services
        )
    
    @staticmethod
    def partial_response(
        data: Dict[str, Any],
        warnings: List[str],
        metadata: Optional[ResponseMetadata] = None
    ) -> SearchResponse:
        """
        Build a partial success response with warnings.
        
        Args:
            data: Response data
            warnings: List of warning messages
            metadata: Response metadata
            
        Returns:
            Standardized partial response
        """
        if metadata is None:
            metadata = ResponseMetadata(timestamp=datetime.now().isoformat())
        
        response_data = data.copy()
        response_data['warnings'] = warnings
        
        return SearchResponse(
            status=ResponseStatus.PARTIAL.value,
            data=response_data,
            metadata=metadata.to_dict(),
            error=None
        )


def convert_legacy_response(legacy_response: Dict[str, Any]) -> SearchResponse:
    """
    Convert legacy response format to standardized format.
    
    Provides backward compatibility while migrating to new response format.
    
    Args:
        legacy_response: Legacy response in old format
        
    Returns:
        Standardized response format
    """
    # Extract data from legacy format
    query = legacy_response.get('data', {}).get('query', '')
    answer = legacy_response.get('data', {}).get('answer', '')
    
    # Convert sources to citations
    citations = []
    sources = legacy_response.get('data', {}).get('sources', [])
    for i, source in enumerate(sources):
        citation = Citation(
            id=str(source.get('id', i)),
            text=source.get('text', ''),
            relevance=source.get('relevance', 0.0),
            metadata=source.get('metadata', {}),
            rank=i + 1
        )
        citations.append(citation)
    
    # Convert metrics
    legacy_metrics = legacy_response.get('data', {}).get('metrics', {})
    metrics = SearchMetrics(
        retriever_score=legacy_metrics.get('retriever_score', 0.0),
        confidence_score=legacy_metrics.get('confidence_score', 0.0),
        processing_time=legacy_metrics.get('processing_time', '0.0s'),
        sources_count=legacy_metrics.get('sources_count', 0),
        is_fallback=legacy_metrics.get('is_fallback', False)
    )
    
    # Create metadata
    metadata = ResponseMetadata(
        timestamp=legacy_response.get('data', {}).get('timestamp', datetime.now().isoformat())
    )
    
    return ResponseBuilder.search_response(
        query=query,
        answer=answer,
        citations=citations,
        metrics=metrics,
        metadata=metadata
    )


def validate_response_schema(response: Dict[str, Any]) -> bool:
    """
    Validate that a response conforms to the standardized schema.
    
    Args:
        response: Response to validate
        
    Returns:
        True if valid, False otherwise
    """
    required_fields = ['status', 'data', 'metadata']
    
    # Check required fields
    for field in required_fields:
        if field not in response:
            return False
    
    # Validate status
    valid_statuses = [status.value for status in ResponseStatus]
    if response['status'] not in valid_statuses:
        return False
    
    # Validate metadata structure
    metadata = response.get('metadata', {})
    if not isinstance(metadata, dict) or 'timestamp' not in metadata:
        return False
    
    return True
