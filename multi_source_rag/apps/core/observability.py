"""
Production-grade observability system for RAG operations.

Provides comprehensive monitoring, metrics collection, and performance tracking
for search operations, caching systems, and system health.
"""

import time
import logging
import threading
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import defaultdict, deque
from contextlib import contextmanager

try:
    from prometheus_client import Counter, Histogram, Gauge, Info
    PROMETHEUS_AVAILABLE = True
except ImportError:
    PROMETHEUS_AVAILABLE = False

logger = logging.getLogger(__name__)


@dataclass
class SearchMetrics:
    """Metrics for a single search operation."""
    query: str
    user_id: int
    tenant_slug: str
    processing_time: float
    result_count: int
    cache_hit: bool
    error: Optional[str] = None
    timestamp: datetime = field(default_factory=datetime.now)


@dataclass
class SystemHealth:
    """System health metrics."""
    memory_usage_mb: float
    cpu_usage_percent: float
    active_connections: int
    cache_hit_rate: float
    error_rate: float
    avg_response_time: float
    timestamp: datetime = field(default_factory=datetime.now)


class MetricsCollector:
    """
    Production-grade metrics collection system.
    
    Collects, aggregates, and exposes metrics for monitoring and alerting.
    Thread-safe and designed for high-throughput environments.
    """
    
    def __init__(self):
        self._lock = threading.Lock()
        self._search_metrics = deque(maxlen=10000)  # Keep last 10k searches
        self._error_counts = defaultdict(int)
        self._response_times = deque(maxlen=1000)  # Keep last 1k response times
        self._cache_stats = defaultdict(lambda: {'hits': 0, 'misses': 0})
        
        # Initialize Prometheus metrics if available
        if PROMETHEUS_AVAILABLE:
            self._init_prometheus_metrics()
    
    def _init_prometheus_metrics(self):
        """Initialize Prometheus metrics."""
        self.search_requests_total = Counter(
            'rag_search_requests_total',
            'Total number of search requests',
            ['tenant', 'user_id', 'status']
        )
        
        self.search_duration_seconds = Histogram(
            'rag_search_duration_seconds',
            'Search request duration in seconds',
            ['tenant']
        )
        
        self.cache_hit_rate = Gauge(
            'rag_cache_hit_rate',
            'Cache hit rate percentage',
            ['cache_type']
        )
        
        self.active_searches = Gauge(
            'rag_active_searches',
            'Number of currently active searches'
        )
        
        self.system_info = Info(
            'rag_system_info',
            'RAG system information'
        )
        
        # Set system info
        self.system_info.info({
            'version': '1.0.0',
            'environment': 'production',
            'features': 'agentic_retrieval,cross_domain_search,hybrid_search'
        })
    
    def record_search(self, metrics: SearchMetrics):
        """Record search operation metrics."""
        with self._lock:
            self._search_metrics.append(metrics)
            self._response_times.append(metrics.processing_time)
            
            if metrics.error:
                self._error_counts[metrics.error] += 1
        
        # Update Prometheus metrics if available
        if PROMETHEUS_AVAILABLE:
            status = 'error' if metrics.error else 'success'
            self.search_requests_total.labels(
                tenant=metrics.tenant_slug,
                user_id=str(metrics.user_id),
                status=status
            ).inc()
            
            if not metrics.error:
                self.search_duration_seconds.labels(
                    tenant=metrics.tenant_slug
                ).observe(metrics.processing_time)
        
        # Log search operation
        if metrics.error:
            logger.error(f"Search failed for user {metrics.user_id}: {metrics.error}")
        else:
            logger.info(f"Search completed for user {metrics.user_id} in {metrics.processing_time:.2f}s")
    
    def record_cache_operation(self, cache_type: str, hit: bool):
        """Record cache operation."""
        with self._lock:
            if hit:
                self._cache_stats[cache_type]['hits'] += 1
            else:
                self._cache_stats[cache_type]['misses'] += 1
        
        # Update Prometheus metrics if available
        if PROMETHEUS_AVAILABLE:
            total_ops = self._cache_stats[cache_type]['hits'] + self._cache_stats[cache_type]['misses']
            if total_ops > 0:
                hit_rate = self._cache_stats[cache_type]['hits'] / total_ops * 100
                self.cache_hit_rate.labels(cache_type=cache_type).set(hit_rate)
    
    def get_system_health(self) -> SystemHealth:
        """Get current system health metrics."""
        with self._lock:
            # Calculate metrics from recent data
            recent_searches = [m for m in self._search_metrics 
                             if m.timestamp > datetime.now() - timedelta(minutes=5)]
            
            error_count = sum(1 for m in recent_searches if m.error)
            error_rate = (error_count / len(recent_searches) * 100) if recent_searches else 0
            
            avg_response_time = (sum(self._response_times) / len(self._response_times)) if self._response_times else 0
            
            # Calculate overall cache hit rate
            total_hits = sum(stats['hits'] for stats in self._cache_stats.values())
            total_ops = sum(stats['hits'] + stats['misses'] for stats in self._cache_stats.values())
            cache_hit_rate = (total_hits / total_ops * 100) if total_ops > 0 else 0
        
        return SystemHealth(
            memory_usage_mb=self._get_memory_usage(),
            cpu_usage_percent=self._get_cpu_usage(),
            active_connections=len(recent_searches),
            cache_hit_rate=cache_hit_rate,
            error_rate=error_rate,
            avg_response_time=avg_response_time
        )
    
    def get_search_stats(self, hours: int = 24) -> Dict[str, Any]:
        """Get search statistics for the specified time period."""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        with self._lock:
            recent_searches = [m for m in self._search_metrics if m.timestamp > cutoff_time]
        
        if not recent_searches:
            return {
                'total_searches': 0,
                'avg_response_time': 0,
                'error_rate': 0,
                'cache_hit_rate': 0,
                'top_errors': []
            }
        
        total_searches = len(recent_searches)
        errors = [m for m in recent_searches if m.error]
        cache_hits = [m for m in recent_searches if m.cache_hit]
        
        # Calculate top errors
        error_counts = defaultdict(int)
        for search in errors:
            error_counts[search.error] += 1
        
        top_errors = sorted(error_counts.items(), key=lambda x: x[1], reverse=True)[:5]
        
        return {
            'total_searches': total_searches,
            'avg_response_time': sum(m.processing_time for m in recent_searches) / total_searches,
            'error_rate': len(errors) / total_searches * 100,
            'cache_hit_rate': len(cache_hits) / total_searches * 100,
            'top_errors': top_errors,
            'searches_by_tenant': self._group_by_tenant(recent_searches),
            'response_time_percentiles': self._calculate_percentiles([m.processing_time for m in recent_searches])
        }
    
    def _group_by_tenant(self, searches: List[SearchMetrics]) -> Dict[str, int]:
        """Group searches by tenant."""
        tenant_counts = defaultdict(int)
        for search in searches:
            tenant_counts[search.tenant_slug] += 1
        return dict(tenant_counts)
    
    def _calculate_percentiles(self, values: List[float]) -> Dict[str, float]:
        """Calculate response time percentiles."""
        if not values:
            return {'p50': 0, 'p95': 0, 'p99': 0}
        
        sorted_values = sorted(values)
        n = len(sorted_values)
        
        return {
            'p50': sorted_values[int(n * 0.5)],
            'p95': sorted_values[int(n * 0.95)],
            'p99': sorted_values[int(n * 0.99)]
        }
    
    def _get_memory_usage(self) -> float:
        """Get current memory usage in MB."""
        try:
            import psutil
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        except ImportError:
            return 0.0
    
    def _get_cpu_usage(self) -> float:
        """Get current CPU usage percentage."""
        try:
            import psutil
            return psutil.cpu_percent(interval=1)
        except ImportError:
            return 0.0


class PerformanceMonitor:
    """
    Context manager for monitoring operation performance.
    
    Automatically tracks timing and records metrics for operations.
    """
    
    def __init__(self, metrics_collector: MetricsCollector, operation_name: str, **context):
        self.metrics_collector = metrics_collector
        self.operation_name = operation_name
        self.context = context
        self.start_time = None
        self.end_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        if PROMETHEUS_AVAILABLE and hasattr(self.metrics_collector, 'active_searches'):
            self.metrics_collector.active_searches.inc()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.end_time = time.time()
        processing_time = self.end_time - self.start_time
        
        if PROMETHEUS_AVAILABLE and hasattr(self.metrics_collector, 'active_searches'):
            self.metrics_collector.active_searches.dec()
        
        # Record error if exception occurred
        error = None
        if exc_type:
            error = f"{exc_type.__name__}: {str(exc_val)}"
        
        # Create search metrics if this is a search operation
        if self.operation_name == 'search' and 'user_id' in self.context:
            metrics = SearchMetrics(
                query=self.context.get('query', ''),
                user_id=self.context['user_id'],
                tenant_slug=self.context.get('tenant_slug', 'default'),
                processing_time=processing_time,
                result_count=self.context.get('result_count', 0),
                cache_hit=self.context.get('cache_hit', False),
                error=error
            )
            self.metrics_collector.record_search(metrics)


# Global metrics collector instance
metrics_collector = MetricsCollector()


@contextmanager
def monitor_search(user_id: int, query: str, tenant_slug: str = 'default'):
    """Context manager for monitoring search operations."""
    with PerformanceMonitor(
        metrics_collector, 
        'search',
        user_id=user_id,
        query=query,
        tenant_slug=tenant_slug
    ) as monitor:
        yield monitor


def record_cache_hit(cache_type: str):
    """Record a cache hit."""
    metrics_collector.record_cache_operation(cache_type, hit=True)


def record_cache_miss(cache_type: str):
    """Record a cache miss."""
    metrics_collector.record_cache_operation(cache_type, hit=False)


def get_health_check() -> Dict[str, Any]:
    """Get system health check data."""
    health = metrics_collector.get_system_health()
    
    return {
        'status': 'healthy' if health.error_rate < 5 else 'degraded',
        'timestamp': health.timestamp.isoformat(),
        'metrics': {
            'memory_usage_mb': health.memory_usage_mb,
            'cpu_usage_percent': health.cpu_usage_percent,
            'active_connections': health.active_connections,
            'cache_hit_rate': health.cache_hit_rate,
            'error_rate': health.error_rate,
            'avg_response_time': health.avg_response_time
        }
    }


def get_search_analytics(hours: int = 24) -> Dict[str, Any]:
    """Get search analytics for the specified time period."""
    return metrics_collector.get_search_stats(hours)
