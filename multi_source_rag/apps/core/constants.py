"""
Production-Grade Constants for RAG System
=========================================

Centralized constants to eliminate magic numbers and improve maintainability.
"""


class RAGConstants:
    """Constants for RAG operations."""
    
    # Query Processing
    DEFAULT_SIMILARITY_TOP_K = 10
    LARGE_SIMILARITY_TOP_K = 15
    MAX_QUERY_LENGTH = 1000
    MIN_QUERY_LENGTH = 1
    
    # Chunking Configuration
    CHUNK_SIZE = 2000
    CHUNK_OVERLAP = 200
    
    # LLM Configuration
    DEFAULT_TEMPERATURE = 0.1
    DEFAULT_MIN_RELEVANCE_SCORE = 0.10
    DEFAULT_CONFIDENCE_SCORE = 0.8
    
    # Caching
    MAX_QUERY_ENGINES_CACHE = 50
    SEARCH_CACHE_TIMEOUT = 3600  # 1 hour
    SERVICE_CACHE_TIMEOUT = 7200  # 2 hours
    
    # Performance
    MAX_CONCURRENT_REQUESTS = 10
    REQUEST_TIMEOUT_SECONDS = 30
    
    # Database
    MAX_SEARCH_RESULTS_PER_QUERY = 100
    DEFAULT_PAGINATION_SIZE = 20


class ValidationConstants:
    """Constants for input validation."""
    
    # User Input Limits
    MAX_QUERY_LENGTH = 1000
    MIN_USER_ID = 1
    MAX_TENANT_SLUG_LENGTH = 50
    
    # Content Limits
    MAX_DOCUMENT_SIZE_MB = 10
    MAX_BATCH_SIZE = 500
    
    # Error Messages
    EMPTY_QUERY_ERROR = "Query cannot be empty"
    QUERY_TOO_LONG_ERROR = f"Query too long (max {MAX_QUERY_LENGTH} characters)"
    INVALID_USER_ID_ERROR = "Invalid user_id"
    TENANT_NOT_FOUND_ERROR = "Tenant with slug '{}' not found"


class DatabaseConstants:
    """Constants for database operations."""
    
    # Field Defaults
    DEFAULT_RETRIEVER_SCORE = 0.8
    DEFAULT_LLM_CONFIDENCE = 0.8
    DEFAULT_RELEVANCE_SCORE = 0.0
    
    # Bulk Operations
    BULK_CREATE_BATCH_SIZE = 1000
    BULK_UPDATE_BATCH_SIZE = 500
    
    # Query Optimization
    SELECT_RELATED_DEPTH = 3
    PREFETCH_BATCH_SIZE = 100


class LlamaIndexConstants:
    """Constants for LlamaIndex configuration."""
    
    # Query Engine Settings
    RESPONSE_MODE = "tree_summarize"
    USE_ASYNC = False
    STREAMING = False
    
    # Retrieval Settings
    SIMILARITY_TOP_K = 10
    NUM_QUERIES_FOR_FUSION = 4
    
    # HyDE Settings
    INCLUDE_ORIGINAL_QUERY = True
    
    # Multi-step Reasoning
    MAX_SUB_QUESTIONS = 3
    
    # Hybrid Search
    VECTOR_WEIGHT = 0.7
    BM25_WEIGHT = 0.3
    RRF_K = 60  # Reciprocal Rank Fusion constant


class ErrorConstants:
    """Constants for error handling."""
    
    # Error Types
    VALIDATION_ERROR = "validation_error"
    DATABASE_ERROR = "database_error"
    RAG_ERROR = "rag_error"
    UNEXPECTED_ERROR = "unexpected_error"
    
    # Default Error Messages
    DEFAULT_ERROR_MESSAGE = "I apologize, but I encountered an error while searching. Please try again."
    TENANT_ERROR_MESSAGE = "Invalid tenant configuration. Please contact support."
    USER_ERROR_MESSAGE = "User authentication error. Please log in again."


class LoggingConstants:
    """Constants for logging configuration."""
    
    # Log Levels
    DEFAULT_LOG_LEVEL = "INFO"
    DEBUG_LOG_LEVEL = "DEBUG"
    
    # Log Formats
    SEARCH_LOG_FORMAT = "Search completed in {duration:.2f}s with {results_count} results"
    ERROR_LOG_FORMAT = "Error in {operation}: {error_message}"
    
    # Performance Logging
    SLOW_QUERY_THRESHOLD_SECONDS = 5.0
    LOG_PERFORMANCE_METRICS = True


class FeatureFlags:
    """Feature flags for enabling/disabling functionality."""
    
    # RAG Features
    ENABLE_HYDE_BY_DEFAULT = True
    ENABLE_HYBRID_SEARCH_BY_DEFAULT = True
    ENABLE_MULTI_STEP_BY_DEFAULT = False
    
    # Caching Features
    ENABLE_SEARCH_CACHING = True
    ENABLE_SERVICE_CACHING = True
    ENABLE_EMBEDDING_CACHING = True
    
    # Logging Features
    ENABLE_SEARCH_LOGGING = True
    ENABLE_PERFORMANCE_LOGGING = True
    ENABLE_DEBUG_LOGGING = False
    
    # Validation Features
    ENABLE_STRICT_VALIDATION = True
    ENABLE_INPUT_SANITIZATION = True


# Export commonly used constants for convenience
DEFAULT_TOP_K = RAGConstants.DEFAULT_SIMILARITY_TOP_K
MAX_QUERY_LENGTH = ValidationConstants.MAX_QUERY_LENGTH
DEFAULT_TEMPERATURE = RAGConstants.DEFAULT_TEMPERATURE
DEFAULT_CONFIDENCE = DatabaseConstants.DEFAULT_LLM_CONFIDENCE
