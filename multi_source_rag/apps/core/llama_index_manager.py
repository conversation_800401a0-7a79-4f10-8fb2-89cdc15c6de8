"""
Unified LlamaIndex Manager
=========================
Consolidates all LlamaIndex functionality into a single, well-organized manager.
Replaces scattered custom implementations with battle-tested LlamaIndex components.
"""

import logging
from contextlib import contextmanager
from typing import Any, Dict, List, Optional

from llama_index.core import VectorStoreIndex, Settings
from llama_index.core.query_engine import TransformQueryEngine, SubQuestionQueryEngine
from llama_index.core.indices.query.query_transform import HyDEQueryTransform
from llama_index.core.retrievers import QueryFusionRetriever, VectorIndexRetriever
from llama_index.core.response_synthesizers import get_response_synthesizer
from llama_index.core.tools import QueryEngineTool
from llama_index.retrievers.bm25 import BM25Retriever

from apps.core.utils.collection_manager import get_collection_name

# Import types for method signatures and lazy loading
# These are only imported for type hints and lazy-loaded properties
from apps.core.retrieval import (
    RetrievalMode, SophisticationLevel, DataDomain, FusionStrategy,
    DomainResult, FusionResult, SearchExecution
)

logger = logging.getLogger(__name__)


class RAGError(Exception):
    """Base exception for RAG operations"""
    pass


class EmbeddingError(RAGError):
    """Embedding-related errors"""
    pass


class RetrievalError(RAGError):
    """Retrieval-related errors"""
    pass


class LlamaIndexManager:
    """
    Unified manager for all LlamaIndex operations.

    Provides a single interface for:
    - Query engines with HyDE transformation
    - Hybrid retrieval (vector + BM25)
    - Multi-step reasoning
    - Response synthesis
    - Citation tracking
    """

    def __init__(self, tenant_slug: str):
        """
        Initialize the LlamaIndex manager with resource management.

        Args:
            tenant_slug: Tenant identifier for collection naming
        """
        self.tenant_slug = tenant_slug
        self._indices = {}
        self._query_engines = {}
        self._initialized = False

        # Resource management
        from apps.core.utils.resource_manager import ResourceManager
        self._resource_manager = ResourceManager(f"llama_index_manager_{tenant_slug}")

        # Lazy initialization to avoid circular imports
        self._strategy_router = None
        self._config_manager = None
        self._cross_domain_router = None
        self._query_enhancer = None
        self._result_fusion_engine = None
        self._unified_search_engine = None

        # Initialize global settings
        self._initialize_global_settings()

    def _initialize_global_settings(self):
        """Initialize global LlamaIndex settings with production configuration."""
        try:
            # Initialize cached embedding wrapper for better performance
            from apps.core.utils.cached_embedding_wrapper import get_cached_embedding_wrapper
            self._embedding_wrapper = get_cached_embedding_wrapper(self.tenant_slug)

            # Warm up the embedding model
            self._embedding_wrapper.warmup()

            # Use single source of truth for embedding model
            from apps.core.utils.embedding_config import get_embedding_model
            embedding_model = get_embedding_model()  # Already warmed up by wrapper
            Settings.embed_model = embedding_model

            # Register embedding model for resource management
            self._resource_manager.register_resource(
                embedding_model,
                lambda: self._cleanup_embedding_model(embedding_model)
            )

            # Set LLM - import locally to avoid circular imports
            from apps.core.utils.llama_index_llm import get_llm
            Settings.llm = get_llm()

            # Configure chunk settings
            Settings.chunk_size = 2000
            Settings.chunk_overlap = 200

            self._initialized = True
            logger.info(f"✅ LlamaIndex manager initialized for tenant: {self.tenant_slug}")

        except Exception as e:
            logger.error(f"CRITICAL: Failed to initialize LlamaIndex settings: {e}")
            raise RAGError(f"Initialization failed: {e}")

    def _cleanup_embedding_model(self, embedding_model):
        """Clean up embedding model resources."""
        try:
            if hasattr(embedding_model, 'cleanup'):
                embedding_model.cleanup()
            elif hasattr(embedding_model, 'close'):
                embedding_model.close()
            logger.info("Cleaned up embedding model resources")
        except Exception as e:
            logger.error(f"Error cleaning up embedding model: {e}")

    @property
    def strategy_router(self):
        """Lazy-loaded strategy router to avoid circular imports"""
        if self._strategy_router is None:
            from apps.core.retrieval import RetrievalStrategyRouter, RoutingMethod
            self._strategy_router = RetrievalStrategyRouter(self.tenant_slug, RoutingMethod.HYBRID)
        return self._strategy_router

    @property
    def config_manager(self):
        """Lazy-loaded config manager to avoid circular imports"""
        if self._config_manager is None:
            from apps.core.retrieval import RetrievalConfigManager
            self._config_manager = RetrievalConfigManager(self.tenant_slug)
        return self._config_manager

    @property
    def cross_domain_router(self):
        """Lazy-loaded cross-domain router to avoid circular imports"""
        if self._cross_domain_router is None:
            from apps.core.retrieval import CrossDomainRouter
            self._cross_domain_router = CrossDomainRouter(self.tenant_slug)
        return self._cross_domain_router

    @property
    def query_enhancer(self):
        """Lazy-loaded query enhancer to avoid circular imports"""
        if self._query_enhancer is None:
            from apps.core.retrieval import DomainQueryEnhancer
            self._query_enhancer = DomainQueryEnhancer(self.tenant_slug)
        return self._query_enhancer

    @property
    def result_fusion_engine(self):
        """Lazy-loaded result fusion engine to avoid circular imports"""
        if self._result_fusion_engine is None:
            from apps.core.retrieval import ResultFusionEngine
            self._result_fusion_engine = ResultFusionEngine(self.tenant_slug)
        return self._result_fusion_engine

    @property
    def unified_search_engine(self):
        """Lazy-loaded unified search engine to avoid circular imports"""
        if self._unified_search_engine is None:
            from apps.core.retrieval import UnifiedAgenticSearchEngine
            self._unified_search_engine = UnifiedAgenticSearchEngine(self.tenant_slug)
        return self._unified_search_engine

    def get_query_engine(self, intent: str = "default", enable_hyde: bool = True,
                        enable_hybrid: bool = True, enable_multi_step: bool = False, **kwargs):
        """
        Get an optimized query engine with all features.

        Args:
            intent: Query intent for specialized handling
            enable_hyde: Enable HyDE query transformation
            enable_hybrid: Enable hybrid retrieval (vector + BM25)
            enable_multi_step: Enable multi-step reasoning

        Returns:
            Query engine with requested features
        """
        cache_key = f"{self.tenant_slug}_{intent}_{enable_hyde}_{enable_hybrid}_{enable_multi_step}"

        if cache_key in self._query_engines:
            return self._query_engines[cache_key]

        try:
            # Get base index
            index = self._get_or_create_index(intent)

            # Create retriever with configurable top_k
            # Use top_k from kwargs or default to 20 for better result coverage
            top_k = kwargs.get('top_k', 20)

            if enable_hybrid:
                retriever = self._create_hybrid_retriever(index, intent, top_k)
            else:
                retriever = VectorIndexRetriever(index=index, similarity_top_k=top_k)

            # Create response synthesizer
            response_synthesizer = get_response_synthesizer(
                response_mode="tree_summarize",
                streaming=False,
                use_async=False
            )

            # Create base query engine directly to avoid parameter conflicts
            from llama_index.core.query_engine import RetrieverQueryEngine
            base_engine = RetrieverQueryEngine(
                retriever=retriever,
                response_synthesizer=response_synthesizer
            )

            # Add HyDE transformation if enabled
            if enable_hyde:
                base_engine = TransformQueryEngine(
                    base_engine,
                    query_transform=HyDEQueryTransform(include_original=True)
                )

            # Add multi-step reasoning if enabled
            if enable_multi_step:
                base_engine = self._create_multi_step_engine(base_engine, intent)

            # Cache and return
            self._query_engines[cache_key] = base_engine
            return base_engine

        except Exception as e:
            logger.error(f"Failed to create query engine: {e}")
            raise RAGError(f"Query engine creation failed: {e}")

    def _get_or_create_index(self, intent: str) -> VectorStoreIndex:
        """Get or create vector index for the given intent."""
        if intent in self._indices:
            return self._indices[intent]

        try:
            collection_name = get_collection_name(self.tenant_slug, intent=intent)
            # Import locally to avoid circular imports
            from apps.core.utils.llama_index_vectorstore import get_vector_store
            vector_store = get_vector_store(collection_name=collection_name)

            # Use single source of truth for embedding model
            from apps.core.utils.embedding_config import get_embedding_model
            embedding_model = get_embedding_model()

            # Create index from existing vector store
            index = VectorStoreIndex.from_vector_store(
                vector_store=vector_store,
                embed_model=embedding_model
            )

            # Register resources for cleanup
            self._resource_manager.register_resource(
                vector_store,
                lambda: self._cleanup_vector_store(vector_store)
            )
            self._resource_manager.register_resource(
                index,
                lambda: self._cleanup_index(index)
            )

            self._indices[intent] = index
            logger.info(f"✅ Created index for intent: {intent} with production embedding model")
            return index

        except Exception as e:
            logger.error(f"CRITICAL: Failed to create index for intent {intent}: {e}")
            raise EmbeddingError(f"Index creation failed: {e}")

    def _cleanup_vector_store(self, vector_store):
        """Clean up vector store resources."""
        try:
            if hasattr(vector_store, 'close'):
                vector_store.close()
            elif hasattr(vector_store, 'disconnect'):
                vector_store.disconnect()
            logger.info("Cleaned up vector store resources")
        except Exception as e:
            logger.error(f"Error cleaning up vector store: {e}")

    def _cleanup_index(self, index):
        """Clean up index resources."""
        try:
            if hasattr(index, 'close'):
                index.close()
            logger.info("Cleaned up index resources")
        except Exception as e:
            logger.error(f"Error cleaning up index: {e}")

    def _create_hybrid_retriever(self, index: VectorStoreIndex, intent: str, top_k: int = 20) -> QueryFusionRetriever:
        """Create hybrid retriever combining vector and BM25 search."""
        try:
            # Vector retriever with configurable top_k
            vector_retriever = VectorIndexRetriever(
                index=index,
                similarity_top_k=top_k
            )

            # Check if docstore has documents before creating BM25
            docstore = index.docstore
            documents = list(docstore.docs.values()) if docstore.docs else []

            if not documents:
                logger.warning(f"No documents in docstore for intent {intent}, using vector-only retriever")
                # Return vector-only fusion retriever
                fusion_retriever = QueryFusionRetriever(
                    retrievers=[vector_retriever],
                    mode="reciprocal_rerank",
                    num_queries=2  # Reduced for single retriever
                )
                return fusion_retriever

            # Create BM25 retriever - BM25 is guaranteed to be available
            bm25_retriever = BM25Retriever.from_defaults(
                docstore=docstore,
                similarity_top_k=top_k
            )

            # Fusion retriever with both vector and BM25
            fusion_retriever = QueryFusionRetriever(
                retrievers=[vector_retriever, bm25_retriever],
                mode="reciprocal_rerank",
                num_queries=4  # Automatic query expansion
            )

            logger.info(f"✅ Created hybrid retriever (vector + BM25) for intent: {intent}")
            return fusion_retriever

        except Exception as e:
            logger.error(f"Failed to create hybrid retriever: {e}")
            raise RetrievalError(f"Hybrid retriever creation failed: {e}")

    def _create_multi_step_engine(self, base_engine, intent: str) -> SubQuestionQueryEngine:
        """Create multi-step reasoning engine."""
        try:
            query_engine_tool = QueryEngineTool.from_defaults(
                query_engine=base_engine,
                name=f"{intent}_search",
                description=f"Search {intent} documents for relevant information"
            )

            sub_question_engine = SubQuestionQueryEngine.from_defaults(
                query_engine_tools=[query_engine_tool]
            )

            logger.info(f"Created multi-step engine for intent: {intent}")
            return sub_question_engine

        except Exception as e:
            logger.error(f"Failed to create multi-step engine: {e}")
            raise RAGError(f"Multi-step engine creation failed: {e}")

    def search(self, query: str, intent: str = "default", **kwargs) -> Dict[str, Any]:
        """
        Perform search with full feature set.

        Args:
            query: Search query
            intent: Query intent
            **kwargs: Additional search parameters

        Returns:
            Search results with citations
        """
        try:
            # Get appropriate query engine with top_k parameter
            engine = self.get_query_engine(
                intent=intent,
                enable_hyde=kwargs.get('enable_hyde', True),
                enable_hybrid=kwargs.get('enable_hybrid', True),
                enable_multi_step=kwargs.get('enable_multi_step', False),
                top_k=kwargs.get('top_k', 20)
            )

            # Execute query
            response = engine.query(query)

            # Format response with citations
            return {
                'answer': str(response),
                'source_nodes': response.source_nodes,
                'metadata': response.metadata,
                'intent': intent,
                'query': query
            }

        except Exception as e:
            logger.error(f"Search failed for query '{query}': {e}")
            raise RAGError(f"Search failed: {e}")

    def agentic_search(self, query: str, intent: str = "default",
                      sophistication: Optional[SophisticationLevel] = None,
                      explicit_mode: Optional[RetrievalMode] = None,
                      **kwargs) -> Dict[str, Any]:
        """
        Perform agentic search with intelligent strategy routing.

        Args:
            query: Search query
            intent: Query intent
            sophistication: Optional sophistication level override
            explicit_mode: Explicitly requested retrieval mode
            **kwargs: Additional search parameters

        Returns:
            Enhanced search results with strategy information
        """
        try:
            # Get configuration for this intent and sophistication level
            config = self.config_manager.get_config(intent, sophistication)

            # Route query to optimal strategy
            routing_decision = self.strategy_router.route_query(
                query=query,
                intent=intent,
                explicit_mode=explicit_mode,
                **kwargs
            )

            # Create strategy instance
            strategy = self.strategy_router.create_strategy(
                routing_decision.strategy,
                intent
            )

            # Retrieve documents using selected strategy
            nodes = strategy.retrieve(
                query=query,
                top_k=config.similarity_top_k,
                **routing_decision.parameters
            )

            # If we have nodes, create response using query engine
            if nodes:
                # Get query engine with configuration
                engine = self.get_query_engine(
                    intent=intent,
                    enable_hyde=config.enable_hyde,
                    enable_hybrid=config.enable_hybrid,
                    enable_multi_step=config.enable_multi_step
                )

                # Execute query with retrieved nodes as context
                response = engine.query(query)

                # Enhanced response with strategy information
                return {
                    'answer': str(response),
                    'source_nodes': response.source_nodes,
                    'metadata': {
                        **response.metadata,
                        'retrieval_strategy': routing_decision.strategy.value,
                        'strategy_confidence': routing_decision.confidence,
                        'strategy_reasoning': routing_decision.reasoning,
                        'sophistication_level': sophistication.value if sophistication else 'default',
                        'config_used': config.__dict__ if config.show_retrieval_strategy else {}
                    },
                    'intent': intent,
                    'query': query,
                    'agentic_info': {
                        'strategy_used': strategy.get_strategy_name(),
                        'strategy_description': strategy.get_strategy_description(),
                        'routing_confidence': routing_decision.confidence,
                        'fallback_strategy': routing_decision.fallback_strategy.value if routing_decision.fallback_strategy else None
                    }
                }
            else:
                # Fallback to standard search if no nodes retrieved
                logger.warning(f"No nodes retrieved with strategy {routing_decision.strategy.value}, falling back to standard search")
                return self.search(query, intent, **kwargs)

        except Exception as e:
            logger.error(f"Agentic search failed for query '{query}': {e}")
            # Fallback to standard search
            logger.info("Falling back to standard search due to agentic search failure")
            return self.search(query, intent, **kwargs)

    def cross_domain_search(self, query: str, intent: str = "default",
                           available_domains: Optional[List[DataDomain]] = None,
                           sophistication: Optional[SophisticationLevel] = None,
                           fusion_strategy: Optional[FusionStrategy] = None,
                           **kwargs) -> Dict[str, Any]:
        """
        Perform cross-domain search with intelligent domain routing and result fusion.

        Args:
            query: Search query
            intent: Query intent
            available_domains: Optional list of available domains to search
            sophistication: Optional sophistication level override
            fusion_strategy: Optional result fusion strategy
            **kwargs: Additional search parameters

        Returns:
            Enhanced search results with cross-domain intelligence
        """
        try:
            # Get configuration for this intent and sophistication level
            config = self.config_manager.get_config(intent, sophistication)

            # Route query to optimal domains
            domain_routing = self.cross_domain_router.route_cross_domain_query(
                query=query,
                intent=intent,
                available_domains=available_domains,
                **kwargs
            )

            # Determine all domains to search
            all_domains = domain_routing.primary_domains + domain_routing.secondary_domains

            # Enhance query for each domain
            query_enhancements = self.query_enhancer.enhance_queries_for_multiple_domains(
                query, all_domains
            )

            # Search each domain with enhanced queries
            domain_results = []
            for domain in all_domains:
                try:
                    enhanced_query = query_enhancements[domain].enhanced_query

                    # Map domain to collection/intent
                    domain_intent = self._map_domain_to_intent(domain, intent)

                    # Perform search for this domain
                    domain_search_results = self.agentic_search(
                        query=enhanced_query,
                        intent=domain_intent,
                        sophistication=sophistication,
                        **kwargs
                    )

                    # Create domain result
                    domain_result = DomainResult(
                        domain=domain,
                        nodes=domain_search_results.get('source_nodes', []),
                        query_used=enhanced_query,
                        confidence=query_enhancements[domain].confidence,
                        metadata={
                            'enhancement_type': query_enhancements[domain].enhancement_type,
                            'keywords_added': query_enhancements[domain].keywords_added,
                            'original_query': query
                        }
                    )

                    domain_results.append(domain_result)

                except Exception as e:
                    logger.warning(f"Search failed for domain {domain.value}: {e}")
                    continue

            # Fuse results from all domains
            fusion_strategy = fusion_strategy or self._select_fusion_strategy(domain_routing.fusion_strategy)
            fused_results = self.result_fusion_engine.fuse_domain_results(
                domain_results=domain_results,
                strategy=fusion_strategy,
                target_count=config.similarity_top_k,
                diversity_weight=0.3
            )

            # Create comprehensive response
            return {
                'answer': self._generate_cross_domain_answer(fused_results, query),
                'source_nodes': fused_results.fused_nodes,
                'metadata': {
                    'cross_domain_routing': {
                        'primary_domains': [d.value for d in domain_routing.primary_domains],
                        'secondary_domains': [d.value for d in domain_routing.secondary_domains],
                        'routing_confidence': domain_routing.confidence,
                        'routing_reasoning': domain_routing.reasoning
                    },
                    'query_enhancements': {
                        domain.value: {
                            'enhanced_query': enhancement.enhanced_query,
                            'enhancement_type': enhancement.enhancement_type,
                            'confidence': enhancement.confidence
                        }
                        for domain, enhancement in query_enhancements.items()
                    },
                    'result_fusion': {
                        'strategy': fused_results.fusion_strategy.value,
                        'domain_contributions': {d.value: count for d, count in fused_results.domain_contributions.items()},
                        'diversity_score': fused_results.diversity_score,
                        'fusion_confidence': fused_results.confidence
                    },
                    'sophistication_level': sophistication.value if sophistication else 'default'
                },
                'intent': intent,
                'query': query,
                'cross_domain_info': {
                    'domains_searched': len(domain_results),
                    'total_results_before_fusion': sum(len(dr.nodes) for dr in domain_results),
                    'final_result_count': len(fused_results.fused_nodes),
                    'fusion_reasoning': fused_results.reasoning
                }
            }

        except Exception as e:
            logger.error(f"Cross-domain search failed for query '{query}': {e}")
            # Fallback to agentic search
            logger.info("Falling back to agentic search due to cross-domain search failure")
            return self.agentic_search(query, intent, sophistication, **kwargs)

    def _map_domain_to_intent(self, domain: DataDomain, base_intent: str) -> str:
        """Map data domain to appropriate search intent."""
        domain_intent_mapping = {
            DataDomain.GITHUB_CODE: 'code',
            DataDomain.GITHUB_ISSUES: 'technical',
            DataDomain.GITHUB_WIKI: 'document',
            DataDomain.GITHUB_DISCUSSIONS: 'conversational',
            DataDomain.SLACK_CONVERSATIONS: 'conversational',
            DataDomain.SLACK_ENGINEERING: 'technical',
            DataDomain.SLACK_SUPPORT: 'technical',
            DataDomain.DOCUMENTATION: 'document',
            DataDomain.MEETING_NOTES: 'document',
            DataDomain.CUSTOMER_DOCS: 'document',
        }

        return domain_intent_mapping.get(domain, base_intent)

    def _select_fusion_strategy(self, suggested_strategy: str) -> FusionStrategy:
        """Select appropriate fusion strategy based on suggestion."""
        strategy_mapping = {
            'weighted_merge': FusionStrategy.WEIGHTED_MERGE,
            'problem_resolution_merge': FusionStrategy.PROBLEM_RESOLUTION,
            'process_flow_merge': FusionStrategy.PROCESS_FLOW,
            'comprehensive_merge': FusionStrategy.COMPREHENSIVE,
        }

        return strategy_mapping.get(suggested_strategy, FusionStrategy.WEIGHTED_MERGE)

    def _generate_cross_domain_answer(self, fused_results: FusionResult, query: str) -> str:
        """Generate answer from fused cross-domain results."""
        if not fused_results.fused_nodes:
            return "I couldn't find relevant information across the available data sources."

        # Use the existing query engine to generate answer from fused nodes
        try:
            # Create a temporary query engine with fused nodes
            engine = self.get_query_engine(intent="default")
            response = engine.query(query)
            return str(response)
        except Exception as e:
            logger.warning(f"Failed to generate cross-domain answer: {e}")
            # Fallback to simple concatenation
            return f"Found {len(fused_results.fused_nodes)} relevant results across {len(fused_results.domain_contributions)} data sources."

    def ultimate_search(self, query: str, intent: str = "default",
                       sophistication: Optional[SophisticationLevel] = None,
                       user_context: Optional[Dict[str, Any]] = None,
                       **kwargs) -> Dict[str, Any]:
        """
        Ultimate search using unified agentic search engine with full intelligence.

        This is the most advanced search method that automatically:
        - Analyzes query complexity
        - Orchestrates optimal strategy selection
        - Executes cross-domain search with intelligent fusion
        - Monitors and adapts based on result quality
        - Provides comprehensive metadata and reasoning

        Args:
            query: Search query
            intent: Query intent for context
            sophistication: Optional sophistication level override
            user_context: Optional user context for personalization
            **kwargs: Additional search parameters

        Returns:
            Comprehensive search results with full intelligence metadata
        """
        try:
            # Execute unified agentic search
            search_execution = self.unified_search_engine.search(
                query=query,
                intent=intent,
                sophistication=sophistication,
                user_context=user_context,
                **kwargs
            )

            # Generate answer from results
            answer = self._generate_ultimate_answer(search_execution, query)

            # Create comprehensive response
            return {
                'answer': answer,
                'source_nodes': search_execution.results,
                'metadata': {
                    'intent': intent,
                    'query': query,
                    'tenant_slug': self.tenant_slug,
                    'sophistication_level': search_execution.sophistication.value,
                    'strategy_decision': {
                        'retrieval_mode': search_execution.strategy_decision.retrieval_mode.value if search_execution.strategy_decision else 'unknown',
                        'domains': [d.value for d in search_execution.strategy_decision.domains] if search_execution.strategy_decision else [],
                        'fusion_strategy': search_execution.strategy_decision.fusion_strategy.value if search_execution.strategy_decision else 'unknown',
                        'complexity_level': search_execution.strategy_decision.complexity_level.value if search_execution.strategy_decision else 'unknown',
                        'confidence': search_execution.strategy_confidence,
                        'reasoning_chain': search_execution.strategy_decision.reasoning_chain if search_execution.strategy_decision else []
                    },
                    'execution_metrics': {
                        'execution_time_ms': search_execution.execution_time_ms,
                        'result_quality_score': search_execution.result_quality_score,
                        'fallback_used': search_execution.fallback_used,
                        'strategy_used': search_execution.execution_metadata.get('strategy_used', 'primary'),
                        'adaptive_improvement': search_execution.execution_metadata.get('adaptive_improvement')
                    },
                    'performance_predictions': {
                        'estimated_latency_ms': search_execution.strategy_decision.estimated_latency_ms if search_execution.strategy_decision else 0,
                        'estimated_relevance': search_execution.strategy_decision.estimated_relevance if search_execution.strategy_decision else 0.0,
                        'estimated_diversity': search_execution.strategy_decision.estimated_diversity if search_execution.strategy_decision else 0.0
                    }
                },
                'ultimate_search_info': {
                    'total_results': len(search_execution.results),
                    'domains_searched': len(search_execution.domain_results),
                    'quality_score': search_execution.result_quality_score,
                    'intelligence_level': 'ultimate',
                    'error_details': search_execution.error_details
                }
            }

        except Exception as e:
            logger.error(f"Ultimate search failed for query '{query}': {e}")
            # Fallback to cross-domain search
            logger.info("Falling back to cross-domain search due to ultimate search failure")
            return self.cross_domain_search(query, intent, sophistication=sophistication, **kwargs)

    def _generate_ultimate_answer(self, search_execution: SearchExecution, query: str) -> str:
        """Generate ultimate answer from search execution results."""
        if search_execution.error_details:
            return f"I encountered an issue while searching: {search_execution.error_details}"

        if not search_execution.results:
            return "I couldn't find relevant information for your query. You might want to try rephrasing your question or checking if the information exists in the knowledge base."

        # Use the existing query engine to generate answer from results
        try:
            # Create a temporary query engine with results
            engine = self.get_query_engine(intent="default")
            response = engine.query(query)

            # Enhance answer with intelligence metadata
            base_answer = str(response)

            # Add quality and confidence information
            quality_info = ""
            if search_execution.result_quality_score > 0.8:
                quality_info = " (High confidence result)"
            elif search_execution.result_quality_score > 0.6:
                quality_info = " (Moderate confidence result)"
            elif search_execution.result_quality_score > 0.4:
                quality_info = " (Lower confidence result - you may want to refine your query)"
            else:
                quality_info = " (Limited confidence - consider rephrasing your question)"

            return base_answer + quality_info

        except Exception as e:
            logger.warning(f"Failed to generate ultimate answer: {e}")
            # Fallback to result summary
            result_count = len(search_execution.results)
            domain_count = len(search_execution.domain_results)

            if domain_count > 1:
                return f"Found {result_count} relevant results across {domain_count} data sources. The search used {search_execution.strategy_decision.complexity_level.value if search_execution.strategy_decision else 'standard'} complexity analysis."
            else:
                return f"Found {result_count} relevant results. Quality score: {search_execution.result_quality_score:.2f}"

    def set_sophistication_level(self, intent: str, level: SophisticationLevel) -> None:
        """
        Set sophistication level for an intent.

        Args:
            intent: Query intent
            level: Sophistication level to apply
        """
        self.config_manager.set_sophistication_level(intent, level)
        logger.info(f"Set sophistication level {level.value} for intent {intent}")

    def update_retrieval_config(self, intent: str, config_updates: Dict[str, Any],
                               sophistication: Optional[SophisticationLevel] = None) -> None:
        """
        Update retrieval configuration for specific intent.

        Args:
            intent: Query intent to update
            config_updates: Dictionary of configuration updates
            sophistication: Optional sophistication level
        """
        self.config_manager.update_config(intent, config_updates, sophistication)
        logger.info(f"Updated retrieval config for {intent}")

    def get_available_sophistication_levels(self) -> List[Dict[str, Any]]:
        """Get available sophistication levels for UI."""
        return self.config_manager.get_available_sophistication_levels()

    def get_retrieval_config(self, intent: str = "default",
                           sophistication: Optional[SophisticationLevel] = None) -> Dict[str, Any]:
        """
        Get current retrieval configuration.

        Args:
            intent: Query intent
            sophistication: Optional sophistication level

        Returns:
            Configuration dictionary for UI display
        """
        return self.config_manager.export_config(intent, sophistication)

    def clear_strategy_cache(self) -> None:
        """Clear the strategy routing cache."""
        self.strategy_router.clear_cache()
        logger.info("Cleared strategy routing cache")

    def get_strategy_stats(self) -> Dict[str, Any]:
        """Get strategy routing statistics."""
        return self.strategy_router.get_cache_stats()

    def cleanup_resources(self, force: bool = False):
        """Clean up resources and caches with proper resource management."""
        try:
            # Only cleanup if forced or if we have too many cached items
            if not force:
                total_cached_items = len(self._indices) + len(self._query_engines)
                if total_cached_items < 5:  # Only cleanup when we have many cached items
                    return

            logger.info(f"Starting resource cleanup for tenant: {self.tenant_slug}")

            # Clear only excess cached items (keep the most recent ones)
            if len(self._indices) > 2:
                # Keep only the 2 most recently used indices
                items_to_remove = list(self._indices.keys())[:-2]
                for key in items_to_remove:
                    self._indices.pop(key, None)

            if len(self._query_engines) > 2:
                # Keep only the 2 most recently used query engines
                items_to_remove = list(self._query_engines.keys())[:-2]
                for key in items_to_remove:
                    self._query_engines.pop(key, None)

            # Only clear caches if forced
            if force:
                # Clear agentic retrieval caches if initialized
                if self._strategy_router is not None:
                    self.strategy_router.clear_cache()

                # Clear cross-domain intelligence caches if initialized
                if self._cross_domain_router is not None:
                    self.cross_domain_router.clear_cache()
                if self._query_enhancer is not None:
                    self.query_enhancer.clear_cache()
                if self._result_fusion_engine is not None:
                    self.result_fusion_engine.clear_cache()

                # Clear unified search engine resources if initialized
                if self._unified_search_engine is not None:
                    self.unified_search_engine.cleanup_resources()

                # Use resource manager for comprehensive cleanup
                self._resource_manager.cleanup_resources()

            logger.info(f"✅ Resource cleanup completed for tenant: {self.tenant_slug}")
        except Exception as e:
            logger.error(f"Error during cleanup for tenant {self.tenant_slug}: {e}")

    def __del__(self):
        """Automatic cleanup when manager is destroyed."""
        try:
            self.cleanup_resources()
        except Exception as e:
            logger.error(f"Error in automatic cleanup for tenant {self.tenant_slug}: {e}")


@contextmanager
def llama_index_manager_context(tenant_slug: str):
    """
    Context manager for LlamaIndex manager with automatic cleanup.

    Args:
        tenant_slug: Tenant identifier

    Yields:
        LlamaIndexManager instance
    """
    manager = None
    try:
        manager = LlamaIndexManager(tenant_slug)
        yield manager
    finally:
        if manager:
            manager.cleanup_resources()


# Convenience functions for backward compatibility
def get_query_engine(tenant_slug: str, intent: str = "default", **kwargs):
    """Get query engine for tenant and intent."""
    manager = LlamaIndexManager(tenant_slug)
    return manager.get_query_engine(intent=intent, **kwargs)


def search_documents(tenant_slug: str, query: str, intent: str = "default", **kwargs):
    """Search documents with full feature set."""
    with llama_index_manager_context(tenant_slug) as manager:
        return manager.search(query, intent=intent, **kwargs)


def agentic_search_documents(tenant_slug: str, query: str, intent: str = "default",
                           sophistication: Optional[SophisticationLevel] = None,
                           explicit_mode: Optional[RetrievalMode] = None, **kwargs):
    """Search documents with agentic retrieval strategies."""
    with llama_index_manager_context(tenant_slug) as manager:
        return manager.agentic_search(
            query=query,
            intent=intent,
            sophistication=sophistication,
            explicit_mode=explicit_mode,
            **kwargs
        )


def cross_domain_search_documents(tenant_slug: str, query: str, intent: str = "default",
                                 available_domains: Optional[List[DataDomain]] = None,
                                 sophistication: Optional[SophisticationLevel] = None,
                                 fusion_strategy: Optional[FusionStrategy] = None, **kwargs):
    """Search documents with cross-domain intelligence."""
    with llama_index_manager_context(tenant_slug) as manager:
        return manager.cross_domain_search(
            query=query,
            intent=intent,
            available_domains=available_domains,
            sophistication=sophistication,
            fusion_strategy=fusion_strategy,
            **kwargs
        )


def ultimate_search_documents(tenant_slug: str, query: str, intent: str = "default",
                             sophistication: Optional[SophisticationLevel] = None,
                             user_context: Optional[Dict[str, Any]] = None, **kwargs):
    """Ultimate search with full agentic intelligence and automatic optimization."""
    with llama_index_manager_context(tenant_slug) as manager:
        return manager.ultimate_search(
            query=query,
            intent=intent,
            sophistication=sophistication,
            user_context=user_context,
            **kwargs
        )
