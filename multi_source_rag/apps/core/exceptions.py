"""
Production-Grade Exception Classes for RAG System
=================================================

Comprehensive exception hierarchy for proper error handling and debugging.
"""


class RAGError(Exception):
    """
    Base exception for all RAG operations.
    
    Provides structured error handling with error codes and context.
    """
    
    def __init__(self, message: str, error_code: str = None, context: dict = None):
        """
        Initialize RAG error.
        
        Args:
            message: Human-readable error message
            error_code: Machine-readable error code
            context: Additional context for debugging
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code or "RAG_ERROR"
        self.context = context or {}
    
    def __str__(self):
        return f"[{self.error_code}] {self.message}"
    
    def to_dict(self):
        """Convert exception to dictionary for API responses."""
        return {
            'error': True,
            'error_type': self.error_code,
            'message': self.message,
            'context': self.context
        }


class ValidationError(RAGError):
    """
    Input validation errors.
    
    Raised when user input doesn't meet validation criteria.
    """
    
    def __init__(self, message: str, field: str = None, value=None):
        """
        Initialize validation error.
        
        Args:
            message: Validation error message
            field: Field that failed validation
            value: Invalid value (sanitized for logging)
        """
        context = {}
        if field:
            context['field'] = field
        if value is not None:
            # Sanitize value for logging (truncate if too long)
            sanitized_value = str(value)[:100] if len(str(value)) > 100 else str(value)
            context['value'] = sanitized_value
        
        super().__init__(message, "VALIDATION_ERROR", context)
        self.field = field
        self.value = value


class DatabaseError(RAGError):
    """
    Database-related errors.
    
    Raised when database operations fail.
    """
    
    def __init__(self, message: str, operation: str = None, model: str = None):
        """
        Initialize database error.
        
        Args:
            message: Database error message
            operation: Database operation that failed
            model: Model involved in the operation
        """
        context = {}
        if operation:
            context['operation'] = operation
        if model:
            context['model'] = model
        
        super().__init__(message, "DATABASE_ERROR", context)
        self.operation = operation
        self.model = model


class EmbeddingError(RAGError):
    """
    Embedding-related errors.
    
    Raised when embedding operations fail.
    """
    
    def __init__(self, message: str, model_name: str = None, text_length: int = None):
        """
        Initialize embedding error.
        
        Args:
            message: Embedding error message
            model_name: Name of embedding model
            text_length: Length of text being embedded
        """
        context = {}
        if model_name:
            context['model_name'] = model_name
        if text_length is not None:
            context['text_length'] = text_length
        
        super().__init__(message, "EMBEDDING_ERROR", context)
        self.model_name = model_name
        self.text_length = text_length


class RetrievalError(RAGError):
    """
    Retrieval-related errors.
    
    Raised when document retrieval fails.
    """
    
    def __init__(self, message: str, collection_name: str = None, query: str = None):
        """
        Initialize retrieval error.
        
        Args:
            message: Retrieval error message
            collection_name: Vector collection name
            query: Search query (truncated for logging)
        """
        context = {}
        if collection_name:
            context['collection_name'] = collection_name
        if query:
            # Truncate query for logging
            context['query'] = query[:100] if len(query) > 100 else query
        
        super().__init__(message, "RETRIEVAL_ERROR", context)
        self.collection_name = collection_name
        self.query = query


class LLMError(RAGError):
    """
    LLM-related errors.
    
    Raised when LLM operations fail.
    """
    
    def __init__(self, message: str, model_name: str = None, prompt_length: int = None):
        """
        Initialize LLM error.
        
        Args:
            message: LLM error message
            model_name: Name of LLM model
            prompt_length: Length of prompt sent to LLM
        """
        context = {}
        if model_name:
            context['model_name'] = model_name
        if prompt_length is not None:
            context['prompt_length'] = prompt_length
        
        super().__init__(message, "LLM_ERROR", context)
        self.model_name = model_name
        self.prompt_length = prompt_length


class ConfigurationError(RAGError):
    """
    Configuration-related errors.
    
    Raised when system configuration is invalid.
    """
    
    def __init__(self, message: str, config_key: str = None, config_value=None):
        """
        Initialize configuration error.
        
        Args:
            message: Configuration error message
            config_key: Configuration key that's invalid
            config_value: Invalid configuration value
        """
        context = {}
        if config_key:
            context['config_key'] = config_key
        if config_value is not None:
            context['config_value'] = str(config_value)
        
        super().__init__(message, "CONFIGURATION_ERROR", context)
        self.config_key = config_key
        self.config_value = config_value


class TenantError(RAGError):
    """
    Tenant-related errors.
    
    Raised when tenant operations fail.
    """
    
    def __init__(self, message: str, tenant_slug: str = None):
        """
        Initialize tenant error.
        
        Args:
            message: Tenant error message
            tenant_slug: Tenant slug that caused the error
        """
        context = {}
        if tenant_slug:
            context['tenant_slug'] = tenant_slug
        
        super().__init__(message, "TENANT_ERROR", context)
        self.tenant_slug = tenant_slug


class AuthenticationError(RAGError):
    """
    Authentication-related errors.
    
    Raised when user authentication fails.
    """
    
    def __init__(self, message: str, user_id: int = None):
        """
        Initialize authentication error.
        
        Args:
            message: Authentication error message
            user_id: User ID that failed authentication
        """
        context = {}
        if user_id is not None:
            context['user_id'] = user_id
        
        super().__init__(message, "AUTHENTICATION_ERROR", context)
        self.user_id = user_id


class RateLimitError(RAGError):
    """
    Rate limiting errors.
    
    Raised when rate limits are exceeded.
    """
    
    def __init__(self, message: str, limit: int = None, window: int = None):
        """
        Initialize rate limit error.
        
        Args:
            message: Rate limit error message
            limit: Rate limit threshold
            window: Time window in seconds
        """
        context = {}
        if limit is not None:
            context['limit'] = limit
        if window is not None:
            context['window'] = window
        
        super().__init__(message, "RATE_LIMIT_ERROR", context)
        self.limit = limit
        self.window = window


# Convenience functions for common error scenarios
def validation_error(message: str, field: str = None, value=None) -> ValidationError:
    """Create a validation error with standard formatting."""
    return ValidationError(message, field, value)


def database_error(message: str, operation: str = None, model: str = None) -> DatabaseError:
    """Create a database error with standard formatting."""
    return DatabaseError(message, operation, model)


def tenant_not_found_error(tenant_slug: str) -> TenantError:
    """Create a standard tenant not found error."""
    return TenantError(f"Tenant with slug '{tenant_slug}' not found", tenant_slug)


def user_not_found_error(user_id: int) -> AuthenticationError:
    """Create a standard user not found error."""
    return AuthenticationError(f"User with ID {user_id} not found", user_id)


def query_too_long_error(query_length: int, max_length: int) -> ValidationError:
    """Create a standard query too long error."""
    return ValidationError(
        f"Query too long ({query_length} characters, max {max_length})",
        field="query",
        value=f"length:{query_length}"
    )


def empty_query_error() -> ValidationError:
    """Create a standard empty query error."""
    return ValidationError("Query cannot be empty", field="query", value="empty")
