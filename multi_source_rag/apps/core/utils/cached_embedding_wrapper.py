"""
High-Performance Cached Embedding Wrapper

This module provides a high-performance wrapper around embedding models
that implements intelligent caching, batching, and optimization strategies.
"""

import time
import logging
import hashlib
from typing import List, Dict, Any, Optional, Union
from concurrent.futures import ThreadPoolExecutor, as_completed
from threading import Lock

from apps.core.performance_cache import get_performance_cache
from apps.core.utils.embedding_config import get_embedding_model, get_model_config

logger = logging.getLogger(__name__)

class CachedEmbeddingWrapper:
    """
    High-performance embedding wrapper with intelligent caching.
    
    Features:
    - Automatic embedding caching with TTL
    - Batch processing for multiple texts
    - Warmup cache integration
    - Performance monitoring
    - Thread-safe operations
    """
    
    def __init__(self, tenant_slug: str):
        self.tenant_slug = tenant_slug
        self.cache = get_performance_cache(tenant_slug)
        self._model = None
        self._model_lock = Lock()
        self._stats = {
            'cache_hits': 0,
            'cache_misses': 0,
            'total_embeddings': 0,
            'total_time': 0.0
        }
        
        # Get model configuration
        self.model_name, self.dimensions = get_model_config()
    
    @property
    def model(self):
        """Lazy-loaded embedding model with thread safety."""
        if self._model is None:
            with self._model_lock:
                if self._model is None:
                    logger.info(f"Loading embedding model for tenant: {self.tenant_slug}")
                    self._model = get_embedding_model(warmup=True)
                    logger.info(f"✅ Embedding model loaded and warmed up")
        return self._model
    
    def get_text_embedding(self, text: str, use_cache: bool = True) -> List[float]:
        """
        Get embedding for a single text with caching.
        
        Args:
            text: Text to embed
            use_cache: Whether to use caching
            
        Returns:
            List[float]: Embedding vector
        """
        start_time = time.time()
        
        try:
            # Check cache first if enabled
            if use_cache:
                cached_embedding = self.cache.get_embedding(text, self.model_name)
                if cached_embedding:
                    self._stats['cache_hits'] += 1
                    self._stats['total_embeddings'] += 1
                    logger.debug(f"Cache HIT for embedding: {text[:30]}...")
                    return cached_embedding
            
            # Generate embedding
            embedding = self.model.get_text_embedding(text)
            
            # Cache the result if enabled
            if use_cache:
                self.cache.set_embedding(text, self.model_name, embedding)
                self._stats['cache_misses'] += 1
            
            # Update stats
            self._stats['total_embeddings'] += 1
            self._stats['total_time'] += time.time() - start_time
            
            return embedding
            
        except Exception as e:
            logger.error(f"Error generating embedding for text: {e}")
            raise
    
    def get_text_embeddings(self, texts: List[str], use_cache: bool = True, 
                           batch_size: Optional[int] = None) -> List[List[float]]:
        """
        Get embeddings for multiple texts with intelligent batching and caching.
        
        Args:
            texts: List of texts to embed
            use_cache: Whether to use caching
            batch_size: Optional batch size override
            
        Returns:
            List[List[float]]: List of embedding vectors
        """
        if not texts:
            return []
        
        start_time = time.time()
        embeddings = []
        texts_to_embed = []
        cached_indices = {}
        
        # Check cache for each text if enabled
        if use_cache:
            for i, text in enumerate(texts):
                cached_embedding = self.cache.get_embedding(text, self.model_name)
                if cached_embedding:
                    cached_indices[i] = cached_embedding
                    self._stats['cache_hits'] += 1
                else:
                    texts_to_embed.append((i, text))
                    self._stats['cache_misses'] += 1
        else:
            texts_to_embed = list(enumerate(texts))
        
        # Generate embeddings for non-cached texts
        if texts_to_embed:
            # Use model's batch processing if available
            if hasattr(self.model, 'get_text_embeddings'):
                batch_texts = [text for _, text in texts_to_embed]
                batch_embeddings = self.model.get_text_embeddings(batch_texts)
                
                # Cache the results
                if use_cache:
                    for (i, text), embedding in zip(texts_to_embed, batch_embeddings):
                        self.cache.set_embedding(text, self.model_name, embedding)
                        cached_indices[i] = embedding
                else:
                    for (i, _), embedding in zip(texts_to_embed, batch_embeddings):
                        cached_indices[i] = embedding
            else:
                # Fallback to individual embeddings
                for i, text in texts_to_embed:
                    embedding = self.get_text_embedding(text, use_cache=use_cache)
                    cached_indices[i] = embedding
        
        # Reconstruct embeddings in original order
        embeddings = [cached_indices[i] for i in range(len(texts))]
        
        # Update stats
        self._stats['total_embeddings'] += len(texts)
        self._stats['total_time'] += time.time() - start_time
        
        logger.info(f"Generated {len(texts)} embeddings ({len(texts_to_embed)} new, "
                   f"{len(cached_indices) - len(texts_to_embed)} cached)")
        
        return embeddings
    
    def get_query_embedding(self, query: str) -> List[float]:
        """
        Get embedding for a query with special handling.
        
        Args:
            query: Query text to embed
            
        Returns:
            List[float]: Query embedding vector
        """
        # Queries are often repeated, so always use cache
        return self.get_text_embedding(query, use_cache=True)
    
    def precompute_embeddings(self, texts: List[str], batch_size: int = 32) -> None:
        """
        Precompute and cache embeddings for a list of texts.
        
        Args:
            texts: List of texts to precompute embeddings for
            batch_size: Batch size for processing
        """
        logger.info(f"Precomputing embeddings for {len(texts)} texts...")
        
        # Process in batches
        for i in range(0, len(texts), batch_size):
            batch = texts[i:i + batch_size]
            try:
                self.get_text_embeddings(batch, use_cache=True)
                logger.info(f"Precomputed batch {i//batch_size + 1}/{(len(texts) + batch_size - 1)//batch_size}")
            except Exception as e:
                logger.error(f"Error precomputing batch {i//batch_size + 1}: {e}")
        
        logger.info(f"✅ Precomputation completed for {len(texts)} texts")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get caching and performance statistics."""
        total_requests = self._stats['cache_hits'] + self._stats['cache_misses']
        cache_hit_rate = (self._stats['cache_hits'] / total_requests * 100) if total_requests > 0 else 0
        avg_time = (self._stats['total_time'] / self._stats['total_embeddings']) if self._stats['total_embeddings'] > 0 else 0
        
        return {
            'tenant_slug': self.tenant_slug,
            'model_name': self.model_name,
            'dimensions': self.dimensions,
            'cache_hits': self._stats['cache_hits'],
            'cache_misses': self._stats['cache_misses'],
            'cache_hit_rate': f"{cache_hit_rate:.1f}%",
            'total_embeddings': self._stats['total_embeddings'],
            'total_time': f"{self._stats['total_time']:.2f}s",
            'average_time_per_embedding': f"{avg_time:.3f}s"
        }
    
    def clear_cache(self) -> None:
        """Clear the embedding cache."""
        self.cache.clear_cache('embedding')
        logger.info(f"Embedding cache cleared for tenant: {self.tenant_slug}")
    
    def warmup(self, common_queries: Optional[List[str]] = None) -> None:
        """
        Warm up the embedding model with common queries.
        
        Args:
            common_queries: Optional list of common queries to warm up with
        """
        if common_queries is None:
            common_queries = [
                "authentication",
                "API configuration", 
                "user management",
                "database setup",
                "error handling",
                "security implementation",
                "system architecture",
                "performance optimization"
            ]
        
        logger.info(f"Warming up embedding model with {len(common_queries)} queries...")
        
        try:
            # Precompute embeddings for common queries
            self.get_text_embeddings(common_queries, use_cache=True)
            logger.info(f"✅ Embedding model warmup completed")
        except Exception as e:
            logger.warning(f"Embedding warmup failed: {e}")

# Global cache instances per tenant
_embedding_wrappers = {}
_wrapper_lock = Lock()

def get_cached_embedding_wrapper(tenant_slug: str) -> CachedEmbeddingWrapper:
    """
    Get or create a cached embedding wrapper for the tenant.
    
    Args:
        tenant_slug: Tenant identifier
        
    Returns:
        CachedEmbeddingWrapper: Cached embedding wrapper instance
    """
    global _embedding_wrappers, _wrapper_lock
    
    if tenant_slug not in _embedding_wrappers:
        with _wrapper_lock:
            if tenant_slug not in _embedding_wrappers:
                _embedding_wrappers[tenant_slug] = CachedEmbeddingWrapper(tenant_slug)
    
    return _embedding_wrappers[tenant_slug]

def clear_all_embedding_caches() -> None:
    """Clear all embedding caches across all tenants."""
    global _embedding_wrappers
    
    for wrapper in _embedding_wrappers.values():
        wrapper.clear_cache()
    
    logger.info("All embedding caches cleared")
