"""
Centralized Timezone Utilities

This module provides consistent timezone handling across the entire system.
It eliminates timezone-related bugs by ensuring all datetime operations use UTC
and provides utilities for safe timezone conversion.

Key Features:
- Consistent UTC conversion for all datetime objects
- Safe timezone-aware datetime comparisons
- Proper handling of naive and timezone-aware datetimes
- Production-ready error handling and validation
"""

import logging
from datetime import datetime, timezone as dt_timezone
from typing import Optional, Union

from django.utils import timezone

logger = logging.getLogger(__name__)


def ensure_utc(dt: Optional[datetime]) -> Optional[datetime]:
    """
    Convert any datetime to UTC consistently.
    
    This function handles both naive and timezone-aware datetimes,
    ensuring consistent UTC conversion across the system.
    
    Args:
        dt: Datetime object to convert (can be None, naive, or timezone-aware)
        
    Returns:
        Optional[datetime]: UTC datetime or None if input was None
    """
    if dt is None:
        return None
    
    try:
        if dt.tzinfo is None:
            # Naive datetime - assume it's in UTC and make it timezone-aware
            logger.debug(f"Converting naive datetime to UTC: {dt}")
            return dt.replace(tzinfo=dt_timezone.utc)
        else:
            # Timezone-aware datetime - convert to UTC
            logger.debug(f"Converting timezone-aware datetime to UTC: {dt}")
            return dt.astimezone(dt_timezone.utc)
    except Exception as e:
        logger.error(f"Error converting datetime to UTC: {dt}, error: {e}")
        # Return current UTC time as fallback
        return datetime.now(dt_timezone.utc)


def ensure_timezone_aware(dt: Optional[datetime]) -> Optional[datetime]:
    """
    Ensure datetime is timezone-aware using Django's timezone utilities.
    
    This function is compatible with Django's timezone handling while
    ensuring consistent UTC conversion.
    
    Args:
        dt: Datetime object to make timezone-aware
        
    Returns:
        Optional[datetime]: Timezone-aware datetime or None if input was None
    """
    if dt is None:
        return None
    
    try:
        if timezone.is_naive(dt):
            # Use Django's make_aware but force UTC timezone
            logger.debug(f"Making naive datetime timezone-aware (UTC): {dt}")
            return timezone.make_aware(dt, timezone=dt_timezone.utc)
        else:
            # Already timezone-aware, convert to UTC
            logger.debug(f"Converting timezone-aware datetime to UTC: {dt}")
            return dt.astimezone(dt_timezone.utc)
    except Exception as e:
        logger.error(f"Error making datetime timezone-aware: {dt}, error: {e}")
        # Return current UTC time as fallback
        return timezone.now()


def safe_datetime_comparison(dt1: Optional[datetime], dt2: Optional[datetime]) -> Optional[bool]:
    """
    Safely compare two datetime objects with proper timezone handling.
    
    Args:
        dt1: First datetime object
        dt2: Second datetime object
        
    Returns:
        Optional[bool]: True if dt1 > dt2, False if dt1 <= dt2, None if comparison fails
    """
    if dt1 is None or dt2 is None:
        logger.warning("Cannot compare datetime objects: one or both are None")
        return None
    
    try:
        # Ensure both datetimes are in UTC
        dt1_utc = ensure_utc(dt1)
        dt2_utc = ensure_utc(dt2)
        
        if dt1_utc is None or dt2_utc is None:
            logger.error("Failed to convert datetimes to UTC for comparison")
            return None
        
        return dt1_utc > dt2_utc
    except Exception as e:
        logger.error(f"Error comparing datetimes: {dt1} vs {dt2}, error: {e}")
        return None


def get_current_utc() -> datetime:
    """
    Get current UTC datetime.
    
    Returns:
        datetime: Current UTC datetime
    """
    return datetime.now(dt_timezone.utc)


def get_current_django_utc() -> datetime:
    """
    Get current UTC datetime using Django's timezone utilities.
    
    Returns:
        datetime: Current UTC datetime (Django timezone-aware)
    """
    return timezone.now()


def format_datetime_for_api(dt: Optional[datetime]) -> Optional[str]:
    """
    Format datetime for API responses with consistent UTC format.
    
    Args:
        dt: Datetime object to format
        
    Returns:
        Optional[str]: ISO format datetime string or None if input was None
    """
    if dt is None:
        return None
    
    try:
        utc_dt = ensure_utc(dt)
        if utc_dt is None:
            return None
        
        # Return ISO format with Z suffix for UTC
        return utc_dt.strftime("%Y-%m-%dT%H:%M:%S.%fZ")
    except Exception as e:
        logger.error(f"Error formatting datetime for API: {dt}, error: {e}")
        return None


def parse_datetime_from_api(dt_str: Optional[str]) -> Optional[datetime]:
    """
    Parse datetime string from API with proper timezone handling.
    
    Args:
        dt_str: ISO format datetime string
        
    Returns:
        Optional[datetime]: Parsed UTC datetime or None if parsing fails
    """
    if not dt_str:
        return None
    
    try:
        # Handle various ISO format variations
        if dt_str.endswith('Z'):
            # UTC timezone indicator
            dt_str = dt_str[:-1] + '+00:00'
        elif '+' not in dt_str and 'T' in dt_str:
            # Assume UTC if no timezone info
            dt_str += '+00:00'
        
        # Parse and convert to UTC
        parsed_dt = datetime.fromisoformat(dt_str)
        return ensure_utc(parsed_dt)
    except Exception as e:
        logger.error(f"Error parsing datetime from API: {dt_str}, error: {e}")
        return None


def calculate_time_difference(dt1: Optional[datetime], dt2: Optional[datetime]) -> Optional[float]:
    """
    Calculate time difference in seconds between two datetimes.
    
    Args:
        dt1: First datetime (typically later time)
        dt2: Second datetime (typically earlier time)
        
    Returns:
        Optional[float]: Time difference in seconds (positive if dt1 > dt2) or None if calculation fails
    """
    if dt1 is None or dt2 is None:
        return None
    
    try:
        # Ensure both datetimes are in UTC
        dt1_utc = ensure_utc(dt1)
        dt2_utc = ensure_utc(dt2)
        
        if dt1_utc is None or dt2_utc is None:
            return None
        
        # Calculate difference in seconds
        diff = (dt1_utc - dt2_utc).total_seconds()
        return diff
    except Exception as e:
        logger.error(f"Error calculating time difference: {dt1} - {dt2}, error: {e}")
        return None


def is_recent(dt: Optional[datetime], days: int = 30) -> bool:
    """
    Check if a datetime is recent (within specified days).
    
    Args:
        dt: Datetime to check
        days: Number of days to consider as "recent"
        
    Returns:
        bool: True if datetime is recent, False otherwise
    """
    if dt is None:
        return False
    
    try:
        dt_utc = ensure_utc(dt)
        current_utc = get_current_utc()
        
        if dt_utc is None:
            return False
        
        diff_seconds = calculate_time_difference(current_utc, dt_utc)
        if diff_seconds is None:
            return False
        
        # Convert days to seconds and compare
        days_in_seconds = days * 24 * 60 * 60
        return diff_seconds <= days_in_seconds
    except Exception as e:
        logger.error(f"Error checking if datetime is recent: {dt}, error: {e}")
        return False
