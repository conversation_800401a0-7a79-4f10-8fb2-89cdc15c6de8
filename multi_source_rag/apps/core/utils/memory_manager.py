"""
Memory Management and Cache Optimization

This module provides comprehensive memory management for the RAG system,
fixing memory leaks and implementing proper cache management with TTL,
size limits, and automatic cleanup.

Key Features:
- TTL-based cache expiration
- Memory usage monitoring
- Automatic cache cleanup
- Thread-safe operations
- Configurable cache policies
- Memory leak prevention
"""

import gc
import logging
import threading
import time
import weakref
from collections import OrderedDict
from dataclasses import dataclass
from functools import wraps
from typing import Any, Callable, Dict, Optional, Set, TypeVar, Union
import os

# Optional psutil for memory monitoring
try:
    import psutil
    HAS_PSUTIL = True
except ImportError:
    HAS_PSUTIL = False
    psutil = None

logger = logging.getLogger(__name__)

F = TypeVar('F', bound=Callable[..., Any])


@dataclass
class CacheConfig:
    """Configuration for cache behavior."""
    max_size: int = 1000
    ttl_seconds: int = 3600  # 1 hour
    cleanup_interval: int = 300  # 5 minutes
    memory_threshold_mb: int = 1024  # 1GB
    enable_monitoring: bool = True


class TTLCache:
    """
    Thread-safe cache with TTL (Time To Live) support.

    Features:
    - Automatic expiration based on TTL
    - LRU eviction when size limit is reached
    - Memory usage tracking
    - Thread-safe operations
    """

    def __init__(self, config: CacheConfig):
        self.config = config
        self._cache: OrderedDict = OrderedDict()
        self._timestamps: Dict[str, float] = {}
        self._lock = threading.RLock()
        self._last_cleanup = time.time()

        # Statistics
        self._hits = 0
        self._misses = 0
        self._evictions = 0

    def get(self, key: str) -> Optional[Any]:
        """Get value from cache if not expired."""
        with self._lock:
            self._cleanup_if_needed()

            if key not in self._cache:
                self._misses += 1
                return None

            # Check TTL
            if self._is_expired(key):
                self._remove_key(key)
                self._misses += 1
                return None

            # Move to end (LRU)
            value = self._cache[key]
            self._cache.move_to_end(key)
            self._hits += 1
            return value

    def set(self, key: str, value: Any) -> None:
        """Set value in cache with current timestamp."""
        with self._lock:
            self._cleanup_if_needed()

            # Remove if already exists
            if key in self._cache:
                self._remove_key(key)

            # Check size limit
            while len(self._cache) >= self.config.max_size:
                self._evict_oldest()

            # Add new entry
            self._cache[key] = value
            self._timestamps[key] = time.time()

    def delete(self, key: str) -> bool:
        """Delete key from cache."""
        with self._lock:
            if key in self._cache:
                self._remove_key(key)
                return True
            return False

    def clear(self) -> None:
        """Clear all cache entries."""
        with self._lock:
            self._cache.clear()
            self._timestamps.clear()
            gc.collect()  # Force garbage collection

    def _is_expired(self, key: str) -> bool:
        """Check if key has expired based on TTL."""
        if key not in self._timestamps:
            return True

        age = time.time() - self._timestamps[key]
        return age > self.config.ttl_seconds

    def _remove_key(self, key: str) -> None:
        """Remove key from both cache and timestamps."""
        self._cache.pop(key, None)
        self._timestamps.pop(key, None)

    def _evict_oldest(self) -> None:
        """Evict the oldest entry (LRU)."""
        if self._cache:
            oldest_key = next(iter(self._cache))
            self._remove_key(oldest_key)
            self._evictions += 1

    def _cleanup_if_needed(self) -> None:
        """Cleanup expired entries if enough time has passed."""
        current_time = time.time()
        if current_time - self._last_cleanup > self.config.cleanup_interval:
            self._cleanup_expired()
            self._last_cleanup = current_time

    def _cleanup_expired(self) -> None:
        """Remove all expired entries."""
        current_time = time.time()
        expired_keys = [
            key for key, timestamp in self._timestamps.items()
            if current_time - timestamp > self.config.ttl_seconds
        ]

        for key in expired_keys:
            self._remove_key(key)

        if expired_keys:
            logger.debug(f"Cleaned up {len(expired_keys)} expired cache entries")

    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        with self._lock:
            total_requests = self._hits + self._misses
            hit_rate = self._hits / total_requests if total_requests > 0 else 0

            return {
                'size': len(self._cache),
                'max_size': self.config.max_size,
                'hits': self._hits,
                'misses': self._misses,
                'evictions': self._evictions,
                'hit_rate': hit_rate,
                'ttl_seconds': self.config.ttl_seconds
            }


class MemoryManager:
    """
    Global memory manager for the RAG system.

    Features:
    - Memory usage monitoring
    - Automatic cache cleanup when memory is low
    - Weak reference tracking for large objects
    - Memory leak detection
    """

    def __init__(self):
        self._caches: Dict[str, TTLCache] = {}
        self._weak_refs: Set[weakref.ref] = set()
        self._lock = threading.RLock()
        self._monitoring_enabled = True

    def create_cache(self, name: str, config: Optional[CacheConfig] = None) -> TTLCache:
        """Create a new managed cache."""
        with self._lock:
            if config is None:
                config = CacheConfig()

            cache = TTLCache(config)
            self._caches[name] = cache
            logger.info(f"Created cache '{name}' with max_size={config.max_size}, ttl={config.ttl_seconds}s")
            return cache

    def get_cache(self, name: str) -> Optional[TTLCache]:
        """Get existing cache by name."""
        return self._caches.get(name)

    def clear_all_caches(self) -> None:
        """Clear all managed caches."""
        with self._lock:
            for cache in self._caches.values():
                cache.clear()
            logger.info("Cleared all managed caches")

    def get_memory_usage(self) -> Dict[str, Any]:
        """Get current memory usage statistics."""
        if HAS_PSUTIL:
            try:
                process = psutil.Process(os.getpid())
                memory_info = process.memory_info()

                return {
                    'rss_mb': memory_info.rss / 1024 / 1024,  # Resident Set Size
                    'vms_mb': memory_info.vms / 1024 / 1024,  # Virtual Memory Size
                    'percent': process.memory_percent(),
                    'available_mb': psutil.virtual_memory().available / 1024 / 1024,
                    'cache_count': len(self._caches),
                    'total_cache_entries': sum(len(cache._cache) for cache in self._caches.values())
                }
            except Exception as e:
                logger.warning(f"Error getting memory usage with psutil: {e}")

        # Fallback when psutil is not available
        return {
            'rss_mb': 0,  # Not available
            'vms_mb': 0,  # Not available
            'percent': 0,  # Not available
            'available_mb': 0,  # Not available
            'cache_count': len(self._caches),
            'total_cache_entries': sum(len(cache._cache) for cache in self._caches.values()),
            'psutil_available': HAS_PSUTIL
        }

    def check_memory_pressure(self) -> bool:
        """Check if system is under memory pressure."""
        if not HAS_PSUTIL:
            # Without psutil, use cache size as a proxy
            total_entries = sum(len(cache._cache) for cache in self._caches.values())
            return total_entries > 10000  # Arbitrary threshold

        try:
            memory_info = self.get_memory_usage()
            return memory_info['rss_mb'] > 1024 or memory_info['percent'] > 80
        except Exception as e:
            logger.warning(f"Error checking memory pressure: {e}")
            return False

    def cleanup_if_needed(self) -> None:
        """Cleanup caches if memory pressure is detected."""
        if self.check_memory_pressure():
            logger.warning("Memory pressure detected, cleaning up caches")
            self._emergency_cleanup()

    def _emergency_cleanup(self) -> None:
        """Emergency cleanup when memory is low."""
        with self._lock:
            # Clear expired entries from all caches
            for cache in self._caches.values():
                cache._cleanup_expired()

            # Force garbage collection
            gc.collect()

            # If still under pressure, clear least recently used caches
            if self.check_memory_pressure():
                logger.warning("Performing aggressive cache cleanup")
                for cache in self._caches.values():
                    # Reduce cache size by 50%
                    target_size = cache.config.max_size // 2
                    while len(cache._cache) > target_size:
                        cache._evict_oldest()

    def register_weak_ref(self, obj: Any) -> None:
        """Register weak reference for memory tracking."""
        def cleanup_callback(ref):
            self._weak_refs.discard(ref)

        weak_ref = weakref.ref(obj, cleanup_callback)
        self._weak_refs.add(weak_ref)

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get statistics for all managed caches."""
        stats = {}
        for name, cache in self._caches.items():
            stats[name] = cache.get_stats()
        return stats


# Global memory manager instance
memory_manager = MemoryManager()


def ttl_cache(maxsize: int = 128, ttl: int = 3600, cache_name: Optional[str] = None):
    """
    Decorator for TTL-based caching with memory management.

    Args:
        maxsize: Maximum number of cached entries
        ttl: Time to live in seconds
        cache_name: Optional name for the cache (for monitoring)
    """
    def decorator(func: F) -> F:
        # Create cache config
        config = CacheConfig(max_size=maxsize, ttl_seconds=ttl)

        # Use function name as cache name if not provided
        name = cache_name or f"{func.__module__}.{func.__qualname__}"

        # Create managed cache
        cache = memory_manager.create_cache(name, config)

        @wraps(func)
        def wrapper(*args, **kwargs):
            # Create cache key from arguments
            key = _create_cache_key(func.__name__, args, kwargs)

            # Try to get from cache
            result = cache.get(key)
            if result is not None:
                return result

            # Call function and cache result
            result = func(*args, **kwargs)
            cache.set(key, result)

            # Check memory pressure
            memory_manager.cleanup_if_needed()

            return result

        # Add cache management methods
        wrapper.cache_clear = cache.clear
        wrapper.cache_info = cache.get_stats
        wrapper.cache = cache

        return wrapper

    return decorator


def _create_cache_key(func_name: str, args: tuple, kwargs: dict) -> str:
    """Create a cache key from function arguments."""
    import hashlib
    import pickle

    try:
        # Create a hashable representation of arguments
        key_data = (func_name, args, tuple(sorted(kwargs.items())))
        key_bytes = pickle.dumps(key_data)
        return hashlib.md5(key_bytes).hexdigest()
    except Exception:
        # Fallback to string representation
        return f"{func_name}_{hash((args, tuple(sorted(kwargs.items()))))}"


def clear_all_caches():
    """Clear all managed caches and force garbage collection."""
    memory_manager.clear_all_caches()
    gc.collect()


def get_memory_stats() -> Dict[str, Any]:
    """Get comprehensive memory and cache statistics."""
    return {
        'memory_usage': memory_manager.get_memory_usage(),
        'cache_stats': memory_manager.get_cache_stats(),
        'memory_pressure': memory_manager.check_memory_pressure()
    }
