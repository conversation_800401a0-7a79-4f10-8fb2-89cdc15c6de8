"""
Production Resource Manager

This module provides comprehensive resource management for the RAG system.
It ensures proper cleanup of LlamaIndex components, vector store connections,
and other resources to prevent memory leaks.

Key Features:
- Automatic resource cleanup on service destruction
- Thread-safe resource tracking
- Graceful shutdown handling
- Memory leak prevention
- Connection pool management
"""

import logging
import gc
import threading
import weakref
from typing import Any, Dict, List, Optional, Set
from contextlib import contextmanager

logger = logging.getLogger(__name__)

# Global resource tracking
_RESOURCE_REGISTRY: Dict[str, Set[Any]] = {}
_RESOURCE_LOCK = threading.Lock()
_CLEANUP_HANDLERS: Dict[str, callable] = {}


class ResourceManager:
    """
    Production-grade resource manager for RAG system components.

    Tracks and manages lifecycle of expensive resources like:
    - LlamaIndex embedding models
    - Vector store connections
    - LLM instances
    - Cache objects
    """

    def __init__(self, component_name: str):
        """
        Initialize resource manager for a component.

        Args:
            component_name: Name of the component being managed
        """
        self.component_name = component_name
        self.resources: List[Any] = []  # Changed from set to list to handle unhashable objects
        self._cleanup_callbacks: List[callable] = []

        # Register this manager globally
        with _RESOURCE_LOCK:
            if component_name not in _RESOURCE_REGISTRY:
                _RESOURCE_REGISTRY[component_name] = set()
            _RESOURCE_REGISTRY[component_name].add(self)

    def register_resource(self, resource: Any, cleanup_callback: Optional[callable] = None) -> None:
        """
        Register a resource for tracking and cleanup.

        Args:
            resource: Resource to track
            cleanup_callback: Optional cleanup function for the resource
        """
        # Avoid duplicates by checking if resource is already in list
        if resource not in self.resources:
            self.resources.append(resource)

        if cleanup_callback:
            self._cleanup_callbacks.append(cleanup_callback)

        logger.debug(f"Registered resource for {self.component_name}: {type(resource).__name__}")

    def unregister_resource(self, resource: Any) -> None:
        """
        Unregister a resource from tracking.

        Args:
            resource: Resource to unregister
        """
        try:
            self.resources.remove(resource)
        except ValueError:
            pass  # Resource not in list, ignore
        logger.debug(f"Unregistered resource for {self.component_name}: {type(resource).__name__}")

    def cleanup_resources(self) -> None:
        """Clean up all registered resources."""
        logger.info(f"Cleaning up resources for {self.component_name}")

        # Execute cleanup callbacks
        for callback in self._cleanup_callbacks:
            try:
                callback()
            except Exception as e:
                logger.error(f"Error in cleanup callback for {self.component_name}: {e}")

        # Clear resource references
        self.resources.clear()
        self._cleanup_callbacks.clear()

        # Force garbage collection
        gc.collect()

        logger.info(f"✅ Resource cleanup completed for {self.component_name}")

    def __del__(self):
        """Automatic cleanup when manager is destroyed."""
        try:
            self.cleanup_resources()
        except Exception as e:
            logger.error(f"Error in automatic cleanup for {self.component_name}: {e}")


class EmbeddingResourceManager(ResourceManager):
    """Specialized resource manager for embedding models."""

    def __init__(self):
        super().__init__("embedding_models")
        self.model_cache: Dict[str, Any] = {}

    def register_embedding_model(self, model_name: str, model: Any) -> None:
        """
        Register an embedding model for resource management.

        Args:
            model_name: Name of the embedding model
            model: Embedding model instance
        """
        self.model_cache[model_name] = model
        self.register_resource(model, lambda: self._cleanup_embedding_model(model_name))
        logger.info(f"Registered embedding model: {model_name}")

    def _cleanup_embedding_model(self, model_name: str) -> None:
        """Clean up a specific embedding model."""
        if model_name in self.model_cache:
            model = self.model_cache.pop(model_name)

            # Try to cleanup model-specific resources
            if hasattr(model, 'cleanup'):
                model.cleanup()
            elif hasattr(model, 'close'):
                model.close()

            logger.info(f"Cleaned up embedding model: {model_name}")


class VectorStoreResourceManager(ResourceManager):
    """Specialized resource manager for vector store connections."""

    def __init__(self):
        super().__init__("vector_stores")
        self.connections: Dict[str, Any] = {}

    def register_vector_store(self, collection_name: str, vector_store: Any) -> None:
        """
        Register a vector store connection for resource management.

        Args:
            collection_name: Name of the vector collection
            vector_store: Vector store instance
        """
        self.connections[collection_name] = vector_store
        self.register_resource(vector_store, lambda: self._cleanup_vector_store(collection_name))
        logger.info(f"Registered vector store: {collection_name}")

    def _cleanup_vector_store(self, collection_name: str) -> None:
        """Clean up a specific vector store connection."""
        if collection_name in self.connections:
            vector_store = self.connections.pop(collection_name)

            # Try to cleanup vector store resources
            if hasattr(vector_store, 'close'):
                vector_store.close()
            elif hasattr(vector_store, 'disconnect'):
                vector_store.disconnect()

            logger.info(f"Cleaned up vector store: {collection_name}")


# Global resource managers
_EMBEDDING_RESOURCE_MANAGER = EmbeddingResourceManager()
_VECTOR_STORE_RESOURCE_MANAGER = VectorStoreResourceManager()


def register_embedding_model(model_name: str, model: Any) -> None:
    """Register an embedding model globally."""
    _EMBEDDING_RESOURCE_MANAGER.register_embedding_model(model_name, model)


def register_vector_store(collection_name: str, vector_store: Any) -> None:
    """Register a vector store globally."""
    _VECTOR_STORE_RESOURCE_MANAGER.register_vector_store(collection_name, vector_store)


def cleanup_all_resources() -> None:
    """Clean up all registered resources across all components."""
    logger.info("Starting global resource cleanup")

    with _RESOURCE_LOCK:
        for component_name, managers in _RESOURCE_REGISTRY.items():
            for manager in managers:
                try:
                    manager.cleanup_resources()
                except Exception as e:
                    logger.error(f"Error cleaning up {component_name}: {e}")

    # Clear global registry
    _RESOURCE_REGISTRY.clear()

    # Force garbage collection
    gc.collect()

    logger.info("✅ Global resource cleanup completed")


@contextmanager
def managed_resource(resource: Any, cleanup_callback: Optional[callable] = None):
    """
    Context manager for automatic resource cleanup.

    Args:
        resource: Resource to manage
        cleanup_callback: Optional cleanup function

    Yields:
        The managed resource
    """
    manager = ResourceManager("context_managed")
    manager.register_resource(resource, cleanup_callback)

    try:
        yield resource
    finally:
        manager.cleanup_resources()


def get_resource_stats() -> Dict[str, int]:
    """
    Get statistics about currently tracked resources.

    Returns:
        Dict with resource counts by component
    """
    stats = {}

    with _RESOURCE_LOCK:
        for component_name, managers in _RESOURCE_REGISTRY.items():
            total_resources = sum(len(manager.resources) for manager in managers)
            stats[component_name] = total_resources

    return stats
