"""
Centralized Token Estimation Utility

This module provides a standardized token estimation system to replace
the 4 different token estimation algorithms found across the codebase.

Implements Phase 3 recommendation: "Standardize Token Estimation"
"""

import logging
import re
from typing import Optional, Dict, Any
from functools import lru_cache

logger = logging.getLogger(__name__)

# Global token estimation configuration
TOKEN_ESTIMATION_CONFIG = {
    'method': 'tiktoken',  # 'tiktoken', 'approximation', 'word_based'
    'model': 'cl100k_base',  # tiktoken encoding model
    'fallback_ratio': 4.0,  # characters per token fallback ratio
    'cache_size': 1000,  # LRU cache size for token counts
}


class StandardTokenEstimator:
    """
    Centralized token estimation utility using tiktoken for accuracy.
    
    This replaces all custom token estimation algorithms with a single,
    consistent implementation that can be configured globally.
    """
    
    def __init__(self, method: str = 'tiktoken', model: str = 'cl100k_base'):
        """
        Initialize the token estimator.
        
        Args:
            method: Estimation method ('tiktoken', 'approximation', 'word_based')
            model: tiktoken model name for accurate estimation
        """
        self.method = method
        self.model = model
        self._encoding = None
        
        # Initialize tiktoken encoding if available
        if method == 'tiktoken':
            try:
                import tiktoken
                self._encoding = tiktoken.get_encoding(model)
                logger.info(f"Initialized tiktoken with model: {model}")
            except ImportError:
                logger.warning("tiktoken not available, falling back to approximation")
                self.method = 'approximation'
            except Exception as e:
                logger.warning(f"Failed to initialize tiktoken: {e}, falling back to approximation")
                self.method = 'approximation'
    
    @lru_cache(maxsize=1000)
    def estimate_tokens(self, text: str) -> int:
        """
        Estimate the number of tokens in the given text.
        
        Args:
            text: Text to estimate tokens for
            
        Returns:
            int: Estimated number of tokens
        """
        if not text:
            return 0
            
        if self.method == 'tiktoken' and self._encoding:
            return len(self._encoding.encode(text))
        elif self.method == 'word_based':
            return self._word_based_estimation(text)
        else:
            return self._approximation_estimation(text)
    
    def _approximation_estimation(self, text: str) -> int:
        """
        Approximation-based token estimation using character count.
        
        This is the fallback method when tiktoken is not available.
        Uses a ratio of ~4 characters per token for English text.
        """
        # Clean the text and count characters
        cleaned_text = re.sub(r'\s+', ' ', text.strip())
        char_count = len(cleaned_text)
        
        # Apply character-to-token ratio
        token_count = max(1, int(char_count / TOKEN_ESTIMATION_CONFIG['fallback_ratio']))
        
        return token_count
    
    def _word_based_estimation(self, text: str) -> int:
        """
        Word-based token estimation.
        
        This method counts words and applies a word-to-token ratio.
        Generally more accurate than character-based for English text.
        """
        # Split by whitespace and count words
        words = text.split()
        word_count = len(words)
        
        # Apply word-to-token ratio (approximately 1.3 tokens per word for English)
        token_count = max(1, int(word_count * 1.3))
        
        return token_count
    
    def estimate_tokens_for_chunks(self, chunks: list) -> Dict[int, int]:
        """
        Estimate tokens for multiple chunks efficiently.
        
        Args:
            chunks: List of text chunks
            
        Returns:
            Dict[int, int]: Mapping of chunk index to token count
        """
        return {i: self.estimate_tokens(chunk) for i, chunk in enumerate(chunks)}
    
    def validate_chunk_size(self, text: str, max_tokens: int) -> bool:
        """
        Validate that text doesn't exceed maximum token limit.
        
        Args:
            text: Text to validate
            max_tokens: Maximum allowed tokens
            
        Returns:
            bool: True if text is within limit
        """
        return self.estimate_tokens(text) <= max_tokens
    
    def split_by_token_limit(self, text: str, max_tokens: int, overlap_tokens: int = 0) -> list:
        """
        Split text into chunks based on token limits.
        
        Args:
            text: Text to split
            max_tokens: Maximum tokens per chunk
            overlap_tokens: Number of tokens to overlap between chunks
            
        Returns:
            list: List of text chunks within token limits
        """
        if self.estimate_tokens(text) <= max_tokens:
            return [text]
        
        # Split by sentences first
        sentences = re.split(r'[.!?]+', text)
        chunks = []
        current_chunk = ""
        
        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue
                
            # Check if adding this sentence would exceed the limit
            test_chunk = current_chunk + " " + sentence if current_chunk else sentence
            
            if self.estimate_tokens(test_chunk) <= max_tokens:
                current_chunk = test_chunk
            else:
                # Save current chunk and start new one
                if current_chunk:
                    chunks.append(current_chunk)
                current_chunk = sentence
        
        # Add the last chunk
        if current_chunk:
            chunks.append(current_chunk)
        
        return chunks
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get token estimator statistics and configuration.
        
        Returns:
            Dict[str, Any]: Statistics and configuration info
        """
        return {
            'method': self.method,
            'model': self.model,
            'encoding_available': self._encoding is not None,
            'cache_info': self.estimate_tokens.cache_info()._asdict(),
            'config': TOKEN_ESTIMATION_CONFIG
        }


# Global instance for consistent usage across the application
_global_estimator = None


def get_token_estimator() -> StandardTokenEstimator:
    """
    Get the global token estimator instance.
    
    Returns:
        StandardTokenEstimator: Global token estimator
    """
    global _global_estimator
    if _global_estimator is None:
        _global_estimator = StandardTokenEstimator(
            method=TOKEN_ESTIMATION_CONFIG['method'],
            model=TOKEN_ESTIMATION_CONFIG['model']
        )
    return _global_estimator


def estimate_tokens(text: str) -> int:
    """
    Convenience function for token estimation.
    
    Args:
        text: Text to estimate tokens for
        
    Returns:
        int: Estimated number of tokens
    """
    return get_token_estimator().estimate_tokens(text)


def configure_token_estimation(method: str = 'tiktoken', model: str = 'cl100k_base') -> None:
    """
    Configure global token estimation settings.
    
    Args:
        method: Estimation method ('tiktoken', 'approximation', 'word_based')
        model: tiktoken model name
    """
    global _global_estimator, TOKEN_ESTIMATION_CONFIG
    
    TOKEN_ESTIMATION_CONFIG['method'] = method
    TOKEN_ESTIMATION_CONFIG['model'] = model
    
    # Reset global estimator to pick up new configuration
    _global_estimator = None
    
    logger.info(f"Token estimation configured: method={method}, model={model}")


def validate_chunk_sizes(chunks: list, max_tokens: int = 500) -> Dict[str, Any]:
    """
    Validate that all chunks are within token limits.
    
    Args:
        chunks: List of text chunks to validate
        max_tokens: Maximum allowed tokens per chunk
        
    Returns:
        Dict[str, Any]: Validation results
    """
    estimator = get_token_estimator()
    results = {
        'total_chunks': len(chunks),
        'valid_chunks': 0,
        'oversized_chunks': 0,
        'max_tokens_found': 0,
        'avg_tokens': 0,
        'oversized_indices': []
    }
    
    total_tokens = 0
    
    for i, chunk in enumerate(chunks):
        token_count = estimator.estimate_tokens(chunk)
        total_tokens += token_count
        
        if token_count <= max_tokens:
            results['valid_chunks'] += 1
        else:
            results['oversized_chunks'] += 1
            results['oversized_indices'].append(i)
        
        results['max_tokens_found'] = max(results['max_tokens_found'], token_count)
    
    results['avg_tokens'] = total_tokens / len(chunks) if chunks else 0
    
    return results
