"""
Centralized Embedding Configuration - Single Source of Truth

This module provides the ONLY embedding configuration for the entire RAG system.
All other embedding utilities MUST use this configuration to prevent dimension mismatches.

CRITICAL: This is the single source of truth for embedding models.
No other file should define embedding models or dimensions.

Key Features:
- Single embedding model configuration across ALL services
- Automatic dimension validation with strict enforcement
- Environment-based configuration with production defaults
- Comprehensive error handling with no fallbacks
- Production-ready caching and performance optimization
- Strict validation to prevent configuration drift
"""

import logging
from typing import Tuple, Any
from functools import lru_cache
from threading import Lock

logger = logging.getLogger(__name__)

# Global lock for thread-safe initialization
_EMBEDDING_LOCK = Lock()
_EMBEDDING_MODEL_CACHE = None
_EMBEDDING_CONFIG_VALIDATED = False
_MODEL_WARMUP_COMPLETED = False
_WARMUP_EMBEDDINGS_CACHE = {}  # Cache for warmup embeddings


class EmbeddingConfig:
    """
    AUTHORITATIVE embedding configuration class.

    This is the ONLY class that should define embedding model configuration.
    All other services MUST use this class to ensure consistency.
    """

    # Production-ready SINGLE model configuration
    # CRITICAL: Only one model is supported to prevent dimension mismatches
    PRODUCTION_MODEL_NAME = "BAAI/bge-base-en-v1.5"
    PRODUCTION_DIMENSIONS = 768
    PRODUCTION_BATCH_SIZE = 32
    PRODUCTION_CACHE_FOLDER = "./models/embeddings"

    # Supported models for validation only - PRODUCTION USES ONLY ONE
    _SUPPORTED_MODELS = {
        "BAAI/bge-base-en-v1.5": 768,      # PRODUCTION MODEL - ONLY ONE USED
        "BAAI/bge-small-en-v1.5": 384,     # For testing only
        "BAAI/bge-large-en-v1.5": 1024,    # For testing only
    }

    @classmethod
    def get_model_config(cls) -> Tuple[str, int]:
        """
        Get the PRODUCTION embedding model configuration.

        CRITICAL: Always returns the same production model to prevent dimension mismatches.
        Environment variables are IGNORED in production for consistency.

        Returns:
            Tuple[str, int]: (model_name, dimensions) - Always production values
        """
        global _EMBEDDING_CONFIG_VALIDATED

        # PRODUCTION: Always use the same model for consistency
        model_name = cls.PRODUCTION_MODEL_NAME
        dimensions = cls.PRODUCTION_DIMENSIONS

        # Validate configuration on first call
        if not _EMBEDDING_CONFIG_VALIDATED:
            cls._validate_production_config()
            _EMBEDDING_CONFIG_VALIDATED = True

        logger.info(f"Using PRODUCTION embedding model: {model_name} ({dimensions}d)")
        return model_name, dimensions

    @classmethod
    def _validate_production_config(cls) -> None:
        """Validate that production configuration is consistent."""
        if cls.PRODUCTION_MODEL_NAME not in cls._SUPPORTED_MODELS:
            raise ValueError(f"Production model {cls.PRODUCTION_MODEL_NAME} not in supported models")

        expected_dims = cls._SUPPORTED_MODELS[cls.PRODUCTION_MODEL_NAME]
        if cls.PRODUCTION_DIMENSIONS != expected_dims:
            raise ValueError(f"Production dimensions mismatch: {cls.PRODUCTION_DIMENSIONS} != {expected_dims}")

        logger.info("✅ Production embedding configuration validated")

    @classmethod
    def get_batch_size(cls) -> int:
        """Get the production batch size for embedding operations."""
        return cls.PRODUCTION_BATCH_SIZE

    @classmethod
    def get_cache_folder(cls) -> str:
        """Get the production cache folder for embedding models."""
        return cls.PRODUCTION_CACHE_FOLDER

    @classmethod
    def validate_dimensions(cls, expected_dimensions: int) -> bool:
        """
        Validate that dimensions match production configuration.

        Args:
            expected_dimensions: Expected number of dimensions

        Returns:
            bool: True if dimensions match production, False otherwise
        """
        _, actual_dimensions = cls.get_model_config()

        if actual_dimensions != expected_dimensions:
            logger.error(
                f"CRITICAL: Dimension mismatch! Expected {expected_dimensions}, "
                f"but production model has {actual_dimensions} dimensions. "
                f"This will break vector search!"
            )
            return False

        return True

    @classmethod
    @lru_cache(maxsize=1)
    def get_embedding_model(cls, warmup: bool = True) -> Any:
        """
        Get the PRODUCTION embedding model instance with optional warmup.

        This method is cached to ensure only ONE embedding model instance
        is created and reused across the ENTIRE system.

        Args:
            warmup: Whether to perform model warmup for better performance

        Returns:
            Embedding model instance (always the same production model)
        """
        global _EMBEDDING_MODEL_CACHE, _EMBEDDING_LOCK, _MODEL_WARMUP_COMPLETED

        # Thread-safe singleton pattern
        with _EMBEDDING_LOCK:
            if _EMBEDDING_MODEL_CACHE is not None:
                # Perform warmup if requested and not already done
                if warmup and not _MODEL_WARMUP_COMPLETED:
                    cls._warmup_model(_EMBEDDING_MODEL_CACHE)
                return _EMBEDDING_MODEL_CACHE

            model_name, dimensions = cls.get_model_config()
            batch_size = cls.get_batch_size()
            cache_folder = cls.get_cache_folder()

            try:
                # Import HuggingFace embedding
                from llama_index.embeddings.huggingface import HuggingFaceEmbedding

                logger.info(f"Initializing PRODUCTION embedding: {model_name}")

                embedding_model = HuggingFaceEmbedding(
                    model_name=model_name,
                    embed_batch_size=batch_size,
                    cache_folder=cache_folder,
                )

                # Cache the model globally
                _EMBEDDING_MODEL_CACHE = embedding_model

                logger.info(
                    f"✅ PRODUCTION embedding model initialized: {model_name} "
                    f"({dimensions}d, batch_size={batch_size})"
                )

                # Perform warmup if requested
                if warmup:
                    cls._warmup_model(embedding_model)

                return embedding_model

            except ImportError as e:
                logger.error(f"CRITICAL: Failed to import HuggingFace embedding: {e}")
                raise ImportError(
                    "HuggingFace embeddings not available. "
                    "Install with: pip install llama-index-embeddings-huggingface"
                )
            except Exception as e:
                logger.error(f"CRITICAL: Failed to initialize embedding model: {e}")
                raise RuntimeError(f"Could not initialize embedding model: {e}")

    @classmethod
    def _warmup_model(cls, embedding_model: Any) -> None:
        """
        Warm up the embedding model with common queries for better performance.

        Args:
            embedding_model: The embedding model to warm up
        """
        global _MODEL_WARMUP_COMPLETED, _WARMUP_EMBEDDINGS_CACHE

        if _MODEL_WARMUP_COMPLETED:
            return

        try:
            logger.info("🔥 Warming up embedding model for optimal performance...")

            # Common warmup queries to pre-load model components
            warmup_texts = [
                "authentication system",
                "API endpoint configuration",
                "user login process",
                "database connection",
                "error handling",
                "security implementation"
            ]

            # Generate embeddings for warmup texts
            for text in warmup_texts:
                try:
                    embedding = embedding_model.get_text_embedding(text)
                    _WARMUP_EMBEDDINGS_CACHE[text] = embedding
                except Exception as e:
                    logger.warning(f"Warmup failed for text '{text}': {e}")

            _MODEL_WARMUP_COMPLETED = True
            logger.info(f"✅ Embedding model warmup completed with {len(_WARMUP_EMBEDDINGS_CACHE)} cached embeddings")

        except Exception as e:
            logger.warning(f"Embedding model warmup failed: {e}")

    @classmethod
    def get_cached_embedding(cls, text: str) -> Any:
        """
        Get a cached embedding if available from warmup cache.

        Args:
            text: Text to get embedding for

        Returns:
            Cached embedding or None if not found
        """
        global _WARMUP_EMBEDDINGS_CACHE
        return _WARMUP_EMBEDDINGS_CACHE.get(text)


# Global functions - PRODUCTION ONLY
def get_embedding_model(warmup: bool = True) -> Any:
    """Get the PRODUCTION embedding model instance."""
    return EmbeddingConfig.get_embedding_model(warmup=warmup)


def get_model_config() -> Tuple[str, int]:
    """Get the PRODUCTION model name and dimensions."""
    return EmbeddingConfig.get_model_config()


def get_embedding_dimensions() -> int:
    """Get the PRODUCTION embedding dimensions."""
    _, dimensions = EmbeddingConfig.get_model_config()
    return dimensions


def validate_embedding_dimensions(expected_dimensions: int) -> bool:
    """Validate that dimensions match PRODUCTION configuration."""
    return EmbeddingConfig.validate_dimensions(expected_dimensions)


def get_embedding_info() -> dict:
    """
    Get PRODUCTION embedding configuration information.

    Returns:
        dict: Production configuration information
    """
    model_name, dimensions = EmbeddingConfig.get_model_config()

    return {
        "model_name": model_name,
        "dimensions": dimensions,
        "batch_size": EmbeddingConfig.get_batch_size(),
        "cache_folder": EmbeddingConfig.get_cache_folder(),
        "is_production": True,
        "config_source": "PRODUCTION_SINGLE_SOURCE_OF_TRUTH",
    }


# Validation function to ensure system consistency
def validate_system_embedding_consistency() -> bool:
    """
    Validate that the entire system uses consistent embedding configuration.

    Returns:
        bool: True if system is consistent, False otherwise
    """
    try:
        # Validate production configuration
        EmbeddingConfig._validate_production_config()

        # Ensure model can be initialized
        model = get_embedding_model()
        if model is None:
            logger.error("CRITICAL: Could not initialize production embedding model")
            return False

        logger.info("✅ System embedding consistency validated")
        return True

    except Exception as e:
        logger.error(f"CRITICAL: System embedding consistency validation failed: {e}")
        return False
