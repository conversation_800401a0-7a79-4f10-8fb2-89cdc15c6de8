"""
Embedding Consistency Management

This module ensures consistent embedding model usage across the entire RAG system.
It provides a single source of truth for embedding model configuration and metadata.
"""

import logging
from typing import Dict, Any, Optional
from django.conf import settings

logger = logging.getLogger(__name__)

# Global embedding model configuration
_GLOBAL_EMBEDDING_CONFIG = {
    "model_name": "BAAI/bge-base-en-v1.5",
    "dimensions": 768,
    "batch_size": 32,
    "max_length": 512,
    "normalize_embeddings": True,
    "device": "cpu"
}

def set_global_embedding_model(
    model_name: str = "BAAI/bge-base-en-v1.5",
    dimensions: int = 768,
    batch_size: int = 32,
    max_length: int = 512,
    normalize_embeddings: bool = True,
    device: str = "cpu"
) -> None:
    """
    Set the global embedding model configuration.

    Args:
        model_name: Name of the embedding model
        dimensions: Vector dimensions
        batch_size: Batch size for processing
        max_length: Maximum sequence length
        normalize_embeddings: Whether to normalize embeddings
        device: Device to run on (cpu/cuda)
    """
    global _GLOBAL_EMBEDDING_CONFIG

    _GLOBAL_EMBEDDING_CONFIG.update({
        "model_name": model_name,
        "dimensions": dimensions,
        "batch_size": batch_size,
        "max_length": max_length,
        "normalize_embeddings": normalize_embeddings,
        "device": device
    })

    logger.info(f"✅ Set global embedding model: {model_name} ({dimensions}d)")

def get_embedding_model_info() -> Dict[str, Any]:
    """
    Get the current global embedding model information.

    Returns:
        Dictionary containing embedding model metadata
    """
    return _GLOBAL_EMBEDDING_CONFIG.copy()

def get_embedding_model_name() -> str:
    """Get the current embedding model name."""
    return _GLOBAL_EMBEDDING_CONFIG["model_name"]

def get_embedding_dimensions() -> int:
    """Get the current embedding dimensions."""
    return _GLOBAL_EMBEDDING_CONFIG["dimensions"]

def get_embedding_batch_size() -> int:
    """Get the current embedding batch size."""
    return _GLOBAL_EMBEDDING_CONFIG["batch_size"]

def validate_embedding_consistency(
    model_name: str,
    dimensions: int
) -> bool:
    """
    Validate that the provided model configuration matches the global configuration.

    Args:
        model_name: Model name to validate
        dimensions: Dimensions to validate

    Returns:
        True if consistent, False otherwise
    """
    global_config = get_embedding_model_info()

    is_consistent = (
        model_name == global_config["model_name"] and
        dimensions == global_config["dimensions"]
    )

    if not is_consistent:
        logger.warning(
            f"Embedding inconsistency detected: "
            f"Expected {global_config['model_name']} ({global_config['dimensions']}d), "
            f"got {model_name} ({dimensions}d)"
        )

    return is_consistent

def ensure_embedding_consistency() -> None:
    """
    Ensure embedding consistency across the system.
    This should be called during system initialization.
    """
    try:
        # Validate against Django settings if available
        if hasattr(settings, 'EMBEDDING_MODEL_NAME'):
            expected_model = settings.EMBEDDING_MODEL_NAME
            if expected_model != _GLOBAL_EMBEDDING_CONFIG["model_name"]:
                logger.warning(
                    f"Embedding model mismatch with settings: "
                    f"Global: {_GLOBAL_EMBEDDING_CONFIG['model_name']}, "
                    f"Settings: {expected_model}"
                )

        # Validate against embedding dimensions in settings
        if hasattr(settings, 'EMBEDDING_DIMENSIONS'):
            expected_dims = settings.EMBEDDING_DIMENSIONS
            if expected_dims != _GLOBAL_EMBEDDING_CONFIG["dimensions"]:
                logger.warning(
                    f"Embedding dimensions mismatch with settings: "
                    f"Global: {_GLOBAL_EMBEDDING_CONFIG['dimensions']}, "
                    f"Settings: {expected_dims}"
                )

        logger.info("✅ Embedding consistency validated")

    except Exception as e:
        logger.error(f"Failed to validate embedding consistency: {e}")

def get_production_embedding_config() -> Dict[str, Any]:
    """
    Get the production embedding configuration.

    Returns:
        Production-ready embedding configuration
    """
    return {
        "model_name": "BAAI/bge-base-en-v1.5",
        "dimensions": 768,
        "batch_size": 32,
        "max_length": 512,
        "normalize_embeddings": True,
        "device": "cpu",
        "trust_remote_code": False,
        "cache_folder": None
    }

def initialize_production_embedding_consistency() -> None:
    """Initialize embedding consistency for production environment."""
    config = get_production_embedding_config()
    # Only pass parameters that set_global_embedding_model accepts
    set_global_embedding_model(
        model_name=config["model_name"],
        dimensions=config["dimensions"],
        batch_size=config["batch_size"],
        max_length=config["max_length"],
        normalize_embeddings=config["normalize_embeddings"],
        device=config["device"]
    )
    ensure_embedding_consistency()
    logger.info("✅ Production embedding consistency initialized")

# Initialize with production defaults
initialize_production_embedding_consistency()
