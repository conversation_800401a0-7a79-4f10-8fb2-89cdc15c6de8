"""
Performance Caching System for RAG

This module provides intelligent caching for embeddings, search results,
and LLM responses to dramatically improve performance.
"""

import hashlib
import json
import time
import logging
from typing import Dict, Any, Optional, List
from django.core.cache import cache
from django.conf import settings

logger = logging.getLogger(__name__)

class PerformanceCache:
    """High-performance caching system for RAG operations."""
    
    def __init__(self, tenant_slug: str):
        self.tenant_slug = tenant_slug
        self.cache_prefix = f"rag_perf_{tenant_slug}"
        
        # Cache TTL settings (in seconds)
        self.ttl_settings = {
            'search_results': 3600,      # 1 hour for search results
            'embeddings': 86400,         # 24 hours for embeddings
            'llm_responses': 1800,       # 30 minutes for LLM responses
            'query_analysis': 7200,      # 2 hours for query analysis
            'intent_classification': 3600, # 1 hour for intent classification
        }
    
    def _generate_cache_key(self, operation: str, data: Any) -> str:
        """Generate a consistent cache key for the given operation and data."""
        if isinstance(data, dict):
            # Sort dict for consistent hashing
            data_str = json.dumps(data, sort_keys=True)
        elif isinstance(data, (list, tuple)):
            data_str = json.dumps(sorted(data) if all(isinstance(x, str) for x in data) else list(data))
        else:
            data_str = str(data)
        
        # Create hash of the data
        data_hash = hashlib.md5(data_str.encode()).hexdigest()[:12]
        return f"{self.cache_prefix}_{operation}_{data_hash}"
    
    def get_search_results(self, query: str, intent: str, features: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Get cached search results."""
        cache_data = {
            'query': query.lower().strip(),
            'intent': intent,
            'features': features
        }
        cache_key = self._generate_cache_key('search', cache_data)
        
        try:
            cached_result = cache.get(cache_key)
            if cached_result:
                logger.info(f"Cache HIT for search: {query[:50]}...")
                # Add cache metadata
                cached_result['metadata']['cached'] = True
                cached_result['metadata']['cache_timestamp'] = time.time()
                return cached_result
        except Exception as e:
            logger.warning(f"Cache get error: {e}")
        
        return None
    
    def set_search_results(self, query: str, intent: str, features: Dict[str, Any], results: Dict[str, Any]) -> None:
        """Cache search results."""
        cache_data = {
            'query': query.lower().strip(),
            'intent': intent,
            'features': features
        }
        cache_key = self._generate_cache_key('search', cache_data)
        
        try:
            # Add cache metadata
            results_to_cache = results.copy()
            results_to_cache['metadata']['cache_stored'] = time.time()
            
            cache.set(cache_key, results_to_cache, self.ttl_settings['search_results'])
            logger.info(f"Cache SET for search: {query[:50]}...")
        except Exception as e:
            logger.warning(f"Cache set error: {e}")
    
    def get_embedding(self, text: str, model_name: str) -> Optional[List[float]]:
        """Get cached embedding."""
        cache_data = {
            'text': text.strip(),
            'model': model_name
        }
        cache_key = self._generate_cache_key('embedding', cache_data)
        
        try:
            cached_embedding = cache.get(cache_key)
            if cached_embedding:
                logger.debug(f"Cache HIT for embedding: {text[:30]}...")
                return cached_embedding
        except Exception as e:
            logger.warning(f"Embedding cache get error: {e}")
        
        return None
    
    def set_embedding(self, text: str, model_name: str, embedding: List[float]) -> None:
        """Cache embedding."""
        cache_data = {
            'text': text.strip(),
            'model': model_name
        }
        cache_key = self._generate_cache_key('embedding', cache_data)
        
        try:
            cache.set(cache_key, embedding, self.ttl_settings['embeddings'])
            logger.debug(f"Cache SET for embedding: {text[:30]}...")
        except Exception as e:
            logger.warning(f"Embedding cache set error: {e}")
    
    def get_llm_response(self, prompt: str, model_name: str, temperature: float = 0.1) -> Optional[str]:
        """Get cached LLM response."""
        cache_data = {
            'prompt': prompt.strip(),
            'model': model_name,
            'temperature': temperature
        }
        cache_key = self._generate_cache_key('llm', cache_data)
        
        try:
            cached_response = cache.get(cache_key)
            if cached_response:
                logger.info(f"Cache HIT for LLM: {prompt[:50]}...")
                return cached_response
        except Exception as e:
            logger.warning(f"LLM cache get error: {e}")
        
        return None
    
    def set_llm_response(self, prompt: str, model_name: str, response: str, temperature: float = 0.1) -> None:
        """Cache LLM response."""
        cache_data = {
            'prompt': prompt.strip(),
            'model': model_name,
            'temperature': temperature
        }
        cache_key = self._generate_cache_key('llm', cache_data)
        
        try:
            cache.set(cache_key, response, self.ttl_settings['llm_responses'])
            logger.info(f"Cache SET for LLM: {prompt[:50]}...")
        except Exception as e:
            logger.warning(f"LLM cache set error: {e}")
    
    def get_intent_classification(self, query: str) -> Optional[str]:
        """Get cached intent classification."""
        cache_key = self._generate_cache_key('intent', query.lower().strip())
        
        try:
            cached_intent = cache.get(cache_key)
            if cached_intent:
                logger.debug(f"Cache HIT for intent: {query[:50]}...")
                return cached_intent
        except Exception as e:
            logger.warning(f"Intent cache get error: {e}")
        
        return None
    
    def set_intent_classification(self, query: str, intent: str) -> None:
        """Cache intent classification."""
        cache_key = self._generate_cache_key('intent', query.lower().strip())
        
        try:
            cache.set(cache_key, intent, self.ttl_settings['intent_classification'])
            logger.debug(f"Cache SET for intent: {query[:50]}...")
        except Exception as e:
            logger.warning(f"Intent cache set error: {e}")
    
    def get_query_analysis(self, query: str) -> Optional[Dict[str, Any]]:
        """Get cached query analysis."""
        cache_key = self._generate_cache_key('analysis', query.lower().strip())
        
        try:
            cached_analysis = cache.get(cache_key)
            if cached_analysis:
                logger.debug(f"Cache HIT for analysis: {query[:50]}...")
                return cached_analysis
        except Exception as e:
            logger.warning(f"Analysis cache get error: {e}")
        
        return None
    
    def set_query_analysis(self, query: str, analysis: Dict[str, Any]) -> None:
        """Cache query analysis."""
        cache_key = self._generate_cache_key('analysis', query.lower().strip())
        
        try:
            cache.set(cache_key, analysis, self.ttl_settings['query_analysis'])
            logger.debug(f"Cache SET for analysis: {query[:50]}...")
        except Exception as e:
            logger.warning(f"Analysis cache set error: {e}")
    
    def clear_cache(self, operation: Optional[str] = None) -> None:
        """Clear cache for specific operation or all operations."""
        try:
            if operation:
                # Clear specific operation cache
                pattern = f"{self.cache_prefix}_{operation}_*"
                logger.info(f"Clearing cache for operation: {operation}")
            else:
                # Clear all cache for this tenant
                pattern = f"{self.cache_prefix}_*"
                logger.info(f"Clearing all cache for tenant: {self.tenant_slug}")
            
            # Note: Django's cache doesn't support pattern deletion by default
            # This is a simplified implementation
            logger.info(f"Cache clear requested for pattern: {pattern}")
            
        except Exception as e:
            logger.warning(f"Cache clear error: {e}")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        try:
            # This is a simplified implementation
            # In production, you might want to use Redis with detailed stats
            return {
                'tenant': self.tenant_slug,
                'cache_prefix': self.cache_prefix,
                'ttl_settings': self.ttl_settings,
                'timestamp': time.time()
            }
        except Exception as e:
            logger.warning(f"Cache stats error: {e}")
            return {}

# Global cache instances
_cache_instances = {}

def get_performance_cache(tenant_slug: str) -> PerformanceCache:
    """Get or create a performance cache instance for the tenant."""
    if tenant_slug not in _cache_instances:
        _cache_instances[tenant_slug] = PerformanceCache(tenant_slug)
    return _cache_instances[tenant_slug]
