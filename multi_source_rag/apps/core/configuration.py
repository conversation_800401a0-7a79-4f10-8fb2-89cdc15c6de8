"""
Production-grade centralized configuration management for RAG system.

Provides environment-specific configuration with validation, type safety,
and proper defaults for all RAG system components.
"""

import os
import logging
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path

try:
    from pydantic import BaseSettings, Field, validator
    PYDANTIC_AVAILABLE = True
except ImportError:
    PYDANTIC_AVAILABLE = False

logger = logging.getLogger(__name__)


class Environment(Enum):
    """Supported deployment environments."""
    DEVELOPMENT = "development"
    TESTING = "testing"
    STAGING = "staging"
    PRODUCTION = "production"


class SophisticationLevel(Enum):
    """RAG sophistication levels for different use cases."""
    BASIC = "basic"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"
    EXPERT = "expert"


@dataclass
class CacheConfiguration:
    """Configuration for caching systems."""
    enabled: bool = True
    ttl_seconds: int = 3600
    max_size: int = 1000
    redis_url: Optional[str] = None
    use_redis: bool = False


@dataclass
class SearchConfiguration:
    """Configuration for search operations."""
    similarity_top_k: int = 10
    max_query_length: int = 1000
    min_relevance_score: float = 0.4
    enable_hybrid_search: bool = True
    enable_query_expansion: bool = False
    enable_multi_step_reasoning: bool = False
    enable_cross_domain_search: bool = True


@dataclass
class LLMConfiguration:
    """Configuration for LLM providers."""
    provider: str = "ollama"
    model_name: str = "llama3.1:8b"
    temperature: float = 0.1
    max_tokens: int = 2048
    timeout_seconds: int = 30
    api_key: Optional[str] = None
    base_url: Optional[str] = None


@dataclass
class EmbeddingConfiguration:
    """Configuration for embedding models."""
    provider: str = "ollama"
    model_name: str = "bge-base-en-v1.5"
    dimension: int = 768
    batch_size: int = 32
    timeout_seconds: int = 30


@dataclass
class VectorStoreConfiguration:
    """Configuration for vector stores."""
    provider: str = "qdrant"
    host: str = "localhost"
    port: int = 6333
    collection_prefix: str = "rag"
    distance_metric: str = "cosine"
    timeout_seconds: int = 30


@dataclass
class SecurityConfiguration:
    """Configuration for security settings."""
    enable_rate_limiting: bool = True
    rate_limit_per_hour: int = 100
    enable_input_validation: bool = True
    max_request_size_mb: int = 10
    allowed_file_types: List[str] = field(default_factory=lambda: ['.txt', '.pdf', '.md', '.docx'])
    enable_api_key_auth: bool = False


@dataclass
class MonitoringConfiguration:
    """Configuration for monitoring and observability."""
    enabled: bool = True
    enable_prometheus: bool = True
    enable_health_checks: bool = True
    log_level: str = "INFO"
    metrics_port: int = 8000
    health_check_interval: int = 60


if PYDANTIC_AVAILABLE:
    class RAGSettings(BaseSettings):
        """
        Production-grade RAG system configuration using Pydantic.
        
        Automatically loads from environment variables with RAG_ prefix
        and validates all configuration values.
        """
        
        # Environment
        environment: Environment = Field(default=Environment.DEVELOPMENT, env="RAG_ENVIRONMENT")
        debug: bool = Field(default=False, env="RAG_DEBUG")
        
        # Search Configuration
        similarity_top_k: int = Field(default=10, ge=1, le=100, env="RAG_SIMILARITY_TOP_K")
        max_query_length: int = Field(default=1000, ge=1, le=5000, env="RAG_MAX_QUERY_LENGTH")
        min_relevance_score: float = Field(default=0.4, ge=0.0, le=1.0, env="RAG_MIN_RELEVANCE_SCORE")
        enable_hybrid_search: bool = Field(default=True, env="RAG_ENABLE_HYBRID_SEARCH")
        enable_query_expansion: bool = Field(default=False, env="RAG_ENABLE_QUERY_EXPANSION")
        enable_multi_step_reasoning: bool = Field(default=False, env="RAG_ENABLE_MULTI_STEP_REASONING")
        enable_cross_domain_search: bool = Field(default=True, env="RAG_ENABLE_CROSS_DOMAIN_SEARCH")
        
        # LLM Configuration
        llm_provider: str = Field(default="ollama", env="RAG_LLM_PROVIDER")
        llm_model_name: str = Field(default="llama3.1:8b", env="RAG_LLM_MODEL_NAME")
        llm_temperature: float = Field(default=0.1, ge=0.0, le=2.0, env="RAG_LLM_TEMPERATURE")
        llm_max_tokens: int = Field(default=2048, ge=1, le=8192, env="RAG_LLM_MAX_TOKENS")
        llm_timeout_seconds: int = Field(default=30, ge=1, le=300, env="RAG_LLM_TIMEOUT_SECONDS")
        llm_api_key: Optional[str] = Field(default=None, env="RAG_LLM_API_KEY")
        llm_base_url: Optional[str] = Field(default=None, env="RAG_LLM_BASE_URL")
        
        # Embedding Configuration
        embedding_provider: str = Field(default="ollama", env="RAG_EMBEDDING_PROVIDER")
        embedding_model_name: str = Field(default="bge-base-en-v1.5", env="RAG_EMBEDDING_MODEL_NAME")
        embedding_dimension: int = Field(default=768, ge=128, le=4096, env="RAG_EMBEDDING_DIMENSION")
        embedding_batch_size: int = Field(default=32, ge=1, le=128, env="RAG_EMBEDDING_BATCH_SIZE")
        embedding_timeout_seconds: int = Field(default=30, ge=1, le=300, env="RAG_EMBEDDING_TIMEOUT_SECONDS")
        
        # Vector Store Configuration
        vector_store_provider: str = Field(default="qdrant", env="RAG_VECTOR_STORE_PROVIDER")
        vector_store_host: str = Field(default="localhost", env="RAG_VECTOR_STORE_HOST")
        vector_store_port: int = Field(default=6333, ge=1, le=65535, env="RAG_VECTOR_STORE_PORT")
        vector_store_collection_prefix: str = Field(default="rag", env="RAG_VECTOR_STORE_COLLECTION_PREFIX")
        vector_store_distance_metric: str = Field(default="cosine", env="RAG_VECTOR_STORE_DISTANCE_METRIC")
        vector_store_timeout_seconds: int = Field(default=30, ge=1, le=300, env="RAG_VECTOR_STORE_TIMEOUT_SECONDS")
        
        # Cache Configuration
        cache_enabled: bool = Field(default=True, env="RAG_CACHE_ENABLED")
        cache_ttl_seconds: int = Field(default=3600, ge=60, le=86400, env="RAG_CACHE_TTL_SECONDS")
        cache_max_size: int = Field(default=1000, ge=10, le=10000, env="RAG_CACHE_MAX_SIZE")
        cache_redis_url: Optional[str] = Field(default=None, env="RAG_CACHE_REDIS_URL")
        cache_use_redis: bool = Field(default=False, env="RAG_CACHE_USE_REDIS")
        
        # Security Configuration
        security_enable_rate_limiting: bool = Field(default=True, env="RAG_SECURITY_ENABLE_RATE_LIMITING")
        security_rate_limit_per_hour: int = Field(default=100, ge=1, le=10000, env="RAG_SECURITY_RATE_LIMIT_PER_HOUR")
        security_enable_input_validation: bool = Field(default=True, env="RAG_SECURITY_ENABLE_INPUT_VALIDATION")
        security_max_request_size_mb: int = Field(default=10, ge=1, le=100, env="RAG_SECURITY_MAX_REQUEST_SIZE_MB")
        security_enable_api_key_auth: bool = Field(default=False, env="RAG_SECURITY_ENABLE_API_KEY_AUTH")
        
        # Monitoring Configuration
        monitoring_enabled: bool = Field(default=True, env="RAG_MONITORING_ENABLED")
        monitoring_enable_prometheus: bool = Field(default=True, env="RAG_MONITORING_ENABLE_PROMETHEUS")
        monitoring_enable_health_checks: bool = Field(default=True, env="RAG_MONITORING_ENABLE_HEALTH_CHECKS")
        monitoring_log_level: str = Field(default="INFO", env="RAG_MONITORING_LOG_LEVEL")
        monitoring_metrics_port: int = Field(default=8000, ge=1024, le=65535, env="RAG_MONITORING_METRICS_PORT")
        monitoring_health_check_interval: int = Field(default=60, ge=10, le=3600, env="RAG_MONITORING_HEALTH_CHECK_INTERVAL")
        
        @validator('llm_provider')
        def validate_llm_provider(cls, v):
            allowed_providers = ['ollama', 'openai', 'anthropic', 'huggingface']
            if v not in allowed_providers:
                raise ValueError(f'LLM provider must be one of: {allowed_providers}')
            return v
        
        @validator('embedding_provider')
        def validate_embedding_provider(cls, v):
            allowed_providers = ['ollama', 'openai', 'huggingface', 'sentence_transformers']
            if v not in allowed_providers:
                raise ValueError(f'Embedding provider must be one of: {allowed_providers}')
            return v
        
        @validator('vector_store_provider')
        def validate_vector_store_provider(cls, v):
            allowed_providers = ['qdrant', 'chroma', 'pinecone', 'weaviate']
            if v not in allowed_providers:
                raise ValueError(f'Vector store provider must be one of: {allowed_providers}')
            return v
        
        @validator('monitoring_log_level')
        def validate_log_level(cls, v):
            allowed_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
            if v.upper() not in allowed_levels:
                raise ValueError(f'Log level must be one of: {allowed_levels}')
            return v.upper()
        
        class Config:
            env_prefix = "RAG_"
            env_file = ".env"
            case_sensitive = False
            
        def get_search_config(self) -> SearchConfiguration:
            """Get search configuration."""
            return SearchConfiguration(
                similarity_top_k=self.similarity_top_k,
                max_query_length=self.max_query_length,
                min_relevance_score=self.min_relevance_score,
                enable_hybrid_search=self.enable_hybrid_search,
                enable_query_expansion=self.enable_query_expansion,
                enable_multi_step_reasoning=self.enable_multi_step_reasoning,
                enable_cross_domain_search=self.enable_cross_domain_search
            )
        
        def get_llm_config(self) -> LLMConfiguration:
            """Get LLM configuration."""
            return LLMConfiguration(
                provider=self.llm_provider,
                model_name=self.llm_model_name,
                temperature=self.llm_temperature,
                max_tokens=self.llm_max_tokens,
                timeout_seconds=self.llm_timeout_seconds,
                api_key=self.llm_api_key,
                base_url=self.llm_base_url
            )
        
        def get_embedding_config(self) -> EmbeddingConfiguration:
            """Get embedding configuration."""
            return EmbeddingConfiguration(
                provider=self.embedding_provider,
                model_name=self.embedding_model_name,
                dimension=self.embedding_dimension,
                batch_size=self.embedding_batch_size,
                timeout_seconds=self.embedding_timeout_seconds
            )
        
        def get_vector_store_config(self) -> VectorStoreConfiguration:
            """Get vector store configuration."""
            return VectorStoreConfiguration(
                provider=self.vector_store_provider,
                host=self.vector_store_host,
                port=self.vector_store_port,
                collection_prefix=self.vector_store_collection_prefix,
                distance_metric=self.vector_store_distance_metric,
                timeout_seconds=self.vector_store_timeout_seconds
            )
        
        def get_cache_config(self) -> CacheConfiguration:
            """Get cache configuration."""
            return CacheConfiguration(
                enabled=self.cache_enabled,
                ttl_seconds=self.cache_ttl_seconds,
                max_size=self.cache_max_size,
                redis_url=self.cache_redis_url,
                use_redis=self.cache_use_redis
            )
        
        def get_security_config(self) -> SecurityConfiguration:
            """Get security configuration."""
            return SecurityConfiguration(
                enable_rate_limiting=self.security_enable_rate_limiting,
                rate_limit_per_hour=self.security_rate_limit_per_hour,
                enable_input_validation=self.security_enable_input_validation,
                max_request_size_mb=self.security_max_request_size_mb,
                enable_api_key_auth=self.security_enable_api_key_auth
            )
        
        def get_monitoring_config(self) -> MonitoringConfiguration:
            """Get monitoring configuration."""
            return MonitoringConfiguration(
                enabled=self.monitoring_enabled,
                enable_prometheus=self.monitoring_enable_prometheus,
                enable_health_checks=self.monitoring_enable_health_checks,
                log_level=self.monitoring_log_level,
                metrics_port=self.monitoring_metrics_port,
                health_check_interval=self.monitoring_health_check_interval
            )

else:
    # Fallback configuration class when Pydantic is not available
    class RAGSettings:
        """Fallback configuration class without Pydantic validation."""
        
        def __init__(self):
            self.environment = Environment(os.getenv("RAG_ENVIRONMENT", "development"))
            self.debug = os.getenv("RAG_DEBUG", "false").lower() == "true"
            
            # Search Configuration
            self.similarity_top_k = int(os.getenv("RAG_SIMILARITY_TOP_K", "10"))
            self.max_query_length = int(os.getenv("RAG_MAX_QUERY_LENGTH", "1000"))
            self.min_relevance_score = float(os.getenv("RAG_MIN_RELEVANCE_SCORE", "0.4"))
            self.enable_hybrid_search = os.getenv("RAG_ENABLE_HYBRID_SEARCH", "true").lower() == "true"
            
            logger.warning("Using fallback configuration without Pydantic validation")


# Global configuration instance
_settings = None


def get_settings() -> RAGSettings:
    """Get the global RAG settings instance."""
    global _settings
    if _settings is None:
        _settings = RAGSettings()
        logger.info(f"RAG settings loaded for environment: {_settings.environment}")
    return _settings


def reload_settings():
    """Reload settings from environment variables."""
    global _settings
    _settings = None
    return get_settings()


# Sophistication level configurations
SOPHISTICATION_CONFIGS = {
    SophisticationLevel.BASIC: {
        'similarity_top_k': 5,
        'enable_query_expansion': False,
        'enable_multi_step_reasoning': False,
        'enable_cross_domain_search': False,
        'min_relevance_score': 0.6
    },
    SophisticationLevel.INTERMEDIATE: {
        'similarity_top_k': 10,
        'enable_query_expansion': True,
        'enable_multi_step_reasoning': False,
        'enable_cross_domain_search': True,
        'min_relevance_score': 0.4
    },
    SophisticationLevel.ADVANCED: {
        'similarity_top_k': 15,
        'enable_query_expansion': True,
        'enable_multi_step_reasoning': True,
        'enable_cross_domain_search': True,
        'min_relevance_score': 0.3
    },
    SophisticationLevel.EXPERT: {
        'similarity_top_k': 20,
        'enable_query_expansion': True,
        'enable_multi_step_reasoning': True,
        'enable_cross_domain_search': True,
        'min_relevance_score': 0.2
    }
}


def get_sophistication_config(level: SophisticationLevel) -> Dict[str, Any]:
    """Get configuration for a specific sophistication level."""
    return SOPHISTICATION_CONFIGS.get(level, SOPHISTICATION_CONFIGS[SophisticationLevel.INTERMEDIATE])


def validate_configuration() -> bool:
    """Validate the current configuration."""
    try:
        settings = get_settings()
        
        # Basic validation checks
        if not hasattr(settings, 'similarity_top_k') or settings.similarity_top_k <= 0:
            logger.error("Invalid similarity_top_k configuration")
            return False
        
        if not hasattr(settings, 'max_query_length') or settings.max_query_length <= 0:
            logger.error("Invalid max_query_length configuration")
            return False
        
        logger.info("Configuration validation passed")
        return True
        
    except Exception as e:
        logger.error(f"Configuration validation failed: {e}")
        return False
