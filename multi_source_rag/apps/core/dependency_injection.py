"""
Production-grade dependency injection system for RAG services.

Implements proper dependency injection patterns to improve testability,
maintainability, and adherence to SOLID principles.
"""

import logging
from typing import Protocol, Dict, Any, Optional, Type, TypeVar, Generic
from abc import ABC, abstractmethod
from dataclasses import dataclass
from functools import lru_cache

logger = logging.getLogger(__name__)

T = TypeVar('T')


class ServiceProvider(Protocol):
    """Protocol for service providers."""

    def get_service(self, service_type: Type[T]) -> T:
        """Get a service instance by type."""
        ...


class LLMProvider(Protocol):
    """Protocol for LLM providers."""

    def invoke(self, prompt: str, **kwargs) -> str:
        """Invoke the LLM with a prompt."""
        ...

    def ainvoke(self, prompt: str, **kwargs) -> str:
        """Async invoke the LLM with a prompt."""
        ...


class VectorStoreProvider(Protocol):
    """Protocol for vector store providers."""

    def similarity_search(self, query: str, k: int = 10, **kwargs) -> list:
        """Perform similarity search."""
        ...

    def add_documents(self, documents: list) -> None:
        """Add documents to the vector store."""
        ...


class EmbeddingProvider(Protocol):
    """Protocol for embedding providers."""

    def embed_query(self, text: str) -> list:
        """Embed a query text."""
        ...

    def embed_documents(self, texts: list) -> list:
        """Embed multiple documents."""
        ...


@dataclass
class ServiceConfiguration:
    """Configuration for service dependencies."""
    llm_provider: Optional[LLMProvider] = None
    vector_store_provider: Optional[VectorStoreProvider] = None
    embedding_provider: Optional[EmbeddingProvider] = None
    tenant_slug: str = 'default'
    cache_enabled: bool = True
    monitoring_enabled: bool = True


class ServiceRegistry:
    """
    Central service registry for dependency injection.

    Manages service instances and their dependencies using proper
    dependency injection patterns.
    """

    def __init__(self):
        self._services: Dict[Type, Any] = {}
        self._factories: Dict[Type, callable] = {}
        self._singletons: Dict[Type, Any] = {}
        self._configurations: Dict[str, ServiceConfiguration] = {}

    def register_service(self, service_type: Type[T], instance: T) -> None:
        """Register a service instance."""
        self._services[service_type] = instance
        logger.debug(f"Registered service: {service_type.__name__}")

    def register_factory(self, service_type: Type[T], factory: callable) -> None:
        """Register a factory function for a service type."""
        self._factories[service_type] = factory
        logger.debug(f"Registered factory for: {service_type.__name__}")

    def register_singleton(self, service_type: Type[T], factory: callable) -> None:
        """Register a singleton factory for a service type."""
        self._factories[service_type] = factory
        self._singletons[service_type] = None
        logger.debug(f"Registered singleton factory for: {service_type.__name__}")

    def get_service(self, service_type: Type[T]) -> T:
        """Get a service instance by type."""
        # Check for direct service registration
        if service_type in self._services:
            return self._services[service_type]

        # Check for singleton
        if service_type in self._singletons:
            if self._singletons[service_type] is None:
                self._singletons[service_type] = self._factories[service_type]()
            return self._singletons[service_type]

        # Check for factory
        if service_type in self._factories:
            return self._factories[service_type]()

        raise ValueError(f"No service registered for type: {service_type.__name__}")

    def register_configuration(self, tenant_slug: str, config: ServiceConfiguration) -> None:
        """Register configuration for a tenant."""
        self._configurations[tenant_slug] = config
        logger.debug(f"Registered configuration for tenant: {tenant_slug}")

    def get_configuration(self, tenant_slug: str) -> ServiceConfiguration:
        """Get configuration for a tenant."""
        return self._configurations.get(tenant_slug, ServiceConfiguration(tenant_slug=tenant_slug))


class DependencyInjector:
    """
    Dependency injector for creating services with proper dependencies.

    Provides a clean interface for dependency injection while maintaining
    backward compatibility with existing code.
    """

    def __init__(self, registry: ServiceRegistry):
        self.registry = registry

    def create_search_service(self, tenant_slug: str, **overrides) -> 'RAGSearchService':
        """Create a search service with injected dependencies."""
        config = self.registry.get_configuration(tenant_slug)

        # Get dependencies with overrides
        llm_provider = overrides.get('llm_provider') or config.llm_provider
        vector_store = overrides.get('vector_store_provider') or config.vector_store_provider
        embedding_provider = overrides.get('embedding_provider') or config.embedding_provider

        # Import here to avoid circular imports
        from apps.search.services.rag_search_service import RAGSearchService

        return RAGSearchService(
            tenant_slug=tenant_slug,
            user=None  # Will be set when needed
        )

    def create_retrieval_router(self, tenant_slug: str, **overrides) -> 'RetrievalStrategyRouter':
        """Create a retrieval router with injected dependencies."""
        config = self.registry.get_configuration(tenant_slug)

        llm_provider = overrides.get('llm_provider') or config.llm_provider

        from apps.core.retrieval.router import RetrievalStrategyRouter

        return RetrievalStrategyRouter(
            tenant_slug=tenant_slug,
            llm_provider=llm_provider
        )

    def create_query_enhancer(self, tenant_slug: str, **overrides) -> 'DomainQueryEnhancer':
        """Create a query enhancer with injected dependencies."""
        config = self.registry.get_configuration(tenant_slug)

        llm_provider = overrides.get('llm_provider') or config.llm_provider

        from apps.core.retrieval.query_enhancer import DomainQueryEnhancer

        return DomainQueryEnhancer(
            tenant_slug=tenant_slug,
            llm_provider=llm_provider
        )


# Global service registry instance
_service_registry = ServiceRegistry()
_dependency_injector = DependencyInjector(_service_registry)


def get_service_registry() -> ServiceRegistry:
    """Get the global service registry."""
    return _service_registry


def get_dependency_injector() -> DependencyInjector:
    """Get the global dependency injector."""
    return _dependency_injector


@lru_cache(maxsize=100)
def get_default_llm_provider() -> LLMProvider:
    """Get the default LLM provider."""
    try:
        from apps.core.llama_index_manager import LlamaIndexManager
        manager = LlamaIndexManager('default')
        return manager.get_llm()
    except Exception as e:
        logger.warning(f"Failed to get default LLM provider: {e}")
        return None


@lru_cache(maxsize=100)
def get_default_vector_store(tenant_slug: str = 'default') -> VectorStoreProvider:
    """Get the default vector store provider."""
    try:
        from apps.core.llama_index_manager import LlamaIndexManager
        manager = LlamaIndexManager(tenant_slug)
        return manager.get_vector_store()
    except Exception as e:
        logger.warning(f"Failed to get default vector store: {e}")
        return None


@lru_cache(maxsize=100)
def get_default_embedding_provider() -> EmbeddingProvider:
    """Get the default embedding provider."""
    try:
        from apps.core.llama_index_manager import LlamaIndexManager
        manager = LlamaIndexManager('default')
        return manager.get_embedding_model()
    except Exception as e:
        logger.warning(f"Failed to get default embedding provider: {e}")
        return None


def configure_default_services():
    """Configure default services in the registry."""
    registry = get_service_registry()

    # Register default factories
    registry.register_factory(LLMProvider, get_default_llm_provider)
    registry.register_factory(VectorStoreProvider, lambda: get_default_vector_store('default'))
    registry.register_factory(EmbeddingProvider, get_default_embedding_provider)

    # Register default configuration
    default_config = ServiceConfiguration(
        tenant_slug='default',
        cache_enabled=True,
        monitoring_enabled=True
    )
    registry.register_configuration('default', default_config)

    logger.info("Default services configured")


def configure_tenant_services(tenant_slug: str, config: ServiceConfiguration):
    """Configure services for a specific tenant."""
    registry = get_service_registry()
    registry.register_configuration(tenant_slug, config)
    logger.info(f"Services configured for tenant: {tenant_slug}")


# Initialize default services
configure_default_services()
