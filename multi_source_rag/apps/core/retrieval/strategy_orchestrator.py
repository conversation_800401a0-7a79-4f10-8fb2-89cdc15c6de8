"""
Advanced Query Strategy Orchestrator for Agentic RAG
===================================================

Orchestrates all retrieval strategies with LLM-based decision making at every layer.
Brings together file-level retrieval, cross-domain intelligence, and sophisticated
routing into a unified, intelligent system.

Features:
- LLM-driven strategy selection at every decision point
- Multi-layered routing with confidence scoring
- Adaptive strategy combination based on query complexity
- Performance monitoring and optimization
- UI-configurable sophistication levels

Follows SOLID principles and enterprise patterns.
"""

import logging
from typing import Any, Dict, List, Optional, Tuple, Union
from dataclasses import dataclass, field
from enum import Enum
import asyncio

from llama_index.core.schema import NodeWithScore

from apps.core.constants import RAGConstants, LlamaIndexConstants
from apps.core.exceptions import RetrievalError, ValidationError
from apps.core.utils.llama_index_llm import get_llm
from apps.core.retrieval import (
    RetrievalMode, Sophistication<PERSON><PERSON>l, RetrievalStrategy<PERSON><PERSON>er,
    CrossD<PERSON>in<PERSON><PERSON><PERSON>, DataD<PERSON>in, DomainQueryEnhancer,
    ResultFusionEngine, FusionStrategy, DomainResult,
    RetrievalConfigManager, RoutingMethod
)

logger = logging.getLogger(__name__)


class StrategyComplexity(Enum):
    """Strategy complexity levels for adaptive routing."""
    SIMPLE = "simple"           # Single strategy, single domain
    MODERATE = "moderate"       # Multiple strategies, limited domains
    COMPLEX = "complex"         # Full cross-domain with fusion
    ADAPTIVE = "adaptive"       # AI-driven complexity selection


@dataclass
class StrategyDecision:
    """
    Comprehensive strategy decision with full reasoning chain.
    """
    # Core strategy decisions
    retrieval_mode: RetrievalMode
    domains: List[DataDomain]
    fusion_strategy: FusionStrategy
    sophistication: SophisticationLevel

    # Decision reasoning
    complexity_level: StrategyComplexity
    confidence: float
    reasoning_chain: List[str] = field(default_factory=list)

    # Performance predictions
    estimated_latency_ms: int = 0
    estimated_relevance: float = 0.0
    estimated_diversity: float = 0.0

    # Execution parameters
    execution_params: Dict[str, Any] = field(default_factory=dict)
    fallback_strategies: List['StrategyDecision'] = field(default_factory=list)


class QueryComplexityAnalyzer:
    """
    Analyzes query complexity to determine optimal strategy approach.
    """

    def __init__(self, tenant_slug: str):
        """Initialize complexity analyzer."""
        self.tenant_slug = tenant_slug
        self._llm = None

    def analyze_complexity(self, query: str, intent: str = "default",
                          user_context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Analyze query complexity across multiple dimensions.

        Args:
            query: Search query to analyze
            intent: Query intent for context
            user_context: Optional user context for personalization

        Returns:
            Complexity analysis with recommendations
        """
        try:
            # Multi-dimensional complexity analysis
            complexity_scores = {
                'semantic_complexity': self._analyze_semantic_complexity(query),
                'domain_complexity': self._analyze_domain_complexity(query, intent),
                'temporal_complexity': self._analyze_temporal_complexity(query),
                'structural_complexity': self._analyze_structural_complexity(query),
                'contextual_complexity': self._analyze_contextual_complexity(query, user_context)
            }

            # Overall complexity score
            overall_complexity = sum(complexity_scores.values()) / len(complexity_scores)

            # Determine complexity level
            if overall_complexity < 0.3:
                complexity_level = StrategyComplexity.SIMPLE
            elif overall_complexity < 0.6:
                complexity_level = StrategyComplexity.MODERATE
            elif overall_complexity < 0.8:
                complexity_level = StrategyComplexity.COMPLEX
            else:
                complexity_level = StrategyComplexity.ADAPTIVE

            return {
                'overall_complexity': overall_complexity,
                'complexity_level': complexity_level,
                'dimension_scores': complexity_scores,
                'recommendations': self._generate_complexity_recommendations(complexity_scores, complexity_level)
            }

        except Exception as e:
            logger.error(f"Complexity analysis failed: {e}")
            # Fallback to moderate complexity
            return {
                'overall_complexity': 0.5,
                'complexity_level': StrategyComplexity.MODERATE,
                'dimension_scores': {},
                'recommendations': ['Use moderate complexity due to analysis error']
            }

    def _analyze_semantic_complexity(self, query: str) -> float:
        """Analyze semantic complexity of the query."""
        # Simple heuristics for semantic complexity
        complexity_indicators = [
            len(query.split()) > 15,  # Long queries
            '?' in query and len([w for w in query.split() if w.endswith('?')]) > 1,  # Multiple questions
            any(word in query.lower() for word in ['compare', 'contrast', 'analyze', 'evaluate']),  # Analytical terms
            any(word in query.lower() for word in ['and', 'or', 'but', 'however', 'although']),  # Complex connectors
            len([w for w in query.split() if w.isupper()]) > 2,  # Multiple acronyms/proper nouns
        ]

        return sum(complexity_indicators) / len(complexity_indicators)

    def _analyze_domain_complexity(self, query: str, intent: str) -> float:
        """Analyze cross-domain complexity."""
        query_lower = query.lower()

        # Domain indicators
        domain_indicators = {
            'code': ['code', 'function', 'class', 'method', 'repository', 'commit'],
            'discussion': ['discuss', 'talk', 'conversation', 'meeting', 'chat'],
            'documentation': ['docs', 'guide', 'manual', 'readme', 'wiki'],
            'issues': ['bug', 'issue', 'problem', 'error', 'fix'],
            'process': ['workflow', 'process', 'procedure', 'steps', 'how to']
        }

        # Count how many domains are referenced
        domains_referenced = 0
        for domain, keywords in domain_indicators.items():
            if any(keyword in query_lower for keyword in keywords):
                domains_referenced += 1

        # Normalize to 0-1 scale
        return min(domains_referenced / 3.0, 1.0)

    def _analyze_temporal_complexity(self, query: str) -> float:
        """Analyze temporal complexity (time-based queries)."""
        temporal_indicators = [
            'when', 'timeline', 'history', 'chronology', 'sequence',
            'before', 'after', 'during', 'since', 'until',
            'yesterday', 'today', 'last week', 'last month', 'recent'
        ]

        query_lower = query.lower()
        temporal_count = sum(1 for indicator in temporal_indicators if indicator in query_lower)

        return min(temporal_count / 3.0, 1.0)

    def _analyze_structural_complexity(self, query: str) -> float:
        """Analyze structural complexity of the query."""
        structural_indicators = [
            len(query.split()) > 20,  # Very long queries
            query.count('(') + query.count('[') > 0,  # Parenthetical expressions
            query.count(',') > 2,  # Multiple clauses
            query.count(';') > 0,  # Complex sentence structure
            len([w for w in query.split() if w.startswith('"') or w.endswith('"')]) > 0,  # Quoted terms
        ]

        return sum(structural_indicators) / len(structural_indicators)

    def _analyze_contextual_complexity(self, query: str, user_context: Optional[Dict[str, Any]]) -> float:
        """Analyze contextual complexity based on user context."""
        if not user_context:
            return 0.3  # Default moderate complexity

        complexity_factors = [
            user_context.get('is_expert_user', False),  # Expert users can handle complex results
            user_context.get('query_history_length', 0) > 5,  # Users with query history
            user_context.get('preferred_detail_level', 'medium') == 'high',  # Users who prefer detail
            user_context.get('domain_expertise', []) != [],  # Users with domain expertise
        ]

        return sum(complexity_factors) / len(complexity_factors)

    def _generate_complexity_recommendations(self, scores: Dict[str, float],
                                           level: StrategyComplexity) -> List[str]:
        """Generate recommendations based on complexity analysis."""
        recommendations = []

        if level == StrategyComplexity.SIMPLE:
            recommendations.append("Use single-domain chunk retrieval for optimal speed")
            recommendations.append("Apply basic relevance filtering")
        elif level == StrategyComplexity.MODERATE:
            recommendations.append("Use file-level retrieval with limited cross-domain search")
            recommendations.append("Apply moderate result fusion")
        elif level == StrategyComplexity.COMPLEX:
            recommendations.append("Use full cross-domain search with intelligent fusion")
            recommendations.append("Apply comprehensive query enhancement")
        else:  # ADAPTIVE
            recommendations.append("Use AI-driven adaptive strategy selection")
            recommendations.append("Apply dynamic complexity adjustment based on results")

        # Add specific recommendations based on dimension scores
        if scores.get('domain_complexity', 0) > 0.7:
            recommendations.append("Enable cross-domain intelligence for multi-source queries")

        if scores.get('temporal_complexity', 0) > 0.6:
            recommendations.append("Apply temporal filtering and chronological ordering")

        if scores.get('semantic_complexity', 0) > 0.8:
            recommendations.append("Use advanced semantic analysis and query decomposition")

        return recommendations


class AdvancedStrategyOrchestrator:
    """
    Advanced orchestrator that coordinates all retrieval strategies with LLM-driven decisions.

    This is the master controller that brings together:
    - File-level retrieval strategies
    - Cross-domain intelligence
    - Query complexity analysis
    - Adaptive strategy selection
    - Performance optimization
    """

    def __init__(self, tenant_slug: str):
        """
        Initialize advanced strategy orchestrator.

        Args:
            tenant_slug: Tenant identifier for configuration
        """
        self.tenant_slug = tenant_slug
        self._llm = None

        # Initialize all component routers and managers
        self.strategy_router = RetrievalStrategyRouter(tenant_slug, RoutingMethod.HYBRID)
        self.cross_domain_router = CrossDomainRouter(tenant_slug)
        self.query_enhancer = DomainQueryEnhancer(tenant_slug)
        self.fusion_engine = ResultFusionEngine(tenant_slug)
        self.config_manager = RetrievalConfigManager(tenant_slug)
        self.complexity_analyzer = QueryComplexityAnalyzer(tenant_slug)

        # Decision cache for performance
        self._decision_cache = {}

        # Performance monitoring
        self._performance_stats = {
            'total_queries': 0,
            'avg_latency_ms': 0,
            'strategy_usage': {},
            'complexity_distribution': {}
        }

    def orchestrate_strategy(self, query: str, intent: str = "default",
                           sophistication: Optional[SophisticationLevel] = None,
                           user_context: Optional[Dict[str, Any]] = None,
                           **kwargs) -> StrategyDecision:
        """
        Orchestrate optimal strategy selection using LLM-driven analysis.

        Args:
            query: Search query
            intent: Query intent
            sophistication: Optional sophistication level override
            user_context: Optional user context for personalization
            **kwargs: Additional parameters

        Returns:
            StrategyDecision with comprehensive routing plan

        Raises:
            ValidationError: If input validation fails
            RetrievalError: If orchestration fails
        """
        try:
            # Input validation
            if not query or not query.strip():
                raise ValidationError("Query cannot be empty for strategy orchestration")

            # Check cache
            cache_key = self._generate_cache_key(query, intent, sophistication, user_context)
            if cache_key in self._decision_cache:
                cached_decision = self._decision_cache[cache_key]
                logger.debug(f"Using cached strategy decision for query: {query[:50]}...")
                return cached_decision

            # Step 1: Analyze query complexity
            complexity_analysis = self.complexity_analyzer.analyze_complexity(
                query, intent, user_context
            )

            # Step 2: Get configuration for sophistication level
            config = self.config_manager.get_config(intent, sophistication)

            # Step 3: LLM-driven strategy orchestration
            strategy_decision = self._orchestrate_with_llm(
                query, intent, complexity_analysis, config, user_context, **kwargs
            )

            # Step 4: Validate and optimize decision
            optimized_decision = self._optimize_strategy_decision(strategy_decision, config)

            # Step 5: Generate fallback strategies
            optimized_decision.fallback_strategies = self._generate_fallback_strategies(
                optimized_decision, complexity_analysis
            )

            # Cache the decision
            self._decision_cache[cache_key] = optimized_decision

            # Update performance stats
            self._update_performance_stats(optimized_decision)

            logger.info(f"Orchestrated {optimized_decision.complexity_level.value} strategy with {optimized_decision.confidence:.2f} confidence")
            logger.debug(f"Strategy reasoning: {' → '.join(optimized_decision.reasoning_chain)}")

            return optimized_decision

        except Exception as e:
            logger.error(f"Strategy orchestration failed for query '{query}': {e}")
            # Fallback to simple strategy
            return self._create_fallback_strategy(query, intent, sophistication)

    def _orchestrate_with_llm(self, query: str, intent: str,
                             complexity_analysis: Dict[str, Any],
                             config: Any, user_context: Optional[Dict[str, Any]],
                             **kwargs) -> StrategyDecision:
        """Use LLM to orchestrate optimal strategy selection."""
        try:
            if self._llm is None:
                # Use stable model name instead of intent-based naming
                self._llm = get_llm(model_name="gemini-1.5-flash")

            orchestration_prompt = f"""
            You are an expert RAG strategy orchestrator. Analyze this query and determine the optimal retrieval strategy.

            Query: "{query}"
            Intent: {intent}
            Complexity Level: {complexity_analysis['complexity_level'].value}
            Overall Complexity: {complexity_analysis['overall_complexity']:.2f}

            Available Strategies:
            1. CHUNKS - Standard chunk-based retrieval
            2. FILES_VIA_CONTENT - Retrieve entire files by content
            3. FILES_VIA_METADATA - Retrieve files by name/path

            Available Domains:
            - GITHUB_CODE, GITHUB_ISSUES, GITHUB_WIKI
            - SLACK_CONVERSATIONS, SLACK_ENGINEERING, SLACK_SUPPORT
            - DOCUMENTATION, MEETING_NOTES

            Fusion Strategies:
            - WEIGHTED_MERGE, RECIPROCAL_RANK_FUSION, DIVERSITY_OPTIMIZED
            - PROBLEM_RESOLUTION, PROCESS_FLOW, COMPREHENSIVE

            Sophistication Levels:
            - BASIC (simple, fast), STANDARD (balanced), ADVANCED (comprehensive), EXPERT (maximum capability)

            Consider:
            1. Query complexity and user intent
            2. Expected result type (specific files vs general information)
            3. Cross-domain requirements
            4. Performance vs accuracy tradeoffs
            5. User expertise level

            Respond with:
            RETRIEVAL_MODE: [mode]
            DOMAINS: [domain1, domain2, ...]
            FUSION_STRATEGY: [strategy]
            SOPHISTICATION: [level]
            CONFIDENCE: [0.0-1.0]
            REASONING: [detailed explanation]
            """

            # Handle different LLM interfaces properly
            try:
                response = self._llm.complete(orchestration_prompt)
                response_text = str(response)
            except AttributeError:
                try:
                    response_text = self._llm.invoke(orchestration_prompt)
                except AttributeError:
                    # Final fallback for different LLM interfaces
                    response_text = str(self._llm(orchestration_prompt))

            # Parse LLM response using the response text
            return self._parse_llm_orchestration_response(response_text, complexity_analysis)

        except Exception as e:
            logger.warning(f"LLM orchestration failed: {e}, using rule-based fallback")
            return self._orchestrate_with_rules(query, intent, complexity_analysis, config)

    def _parse_llm_orchestration_response(self, response: str,
                                        complexity_analysis: Dict[str, Any]) -> StrategyDecision:
        """Parse LLM orchestration response into StrategyDecision."""
        try:
            lines = response.strip().split('\n')
            parsed_data = {}

            for line in lines:
                if ':' in line:
                    key, value = line.split(':', 1)
                    parsed_data[key.strip()] = value.strip()

            # Parse retrieval mode
            retrieval_mode_str = parsed_data.get('RETRIEVAL_MODE', 'CHUNKS')
            retrieval_mode = RetrievalMode(retrieval_mode_str.lower())

            # Parse domains
            domains_str = parsed_data.get('DOMAINS', 'github_code,documentation')
            domains = [DataDomain(d.strip().lower()) for d in domains_str.split(',') if d.strip()]

            # Parse fusion strategy
            fusion_str = parsed_data.get('FUSION_STRATEGY', 'weighted_merge')
            fusion_strategy = FusionStrategy(fusion_str.lower())

            # Parse sophistication
            soph_str = parsed_data.get('SOPHISTICATION', 'standard')
            sophistication = SophisticationLevel(soph_str.lower())

            # Parse confidence
            confidence = float(parsed_data.get('CONFIDENCE', '0.7'))

            # Parse reasoning
            reasoning = parsed_data.get('REASONING', 'LLM-based strategy orchestration')

            return StrategyDecision(
                retrieval_mode=retrieval_mode,
                domains=domains,
                fusion_strategy=fusion_strategy,
                sophistication=sophistication,
                complexity_level=complexity_analysis['complexity_level'],
                confidence=confidence,
                reasoning_chain=[reasoning],
                execution_params={}
            )

        except Exception as e:
            logger.error(f"Failed to parse LLM orchestration response: {e}")
            # Fallback to rule-based decision
            return self._orchestrate_with_rules("", "", complexity_analysis, None)

    def _orchestrate_with_rules(self, query: str, intent: str,
                               complexity_analysis: Dict[str, Any],
                               config: Any) -> StrategyDecision:
        """Fallback rule-based orchestration."""
        complexity_level = complexity_analysis['complexity_level']

        if complexity_level == StrategyComplexity.SIMPLE:
            return StrategyDecision(
                retrieval_mode=RetrievalMode.CHUNKS,
                domains=[DataDomain.GITHUB_CODE],
                fusion_strategy=FusionStrategy.WEIGHTED_MERGE,
                sophistication=SophisticationLevel.BASIC,
                complexity_level=complexity_level,
                confidence=0.7,
                reasoning_chain=["Rule-based simple strategy for low complexity query"]
            )
        elif complexity_level == StrategyComplexity.MODERATE:
            return StrategyDecision(
                retrieval_mode=RetrievalMode.FILES_VIA_CONTENT,
                domains=[DataDomain.GITHUB_CODE, DataDomain.DOCUMENTATION],
                fusion_strategy=FusionStrategy.RECIPROCAL_RANK_FUSION,
                sophistication=SophisticationLevel.STANDARD,
                complexity_level=complexity_level,
                confidence=0.75,
                reasoning_chain=["Rule-based moderate strategy for medium complexity query"]
            )
        else:  # COMPLEX or ADAPTIVE
            return StrategyDecision(
                retrieval_mode=RetrievalMode.FILES_VIA_CONTENT,
                domains=[DataDomain.GITHUB_CODE, DataDomain.SLACK_ENGINEERING, DataDomain.DOCUMENTATION],
                fusion_strategy=FusionStrategy.COMPREHENSIVE,
                sophistication=SophisticationLevel.ADVANCED,
                complexity_level=complexity_level,
                confidence=0.8,
                reasoning_chain=["Rule-based complex strategy for high complexity query"]
            )

    def _optimize_strategy_decision(self, decision: StrategyDecision, config: Any) -> StrategyDecision:
        """Optimize strategy decision based on configuration and constraints."""
        # Apply configuration constraints
        if config and hasattr(config, 'similarity_top_k'):
            decision.execution_params['top_k'] = config.similarity_top_k

        # Estimate performance metrics
        decision.estimated_latency_ms = self._estimate_latency(decision)
        decision.estimated_relevance = self._estimate_relevance(decision)
        decision.estimated_diversity = self._estimate_diversity(decision)

        return decision

    def _estimate_latency(self, decision: StrategyDecision) -> int:
        """Estimate query latency based on strategy complexity."""
        base_latency = 500  # Base latency in ms

        # Add latency for complexity
        complexity_multiplier = {
            StrategyComplexity.SIMPLE: 1.0,
            StrategyComplexity.MODERATE: 1.5,
            StrategyComplexity.COMPLEX: 2.0,
            StrategyComplexity.ADAPTIVE: 2.5
        }

        latency = base_latency * complexity_multiplier[decision.complexity_level]

        # Add latency for multiple domains
        latency += len(decision.domains) * 200

        return int(latency)

    def _estimate_relevance(self, decision: StrategyDecision) -> float:
        """Estimate result relevance based on strategy sophistication."""
        sophistication_relevance = {
            SophisticationLevel.BASIC: 0.7,
            SophisticationLevel.STANDARD: 0.8,
            SophisticationLevel.ADVANCED: 0.9,
            SophisticationLevel.EXPERT: 0.95
        }

        return sophistication_relevance[decision.sophistication]

    def _estimate_diversity(self, decision: StrategyDecision) -> float:
        """Estimate result diversity based on domains and fusion strategy."""
        domain_diversity = min(len(decision.domains) / 5.0, 1.0)

        fusion_diversity = {
            FusionStrategy.WEIGHTED_MERGE: 0.6,
            FusionStrategy.RECIPROCAL_RANK_FUSION: 0.7,
            FusionStrategy.DIVERSITY_OPTIMIZED: 0.9,
            FusionStrategy.COMPREHENSIVE: 0.8
        }

        return (domain_diversity + fusion_diversity.get(decision.fusion_strategy, 0.7)) / 2

    def _generate_fallback_strategies(self, primary: StrategyDecision,
                                    complexity_analysis: Dict[str, Any]) -> List[StrategyDecision]:
        """Generate fallback strategies for resilience."""
        fallbacks = []

        # Simpler strategy as first fallback
        if primary.complexity_level != StrategyComplexity.SIMPLE:
            fallbacks.append(StrategyDecision(
                retrieval_mode=RetrievalMode.CHUNKS,
                domains=[primary.domains[0]] if primary.domains else [DataDomain.GITHUB_CODE],
                fusion_strategy=FusionStrategy.WEIGHTED_MERGE,
                sophistication=SophisticationLevel.BASIC,
                complexity_level=StrategyComplexity.SIMPLE,
                confidence=0.6,
                reasoning_chain=["Simplified fallback strategy"]
            ))

        # Single domain fallback
        if len(primary.domains) > 1:
            fallbacks.append(StrategyDecision(
                retrieval_mode=primary.retrieval_mode,
                domains=[primary.domains[0]],
                fusion_strategy=FusionStrategy.WEIGHTED_MERGE,
                sophistication=primary.sophistication,
                complexity_level=StrategyComplexity.MODERATE,
                confidence=0.5,
                reasoning_chain=["Single domain fallback strategy"]
            ))

        return fallbacks

    def _create_fallback_strategy(self, query: str, intent: str,
                                 sophistication: Optional[SophisticationLevel]) -> StrategyDecision:
        """Create a safe fallback strategy."""
        return StrategyDecision(
            retrieval_mode=RetrievalMode.CHUNKS,
            domains=[DataDomain.GITHUB_CODE],
            fusion_strategy=FusionStrategy.WEIGHTED_MERGE,
            sophistication=sophistication or SophisticationLevel.STANDARD,
            complexity_level=StrategyComplexity.SIMPLE,
            confidence=0.5,
            reasoning_chain=["Emergency fallback strategy due to orchestration failure"]
        )

    def _generate_cache_key(self, query: str, intent: str,
                           sophistication: Optional[SophisticationLevel],
                           user_context: Optional[Dict[str, Any]]) -> str:
        """Generate cache key for strategy decisions."""
        query_key = query[:100].lower().strip()
        soph_key = sophistication.value if sophistication else "default"
        context_key = str(hash(str(user_context))) if user_context else "none"
        return f"{self.tenant_slug}_{intent}_{soph_key}_{context_key}_{hash(query_key)}"

    def _update_performance_stats(self, decision: StrategyDecision) -> None:
        """Update performance statistics."""
        self._performance_stats['total_queries'] += 1

        # Update strategy usage
        strategy_key = f"{decision.retrieval_mode.value}_{decision.complexity_level.value}"
        self._performance_stats['strategy_usage'][strategy_key] = \
            self._performance_stats['strategy_usage'].get(strategy_key, 0) + 1

        # Update complexity distribution
        complexity_key = decision.complexity_level.value
        self._performance_stats['complexity_distribution'][complexity_key] = \
            self._performance_stats['complexity_distribution'].get(complexity_key, 0) + 1

    def clear_cache(self) -> None:
        """Clear the decision cache."""
        self._decision_cache.clear()
        logger.info("Strategy orchestrator cache cleared")

    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics."""
        return self._performance_stats.copy()

    def cleanup_resources(self) -> None:
        """Clean up resources and caches."""
        self.clear_cache()
        self.strategy_router.clear_cache()
        self.cross_domain_router.clear_cache()
        self.query_enhancer.clear_cache()
        self.fusion_engine.clear_cache()
        logger.info("Strategy orchestrator resources cleaned up")
