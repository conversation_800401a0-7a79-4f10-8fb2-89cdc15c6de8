"""
Intelligent Result Fusion Engine for Cross-Domain RAG
=====================================================

Fuses results from multiple data domains with intelligent ranking,
deduplication, and diversity optimization.

Features:
- Multiple fusion strategies (weighted, RRF, semantic)
- Cross-domain diversity optimization
- Intelligent deduplication
- Relevance score normalization
- Performance monitoring

Follows SOLID principles and enterprise patterns.
"""

import logging
import math
from typing import Any, Dict, List, Optional, Tuple, Set
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict

from llama_index.core.schema import NodeWithScore

from apps.core.constants import RAGConstants, LlamaIndexConstants
from apps.core.exceptions import RetrievalError
from apps.core.retrieval.cross_domain_router import DataDomain

logger = logging.getLogger(__name__)

try:
    from datasketch import MinHashLSH, MinHash
    MINHASH_AVAILABLE = True
except ImportError:
    MINHASH_AVAILABLE = False
    logger.warning("datasketch not available, falling back to simple text similarity")


class SemanticDeduplicator:
    """
    High-performance semantic deduplication using MinHash LSH.

    Replaces O(n²) text similarity with O(n) MinHash-based deduplication
    for significant performance improvement on large result sets.
    """

    def __init__(self, threshold: float = 0.8, num_perm: int = 128):
        """
        Initialize semantic deduplicator.

        Args:
            threshold: Similarity threshold for considering items duplicates
            num_perm: Number of permutations for MinHash (higher = more accurate)
        """
        self.threshold = threshold
        self.num_perm = num_perm

        if MINHASH_AVAILABLE:
            self.lsh = MinHashLSH(threshold=threshold, num_perm=num_perm)
            self.use_minhash = True
        else:
            self.use_minhash = False
            self._seen_texts = set()
            logger.warning("Using fallback text deduplication without MinHash")

    def is_duplicate(self, text: str, identifier: str = None) -> bool:
        """
        Check if text is a duplicate of previously seen content.

        Args:
            text: Text content to check
            identifier: Optional unique identifier for the text

        Returns:
            True if text is considered a duplicate
        """
        if not text or not text.strip():
            return True

        if self.use_minhash:
            return self._is_duplicate_minhash(text, identifier)
        else:
            return self._is_duplicate_fallback(text)

    def _is_duplicate_minhash(self, text: str, identifier: str = None) -> bool:
        """Check for duplicates using MinHash LSH."""
        # Create MinHash for the text
        minhash = MinHash(num_perm=self.num_perm)

        # Tokenize and hash the text
        words = text.lower().split()
        for word in words:
            minhash.update(word.encode('utf8'))

        # Check if similar content exists
        similar_items = self.lsh.query(minhash)

        if similar_items:
            return True

        # Add to LSH for future comparisons
        key = identifier or f"text_{len(self.lsh.keys)}"
        self.lsh.insert(key, minhash)
        return False

    def _is_duplicate_fallback(self, text: str) -> bool:
        """Fallback deduplication using simple text comparison."""
        # Simple hash-based deduplication
        text_hash = hash(text.lower().strip())

        if text_hash in self._seen_texts:
            return True

        self._seen_texts.add(text_hash)
        return False

    def clear(self):
        """Clear the deduplicator state."""
        if self.use_minhash:
            self.lsh = MinHashLSH(threshold=self.threshold, num_perm=self.num_perm)
        else:
            self._seen_texts.clear()

    def get_stats(self) -> Dict[str, Any]:
        """Get deduplication statistics."""
        if self.use_minhash:
            return {
                'method': 'minhash_lsh',
                'threshold': self.threshold,
                'num_perm': self.num_perm,
                'items_stored': len(self.lsh.keys) if hasattr(self.lsh, 'keys') else 0
            }
        else:
            return {
                'method': 'fallback_hash',
                'items_stored': len(self._seen_texts)
            }


class FusionStrategy(Enum):
    """Result fusion strategies."""
    WEIGHTED_MERGE = "weighted_merge"
    RECIPROCAL_RANK_FUSION = "reciprocal_rank_fusion"
    SEMANTIC_FUSION = "semantic_fusion"
    DIVERSITY_OPTIMIZED = "diversity_optimized"
    PROBLEM_RESOLUTION = "problem_resolution_merge"
    PROCESS_FLOW = "process_flow_merge"
    COMPREHENSIVE = "comprehensive_merge"


@dataclass
class DomainResult:
    """Results from a specific domain."""
    domain: DataDomain
    nodes: List[NodeWithScore]
    query_used: str
    confidence: float
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class FusionResult:
    """Fused results from multiple domains."""
    fused_nodes: List[NodeWithScore]
    fusion_strategy: FusionStrategy
    domain_contributions: Dict[DataDomain, int]
    diversity_score: float
    confidence: float
    reasoning: str
    metadata: Dict[str, Any] = field(default_factory=dict)


class ResultFusionEngine:
    """
    Intelligent engine for fusing results from multiple data domains.

    Combines results from different domains while optimizing for relevance,
    diversity, and user intent satisfaction.
    """

    def __init__(self, tenant_slug: str):
        """
        Initialize result fusion engine.

        Args:
            tenant_slug: Tenant identifier for configuration
        """
        self.tenant_slug = tenant_slug

        # High-performance semantic deduplicator
        self.deduplicator = SemanticDeduplicator(threshold=0.8, num_perm=128)
        self._fusion_cache = {}

        # Fusion strategy configurations
        self._strategy_configs = self._build_strategy_configs()

        # Domain importance weights
        self._domain_weights = self._build_domain_weights()

        # Diversity optimization parameters
        self._diversity_params = self._build_diversity_params()

    def fuse_domain_results(self, domain_results: List[DomainResult],
                           strategy: FusionStrategy = FusionStrategy.WEIGHTED_MERGE,
                           target_count: int = 10,
                           diversity_weight: float = 0.3,
                           **kwargs) -> FusionResult:
        """
        Fuse results from multiple domains using specified strategy.

        Args:
            domain_results: Results from different domains
            strategy: Fusion strategy to use
            target_count: Target number of results
            diversity_weight: Weight for diversity optimization (0.0-1.0)
            **kwargs: Additional fusion parameters

        Returns:
            FusionResult with combined and optimized results

        Raises:
            RetrievalError: If fusion fails
        """
        try:
            # Input validation
            if not domain_results:
                return FusionResult(
                    fused_nodes=[],
                    fusion_strategy=strategy,
                    domain_contributions={},
                    diversity_score=0.0,
                    confidence=0.0,
                    reasoning="No domain results to fuse"
                )

            # Check cache
            cache_key = self._generate_cache_key(domain_results, strategy, target_count)
            if cache_key in self._fusion_cache:
                cached_result = self._fusion_cache[cache_key]
                logger.debug(f"Using cached fusion result")
                return cached_result

            # Apply fusion strategy
            if strategy == FusionStrategy.WEIGHTED_MERGE:
                result = self._fuse_weighted_merge(domain_results, target_count, diversity_weight)
            elif strategy == FusionStrategy.RECIPROCAL_RANK_FUSION:
                result = self._fuse_reciprocal_rank(domain_results, target_count, diversity_weight)
            elif strategy == FusionStrategy.SEMANTIC_FUSION:
                result = self._fuse_semantic(domain_results, target_count, diversity_weight)
            elif strategy == FusionStrategy.DIVERSITY_OPTIMIZED:
                result = self._fuse_diversity_optimized(domain_results, target_count, diversity_weight)
            elif strategy == FusionStrategy.PROBLEM_RESOLUTION:
                result = self._fuse_problem_resolution(domain_results, target_count, diversity_weight)
            elif strategy == FusionStrategy.PROCESS_FLOW:
                result = self._fuse_process_flow(domain_results, target_count, diversity_weight)
            else:  # COMPREHENSIVE
                result = self._fuse_comprehensive(domain_results, target_count, diversity_weight)

            # Cache the result
            self._fusion_cache[cache_key] = result

            logger.info(f"Fused {len(result.fused_nodes)} results from {len(domain_results)} domains using {strategy.value}")
            logger.debug(f"Domain contributions: {result.domain_contributions}")

            return result

        except Exception as e:
            logger.error(f"Result fusion failed: {e}")
            # Fallback to simple concatenation
            all_nodes = []
            domain_contributions = {}

            for domain_result in domain_results:
                all_nodes.extend(domain_result.nodes)
                domain_contributions[domain_result.domain] = len(domain_result.nodes)

            return FusionResult(
                fused_nodes=all_nodes[:target_count],
                fusion_strategy=strategy,
                domain_contributions=domain_contributions,
                diversity_score=0.5,
                confidence=0.5,
                reasoning=f"Fallback fusion due to error: {e}"
            )

    def _fuse_weighted_merge(self, domain_results: List[DomainResult], target_count: int, diversity_weight: float) -> FusionResult:
        """Fuse results using weighted merge based on domain importance and confidence."""
        weighted_nodes = []
        domain_contributions = defaultdict(int)

        for domain_result in domain_results:
            domain_weight = self._domain_weights.get(domain_result.domain, 1.0)
            confidence_weight = domain_result.confidence

            for node in domain_result.nodes:
                # Calculate weighted score
                original_score = node.score if node.score else 0.5
                weighted_score = original_score * domain_weight * confidence_weight

                # Create new node with weighted score
                weighted_node = NodeWithScore(node=node.node, score=weighted_score)
                weighted_nodes.append((weighted_node, domain_result.domain))

        # Sort by weighted score
        weighted_nodes.sort(key=lambda x: x[0].score, reverse=True)

        # Apply diversity optimization
        if diversity_weight > 0:
            diverse_nodes = self._optimize_diversity(weighted_nodes, target_count, diversity_weight)
        else:
            diverse_nodes = weighted_nodes[:target_count]

        # Count domain contributions
        for node, domain in diverse_nodes:
            domain_contributions[domain] += 1

        # Calculate diversity score
        diversity_score = self._calculate_diversity_score(domain_contributions)

        return FusionResult(
            fused_nodes=[node for node, _ in diverse_nodes],
            fusion_strategy=FusionStrategy.WEIGHTED_MERGE,
            domain_contributions=dict(domain_contributions),
            diversity_score=diversity_score,
            confidence=0.8,
            reasoning=f"Weighted merge with domain weights and {diversity_weight:.1f} diversity optimization"
        )

    def _fuse_reciprocal_rank(self, domain_results: List[DomainResult], target_count: int, diversity_weight: float) -> FusionResult:
        """Fuse results using Reciprocal Rank Fusion (RRF)."""
        rrf_scores = defaultdict(float)
        node_domains = {}
        all_nodes = {}

        k = LlamaIndexConstants.RRF_K  # RRF constant

        for domain_result in domain_results:
            for rank, node in enumerate(domain_result.nodes):
                node_id = str(node.node.node_id)

                # RRF score calculation
                rrf_score = 1.0 / (k + rank + 1)
                rrf_scores[node_id] += rrf_score

                # Store node and domain mapping
                all_nodes[node_id] = node
                node_domains[node_id] = domain_result.domain

        # Sort by RRF score
        sorted_nodes = sorted(rrf_scores.items(), key=lambda x: x[1], reverse=True)

        # Create result nodes with RRF scores
        result_nodes = []
        domain_contributions = defaultdict(int)

        for node_id, rrf_score in sorted_nodes[:target_count]:
            node = all_nodes[node_id]
            domain = node_domains[node_id]

            # Update node score with RRF score
            rrf_node = NodeWithScore(node=node.node, score=rrf_score)
            result_nodes.append(rrf_node)
            domain_contributions[domain] += 1

        diversity_score = self._calculate_diversity_score(domain_contributions)

        return FusionResult(
            fused_nodes=result_nodes,
            fusion_strategy=FusionStrategy.RECIPROCAL_RANK_FUSION,
            domain_contributions=dict(domain_contributions),
            diversity_score=diversity_score,
            confidence=0.85,
            reasoning=f"Reciprocal Rank Fusion with k={k}"
        )

    def _fuse_semantic(self, domain_results: List[DomainResult], target_count: int, diversity_weight: float) -> FusionResult:
        """Fuse results using semantic similarity clustering."""
        # For now, implement as enhanced weighted merge with semantic grouping
        # In a full implementation, this would use embedding similarity

        all_nodes_with_domains = []
        for domain_result in domain_results:
            for node in domain_result.nodes:
                all_nodes_with_domains.append((node, domain_result.domain))

        # Sort by original scores
        all_nodes_with_domains.sort(key=lambda x: x[0].score if x[0].score else 0, reverse=True)

        # Simple semantic deduplication based on text similarity
        deduplicated_nodes = self._deduplicate_semantically(all_nodes_with_domains)

        # Take top results
        result_nodes = deduplicated_nodes[:target_count]

        domain_contributions = defaultdict(int)
        for _, domain in result_nodes:
            domain_contributions[domain] += 1

        diversity_score = self._calculate_diversity_score(domain_contributions)

        return FusionResult(
            fused_nodes=[node for node, _ in result_nodes],
            fusion_strategy=FusionStrategy.SEMANTIC_FUSION,
            domain_contributions=dict(domain_contributions),
            diversity_score=diversity_score,
            confidence=0.75,
            reasoning="Semantic fusion with deduplication"
        )

    def _fuse_diversity_optimized(self, domain_results: List[DomainResult], target_count: int, diversity_weight: float) -> FusionResult:
        """Fuse results with maximum diversity optimization."""
        all_nodes_with_domains = []
        for domain_result in domain_results:
            for node in domain_result.nodes:
                all_nodes_with_domains.append((node, domain_result.domain))

        # Sort by score
        all_nodes_with_domains.sort(key=lambda x: x[0].score if x[0].score else 0, reverse=True)

        # Maximize diversity
        diverse_nodes = self._maximize_diversity(all_nodes_with_domains, target_count)

        domain_contributions = defaultdict(int)
        for _, domain in diverse_nodes:
            domain_contributions[domain] += 1

        diversity_score = self._calculate_diversity_score(domain_contributions)

        return FusionResult(
            fused_nodes=[node for node, _ in diverse_nodes],
            fusion_strategy=FusionStrategy.DIVERSITY_OPTIMIZED,
            domain_contributions=dict(domain_contributions),
            diversity_score=diversity_score,
            confidence=0.7,
            reasoning="Maximum diversity optimization"
        )

    def _fuse_problem_resolution(self, domain_results: List[DomainResult], target_count: int, diversity_weight: float) -> FusionResult:
        """Fuse results optimized for problem resolution workflows."""
        # Prioritize: Issues > Support discussions > Code > Documentation
        priority_order = [
            DataDomain.GITHUB_ISSUES,
            DataDomain.SLACK_SUPPORT,
            DataDomain.SLACK_ENGINEERING,
            DataDomain.GITHUB_CODE,
            DataDomain.DOCUMENTATION
        ]

        prioritized_nodes = []
        domain_contributions = defaultdict(int)

        # Add nodes in priority order
        for priority_domain in priority_order:
            for domain_result in domain_results:
                if domain_result.domain == priority_domain:
                    for node in domain_result.nodes[:target_count//len(priority_order) + 1]:
                        prioritized_nodes.append(node)
                        domain_contributions[priority_domain] += 1
                        if len(prioritized_nodes) >= target_count:
                            break
            if len(prioritized_nodes) >= target_count:
                break

        diversity_score = self._calculate_diversity_score(domain_contributions)

        return FusionResult(
            fused_nodes=prioritized_nodes[:target_count],
            fusion_strategy=FusionStrategy.PROBLEM_RESOLUTION,
            domain_contributions=dict(domain_contributions),
            diversity_score=diversity_score,
            confidence=0.8,
            reasoning="Problem resolution workflow optimization"
        )

    def _fuse_process_flow(self, domain_results: List[DomainResult], target_count: int, diversity_weight: float) -> FusionResult:
        """Fuse results optimized for process flow understanding."""
        # Prioritize: Documentation > Meeting notes > Discussions > Code
        priority_order = [
            DataDomain.DOCUMENTATION,
            DataDomain.MEETING_NOTES,
            DataDomain.GITHUB_DISCUSSIONS,
            DataDomain.SLACK_ENGINEERING,
            DataDomain.GITHUB_CODE
        ]

        return self._fuse_by_priority(domain_results, priority_order, target_count, "Process flow optimization")

    def _fuse_comprehensive(self, domain_results: List[DomainResult], target_count: int, diversity_weight: float) -> FusionResult:
        """Fuse results for comprehensive knowledge synthesis."""
        # Use RRF for balanced representation
        return self._fuse_reciprocal_rank(domain_results, target_count, diversity_weight)

    def _fuse_by_priority(self, domain_results: List[DomainResult], priority_order: List[DataDomain], target_count: int, reasoning: str) -> FusionResult:
        """Helper method to fuse results by domain priority."""
        prioritized_nodes = []
        domain_contributions = defaultdict(int)

        nodes_per_domain = max(1, target_count // len(priority_order))

        for priority_domain in priority_order:
            for domain_result in domain_results:
                if domain_result.domain == priority_domain:
                    for node in domain_result.nodes[:nodes_per_domain]:
                        prioritized_nodes.append(node)
                        domain_contributions[priority_domain] += 1
                        if len(prioritized_nodes) >= target_count:
                            break
            if len(prioritized_nodes) >= target_count:
                break

        diversity_score = self._calculate_diversity_score(domain_contributions)

        return FusionResult(
            fused_nodes=prioritized_nodes[:target_count],
            fusion_strategy=FusionStrategy.PROCESS_FLOW,
            domain_contributions=dict(domain_contributions),
            diversity_score=diversity_score,
            confidence=0.75,
            reasoning=reasoning
        )

    def _optimize_diversity(self, nodes_with_domains: List[Tuple[NodeWithScore, DataDomain]], target_count: int, diversity_weight: float) -> List[Tuple[NodeWithScore, DataDomain]]:
        """Optimize result diversity while maintaining relevance."""
        if diversity_weight <= 0:
            return nodes_with_domains[:target_count]

        selected_nodes = []
        domain_counts = defaultdict(int)

        for node, domain in nodes_with_domains:
            if len(selected_nodes) >= target_count:
                break

            # Calculate diversity bonus
            current_domain_count = domain_counts[domain]
            diversity_penalty = current_domain_count * diversity_weight

            # Adjust score with diversity consideration
            adjusted_score = (node.score if node.score else 0.5) - diversity_penalty

            # Add if score is still positive or we need more results
            if adjusted_score > 0 or len(selected_nodes) < target_count // 2:
                selected_nodes.append((node, domain))
                domain_counts[domain] += 1

        return selected_nodes

    def _maximize_diversity(self, nodes_with_domains: List[Tuple[NodeWithScore, DataDomain]], target_count: int) -> List[Tuple[NodeWithScore, DataDomain]]:
        """Maximize diversity by ensuring balanced domain representation."""
        domain_groups = defaultdict(list)

        # Group by domain
        for node, domain in nodes_with_domains:
            domain_groups[domain].append((node, domain))

        # Round-robin selection from domains
        selected_nodes = []
        domain_iterators = {domain: iter(nodes) for domain, nodes in domain_groups.items()}

        while len(selected_nodes) < target_count and domain_iterators:
            for domain in list(domain_iterators.keys()):
                try:
                    node_tuple = next(domain_iterators[domain])
                    selected_nodes.append(node_tuple)
                    if len(selected_nodes) >= target_count:
                        break
                except StopIteration:
                    del domain_iterators[domain]

        return selected_nodes

    def _deduplicate_semantically(self, nodes_with_domains: List[Tuple[NodeWithScore, DataDomain]]) -> List[Tuple[NodeWithScore, DataDomain]]:
        """
        Remove semantically similar nodes using high-performance MinHash deduplication.

        Replaces O(n²) text similarity with O(n) MinHash-based approach
        for significant performance improvement on large result sets.
        """
        # Clear deduplicator state for fresh deduplication
        self.deduplicator.clear()

        deduplicated = []

        for i, (node, domain) in enumerate(nodes_with_domains):
            try:
                # Get node content for deduplication
                node_text = node.node.get_content()
                if not node_text or not node_text.strip():
                    continue

                # Use high-performance semantic deduplicator
                identifier = f"node_{i}_{domain.value}"
                if not self.deduplicator.is_duplicate(node_text, identifier):
                    deduplicated.append((node, domain))

            except Exception as e:
                logger.warning(f"Error during deduplication for node {i}: {e}")
                # Include node if deduplication fails to avoid losing content
                deduplicated.append((node, domain))

        # Log deduplication statistics
        stats = self.deduplicator.get_stats()
        logger.debug(f"Deduplication completed: {len(deduplicated)}/{len(nodes_with_domains)} nodes retained using {stats['method']}")

        return deduplicated

    def _text_similarity(self, text1: str, text2: str) -> float:
        """Calculate simple text similarity."""
        words1 = set(text1.split())
        words2 = set(text2.split())

        if not words1 or not words2:
            return 0.0

        intersection = words1.intersection(words2)
        union = words1.union(words2)

        return len(intersection) / len(union) if union else 0.0

    def _calculate_diversity_score(self, domain_contributions: Dict[DataDomain, int]) -> float:
        """Calculate diversity score based on domain distribution."""
        if not domain_contributions:
            return 0.0

        total_results = sum(domain_contributions.values())
        if total_results == 0:
            return 0.0

        # Calculate entropy-based diversity score
        entropy = 0.0
        for count in domain_contributions.values():
            if count > 0:
                p = count / total_results
                entropy -= p * math.log2(p)

        # Normalize to 0-1 range
        max_entropy = math.log2(len(domain_contributions))
        return entropy / max_entropy if max_entropy > 0 else 0.0

    def _build_strategy_configs(self) -> Dict[FusionStrategy, Dict[str, Any]]:
        """Build configuration for fusion strategies."""
        return {
            FusionStrategy.WEIGHTED_MERGE: {"diversity_weight": 0.3, "confidence_weight": 0.7},
            FusionStrategy.RECIPROCAL_RANK_FUSION: {"k": 60, "diversity_weight": 0.2},
            FusionStrategy.SEMANTIC_FUSION: {"similarity_threshold": 0.8, "diversity_weight": 0.4},
            FusionStrategy.DIVERSITY_OPTIMIZED: {"diversity_weight": 0.8, "relevance_weight": 0.2},
        }

    def _build_domain_weights(self) -> Dict[DataDomain, float]:
        """Build importance weights for different domains."""
        return {
            DataDomain.GITHUB_CODE: 1.0,
            DataDomain.GITHUB_ISSUES: 0.9,
            DataDomain.SLACK_ENGINEERING: 0.8,
            DataDomain.DOCUMENTATION: 0.8,
            DataDomain.SLACK_CONVERSATIONS: 0.7,
            DataDomain.MEETING_NOTES: 0.6,
            DataDomain.GITHUB_WIKI: 0.7,
            DataDomain.SLACK_SUPPORT: 0.8,
        }

    def _build_diversity_params(self) -> Dict[str, Any]:
        """Build diversity optimization parameters."""
        return {
            "min_diversity_score": 0.3,
            "max_domain_dominance": 0.6,
            "diversity_boost_threshold": 0.5,
        }

    def _generate_cache_key(self, domain_results: List[DomainResult], strategy: FusionStrategy, target_count: int) -> str:
        """Generate cache key for fusion results."""
        domain_key = "_".join(sorted([dr.domain.value for dr in domain_results]))
        result_counts = "_".join([str(len(dr.nodes)) for dr in domain_results])
        return f"{self.tenant_slug}_{strategy.value}_{target_count}_{domain_key}_{result_counts}"

    def clear_cache(self) -> None:
        """Clear the fusion cache."""
        self._fusion_cache.clear()
        logger.info("Result fusion cache cleared")

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics for monitoring."""
        return {
            'cache_size': len(self._fusion_cache),
            'tenant_slug': self.tenant_slug,
            'available_strategies': len(FusionStrategy),
            'deduplicator_stats': self.deduplicator.get_stats()
        }
