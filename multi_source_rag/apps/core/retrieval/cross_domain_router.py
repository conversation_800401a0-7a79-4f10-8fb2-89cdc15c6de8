"""
Enhanced Cross-Domain Intelligence Router
========================================

Intelligent routing across multiple data domains (Slack, GitHub, Docs, etc.)
with sophisticated query analysis and domain-specific optimization.

Features:
- LLM-based domain analysis
- Query intent classification for cross-domain needs
- Domain-specific query adaptation
- Intelligent result fusion and ranking
- Performance optimization with caching

Follows SOLID principles and enterprise patterns.
"""

import logging
import re
import threading
from typing import Any, Dict, List, Optional, Set, Tuple
from dataclasses import dataclass, field
from enum import Enum

from apps.core.utils.memory_manager import memory_manager

from apps.core.constants import RAGConstants, LlamaIndexConstants
from apps.core.exceptions import RetrievalError, ValidationError
from apps.core.utils.llama_index_llm import get_llm

logger = logging.getLogger(__name__)


class DataDomain(Enum):
    """Available data domains for cross-domain routing."""
    SLACK_CONVERSATIONS = "slack_conversations"
    SLACK_ENGINEERING = "slack_engineering"
    SLACK_SUPPORT = "slack_support"
    GITHUB_CODE = "github_code"
    GITHUB_ISSUES = "github_issues"
    GITHUB_WIKI = "github_wiki"
    GITHUB_DISCUSSIONS = "github_discussions"
    DOCUMENTATION = "documentation"
    MEETING_NOTES = "meeting_notes"
    CUSTOMER_DOCS = "customer_docs"


@dataclass
class DomainRoutingDecision:
    """
    Represents a cross-domain routing decision with confidence and reasoning.
    """
    primary_domains: List[DataDomain]
    secondary_domains: List[DataDomain]
    confidence: float
    reasoning: str
    query_adaptations: Dict[DataDomain, str] = field(default_factory=dict)
    fusion_strategy: str = "weighted_merge"
    expected_result_distribution: Dict[DataDomain, float] = field(default_factory=dict)


class QueryType(Enum):
    """Types of queries for cross-domain analysis."""
    SINGLE_DOMAIN = "single_domain"          # Query targets one specific domain
    CROSS_DOMAIN = "cross_domain"            # Query needs multiple domains
    WORKFLOW_BASED = "workflow_based"        # Query follows a business workflow
    TROUBLESHOOTING = "troubleshooting"      # Query for problem resolution
    KNOWLEDGE_SYNTHESIS = "knowledge_synthesis"  # Query needs comprehensive knowledge


class CrossDomainRouter:
    """
    Intelligent router for cross-domain query routing and result fusion.

    Analyzes queries to determine which data domains should be searched
    and how results should be combined for optimal user experience.
    """

    def __init__(self, tenant_slug: str):
        """
        Initialize cross-domain router.

        Args:
            tenant_slug: Tenant identifier for configuration
        """
        self.tenant_slug = tenant_slug
        self._llm = None

        # Thread safety lock for cache operations
        self._cache_lock = threading.RLock()

        # Thread-safe cache manager to prevent memory leaks
        from apps.core.utils.memory_manager import CacheConfig
        cache_config = CacheConfig(max_size=500, ttl_seconds=1800)
        self._domain_cache = memory_manager.create_cache(
            f"cross_domain_router_{tenant_slug}",
            cache_config
        )

        # Domain relationship mapping
        self._domain_relationships = self._build_domain_relationships()

        # Query pattern analysis
        self._domain_patterns = self._build_domain_patterns()

        # Workflow patterns for cross-domain queries
        self._workflow_patterns = self._build_workflow_patterns()

    def route_cross_domain_query(self, query: str, intent: str = "default",
                                available_domains: Optional[List[DataDomain]] = None,
                                **kwargs) -> DomainRoutingDecision:
        """
        Route query across multiple domains with intelligent analysis.

        Args:
            query: Search query to analyze
            intent: Query intent for context
            available_domains: Optional list of available domains
            **kwargs: Additional routing parameters

        Returns:
            DomainRoutingDecision with routing strategy

        Raises:
            ValidationError: If query is invalid
            RetrievalError: If routing fails
        """
        try:
            # Input validation
            if not query or not query.strip():
                raise ValidationError("Query cannot be empty for cross-domain routing")

            # Check cache (thread-safe)
            cache_key = self._generate_cache_key(query, intent, available_domains)
            with self._cache_lock:
                if cache_key in self._domain_cache:
                    cached_decision = self._domain_cache[cache_key]
                    logger.info(f"Using cached cross-domain decision for query: {query[:50]}...")
                    return cached_decision

            # Analyze query type
            query_type = self._classify_query_type(query, intent)

            # Route based on query type
            if query_type == QueryType.SINGLE_DOMAIN:
                decision = self._route_single_domain(query, intent, available_domains)
            elif query_type == QueryType.CROSS_DOMAIN:
                decision = self._route_cross_domain(query, intent, available_domains)
            elif query_type == QueryType.WORKFLOW_BASED:
                decision = self._route_workflow_based(query, intent, available_domains)
            elif query_type == QueryType.TROUBLESHOOTING:
                decision = self._route_troubleshooting(query, intent, available_domains)
            else:  # KNOWLEDGE_SYNTHESIS
                decision = self._route_knowledge_synthesis(query, intent, available_domains)

            # Generate query adaptations for each domain
            decision.query_adaptations = self._generate_query_adaptations(query, decision.primary_domains + decision.secondary_domains)

            # Cache the decision (thread-safe)
            with self._cache_lock:
                self._domain_cache[cache_key] = decision

            logger.info(f"Cross-domain routing: {len(decision.primary_domains)} primary, {len(decision.secondary_domains)} secondary domains")
            logger.debug(f"Routing reasoning: {decision.reasoning}")

            return decision

        except Exception as e:
            logger.error(f"Cross-domain routing failed for query '{query}': {e}")
            # Fallback to all available domains
            fallback_domains = available_domains or [DataDomain.SLACK_CONVERSATIONS, DataDomain.GITHUB_CODE, DataDomain.DOCUMENTATION]
            return DomainRoutingDecision(
                primary_domains=fallback_domains[:2],
                secondary_domains=fallback_domains[2:],
                confidence=0.5,
                reasoning=f"Fallback due to routing error: {e}"
            )

    def _classify_query_type(self, query: str, intent: str) -> QueryType:
        """Classify the type of query for appropriate routing strategy."""
        query_lower = query.lower()

        # Single domain indicators
        single_domain_patterns = [
            (r'\b(slack|chat|conversation|message|discussion)\b', QueryType.SINGLE_DOMAIN),
            (r'\b(github|code|repository|commit|pull request|pr)\b', QueryType.SINGLE_DOMAIN),
            (r'\b(documentation|docs|manual|guide)\b', QueryType.SINGLE_DOMAIN),
        ]

        # Cross-domain indicators
        cross_domain_patterns = [
            (r'\b(deployment|release|launch)\b.*\b(discussed|mentioned|planned)\b', QueryType.CROSS_DOMAIN),
            (r'\b(bug|issue|problem)\b.*\b(reported|discussed|fixed)\b', QueryType.CROSS_DOMAIN),
            (r'\b(feature|enhancement)\b.*\b(requested|discussed|implemented)\b', QueryType.CROSS_DOMAIN),
        ]

        # Workflow patterns
        workflow_patterns = [
            (r'\b(how.*process|workflow|procedure|steps)\b', QueryType.WORKFLOW_BASED),
            (r'\b(from.*to|start.*finish|begin.*end)\b', QueryType.WORKFLOW_BASED),
        ]

        # Troubleshooting patterns
        troubleshooting_patterns = [
            (r'\b(error|failure|broken|not working|issue|problem)\b', QueryType.TROUBLESHOOTING),
            (r'\b(debug|fix|solve|resolve|troubleshoot)\b', QueryType.TROUBLESHOOTING),
        ]

        # Check patterns in order of specificity
        for patterns, query_type in [
            (troubleshooting_patterns, QueryType.TROUBLESHOOTING),
            (workflow_patterns, QueryType.WORKFLOW_BASED),
            (cross_domain_patterns, QueryType.CROSS_DOMAIN),
            (single_domain_patterns, QueryType.SINGLE_DOMAIN),
        ]:
            for pattern, qtype in patterns:
                if re.search(pattern, query_lower, re.IGNORECASE):
                    return qtype

        # Default to knowledge synthesis for complex queries
        if len(query.split()) > 10 or intent in ['technical', 'comprehensive']:
            return QueryType.KNOWLEDGE_SYNTHESIS

        return QueryType.CROSS_DOMAIN  # Default to cross-domain for better coverage

    def _route_with_ingestion_metadata(self, query: str, intent: str, available_domains: Optional[List[DataDomain]]) -> Optional[DomainRoutingDecision]:
        """
        CRITICAL FIX: Route using domain metadata from ingestion.

        This method uses the rich domain metadata created during ingestion
        to make intelligent routing decisions based on learned patterns.
        """
        try:
            from apps.documents.models import DocumentDomainMetadata
            from django.db.models import Q, Count, Avg

            # Extract query keywords for metadata matching
            query_keywords = query.lower().split()

            # Find documents with relevant routing metadata
            relevant_docs = DocumentDomainMetadata.objects.filter(
                document__tenant__slug=self.tenant_slug
            )

            # Filter by routing metadata that contains query keywords
            for keyword in query_keywords:
                relevant_docs = relevant_docs.filter(
                    Q(routing_metadata__icontains=keyword) |
                    Q(document__title__icontains=keyword) |
                    Q(document__metadata__icontains=keyword)
                )

            if not relevant_docs.exists():
                return None

            # Aggregate domain patterns from relevant documents
            domain_stats = relevant_docs.values('primary_domain').annotate(
                count=Count('id'),
                avg_confidence=Avg('domain_confidence')
            ).order_by('-count', '-avg_confidence')

            if not domain_stats:
                return None

            # Convert to DataDomain objects and filter by available domains
            primary_domains = []
            secondary_domains = []

            for stat in domain_stats[:4]:  # Top 4 domains
                try:
                    domain = DataDomain(stat['primary_domain'])
                    if available_domains and domain not in available_domains:
                        continue

                    if len(primary_domains) < 2:
                        primary_domains.append(domain)
                    else:
                        secondary_domains.append(domain)

                except ValueError:
                    # Skip invalid domain values
                    continue

            if not primary_domains:
                return None

            # Calculate confidence based on metadata quality
            total_docs = relevant_docs.count()
            top_domain_count = domain_stats[0]['count']
            avg_confidence = domain_stats[0]['avg_confidence']

            # Confidence based on document count and domain confidence
            routing_confidence = min(0.9, (top_domain_count / total_docs) * avg_confidence)

            return DomainRoutingDecision(
                primary_domains=primary_domains,
                secondary_domains=secondary_domains,
                confidence=routing_confidence,
                reasoning=f"Metadata-based routing using {total_docs} relevant documents with {routing_confidence:.2f} confidence"
            )

        except Exception as e:
            logger.warning(f"Metadata-based routing failed: {e}")
            return None

    def _route_single_domain(self, query: str, intent: str, available_domains: Optional[List[DataDomain]]) -> DomainRoutingDecision:
        """Route query to a single primary domain."""

        # CRITICAL FIX: First try routing using ingestion metadata
        metadata_routing = self._route_with_ingestion_metadata(query, intent, available_domains)
        if metadata_routing and metadata_routing.confidence > 0.7:
            # Use only the primary domain for single-domain routing
            return DomainRoutingDecision(
                primary_domains=metadata_routing.primary_domains[:1],
                secondary_domains=[],
                confidence=metadata_routing.confidence,
                reasoning=f"Single domain metadata-based routing: {metadata_routing.reasoning}"
            )

        # Fallback to keyword-based routing
        query_lower = query.lower()

        # Domain-specific keywords
        domain_keywords = {
            DataDomain.SLACK_CONVERSATIONS: ['slack', 'chat', 'conversation', 'message', 'discussion', 'talked', 'said'],
            DataDomain.GITHUB_CODE: ['github', 'code', 'repository', 'commit', 'pull request', 'pr', 'branch'],
            DataDomain.GITHUB_ISSUES: ['issue', 'bug', 'problem', 'ticket', 'reported'],
            DataDomain.DOCUMENTATION: ['documentation', 'docs', 'manual', 'guide', 'readme', 'wiki'],
            DataDomain.MEETING_NOTES: ['meeting', 'notes', 'minutes', 'agenda', 'discussed'],
        }

        # Score each domain
        domain_scores = {}
        for domain, keywords in domain_keywords.items():
            if available_domains and domain not in available_domains:
                continue

            score = sum(1 for keyword in keywords if keyword in query_lower)
            if score > 0:
                domain_scores[domain] = score

        if domain_scores:
            # Select highest scoring domain
            primary_domain = max(domain_scores, key=domain_scores.get)
            confidence = min(0.9, domain_scores[primary_domain] * 0.3)

            return DomainRoutingDecision(
                primary_domains=[primary_domain],
                secondary_domains=[],
                confidence=confidence,
                reasoning=f"Single domain routing to {primary_domain.value} based on keyword matching"
            )

        # Fallback to default domains
        default_domains = available_domains or [DataDomain.SLACK_CONVERSATIONS, DataDomain.GITHUB_CODE]
        return DomainRoutingDecision(
            primary_domains=[default_domains[0]],
            secondary_domains=default_domains[1:2],
            confidence=0.6,
            reasoning="Single domain fallback to default domains"
        )

    def _route_cross_domain(self, query: str, intent: str, available_domains: Optional[List[DataDomain]]) -> DomainRoutingDecision:
        """Route query across multiple related domains."""
        query_lower = query.lower()

        # Cross-domain scenarios
        cross_domain_scenarios = {
            'deployment': [DataDomain.GITHUB_CODE, DataDomain.SLACK_ENGINEERING, DataDomain.DOCUMENTATION],
            'bug_report': [DataDomain.GITHUB_ISSUES, DataDomain.SLACK_SUPPORT, DataDomain.SLACK_CONVERSATIONS],
            'feature_development': [DataDomain.GITHUB_CODE, DataDomain.GITHUB_DISCUSSIONS, DataDomain.SLACK_ENGINEERING],
            'customer_issue': [DataDomain.SLACK_SUPPORT, DataDomain.GITHUB_ISSUES, DataDomain.CUSTOMER_DOCS],
            'release_planning': [DataDomain.GITHUB_CODE, DataDomain.SLACK_ENGINEERING, DataDomain.MEETING_NOTES],
        }

        # Detect scenario
        scenario_keywords = {
            'deployment': ['deploy', 'deployment', 'release', 'launch', 'production'],
            'bug_report': ['bug', 'error', 'issue', 'problem', 'broken', 'failure'],
            'feature_development': ['feature', 'enhancement', 'development', 'implement', 'build'],
            'customer_issue': ['customer', 'user', 'client', 'support', 'help'],
            'release_planning': ['release', 'version', 'milestone', 'planning', 'roadmap'],
        }

        scenario_scores = {}
        for scenario, keywords in scenario_keywords.items():
            score = sum(1 for keyword in keywords if keyword in query_lower)
            if score > 0:
                scenario_scores[scenario] = score

        if scenario_scores:
            # Select best matching scenario
            best_scenario = max(scenario_scores, key=scenario_scores.get)
            domains = cross_domain_scenarios[best_scenario]

            # Filter by available domains
            if available_domains:
                domains = [d for d in domains if d in available_domains]

            primary_domains = domains[:2]
            secondary_domains = domains[2:4]
            confidence = min(0.85, scenario_scores[best_scenario] * 0.25)

            return DomainRoutingDecision(
                primary_domains=primary_domains,
                secondary_domains=secondary_domains,
                confidence=confidence,
                reasoning=f"Cross-domain routing for {best_scenario} scenario",
                expected_result_distribution={
                    primary_domains[0]: 0.4 if len(primary_domains) > 0 else 0,
                    primary_domains[1]: 0.3 if len(primary_domains) > 1 else 0,
                    **{d: 0.15 for d in secondary_domains}
                }
            )

        # Fallback to balanced cross-domain search
        default_domains = available_domains or [DataDomain.SLACK_CONVERSATIONS, DataDomain.GITHUB_CODE, DataDomain.DOCUMENTATION]
        return DomainRoutingDecision(
            primary_domains=default_domains[:2],
            secondary_domains=default_domains[2:3],
            confidence=0.7,
            reasoning="Cross-domain fallback to balanced search"
        )

    def _route_troubleshooting(self, query: str, intent: str, available_domains: Optional[List[DataDomain]]) -> DomainRoutingDecision:
        """Route troubleshooting queries to relevant domains."""
        # Troubleshooting typically needs: Issues + Discussions + Code
        troubleshooting_domains = [
            DataDomain.GITHUB_ISSUES,
            DataDomain.SLACK_SUPPORT,
            DataDomain.SLACK_ENGINEERING,
            DataDomain.GITHUB_CODE,
            DataDomain.DOCUMENTATION
        ]

        if available_domains:
            troubleshooting_domains = [d for d in troubleshooting_domains if d in available_domains]

        return DomainRoutingDecision(
            primary_domains=troubleshooting_domains[:2],
            secondary_domains=troubleshooting_domains[2:4],
            confidence=0.8,
            reasoning="Troubleshooting query routing to issues, support, and code domains",
            fusion_strategy="problem_resolution_merge",
            expected_result_distribution={
                DataDomain.GITHUB_ISSUES: 0.35,
                DataDomain.SLACK_SUPPORT: 0.25,
                DataDomain.SLACK_ENGINEERING: 0.20,
                DataDomain.GITHUB_CODE: 0.20
            }
        )

    def _route_workflow_based(self, query: str, intent: str, available_domains: Optional[List[DataDomain]]) -> DomainRoutingDecision:
        """Route workflow-based queries across process-relevant domains."""
        # Workflow queries need: Documentation + Discussions + Examples
        workflow_domains = [
            DataDomain.DOCUMENTATION,
            DataDomain.MEETING_NOTES,
            DataDomain.SLACK_ENGINEERING,
            DataDomain.GITHUB_WIKI,
            DataDomain.GITHUB_DISCUSSIONS
        ]

        if available_domains:
            workflow_domains = [d for d in workflow_domains if d in available_domains]

        return DomainRoutingDecision(
            primary_domains=workflow_domains[:2],
            secondary_domains=workflow_domains[2:4],
            confidence=0.75,
            reasoning="Workflow-based query routing to documentation and process domains",
            fusion_strategy="process_flow_merge"
        )

    def _route_knowledge_synthesis(self, query: str, intent: str, available_domains: Optional[List[DataDomain]]) -> DomainRoutingDecision:
        """Route comprehensive knowledge synthesis queries."""
        # Knowledge synthesis needs broad coverage
        all_domains = available_domains or list(DataDomain)

        return DomainRoutingDecision(
            primary_domains=all_domains[:3],
            secondary_domains=all_domains[3:6],
            confidence=0.7,
            reasoning="Knowledge synthesis query routing to comprehensive domain coverage",
            fusion_strategy="comprehensive_merge"
        )

    def _generate_query_adaptations(self, original_query: str, domains: List[DataDomain]) -> Dict[DataDomain, str]:
        """Generate domain-specific query adaptations."""
        adaptations = {}

        # Domain-specific query enhancements
        domain_adaptations = {
            DataDomain.SLACK_CONVERSATIONS: f"In conversations or discussions: {original_query}",
            DataDomain.SLACK_ENGINEERING: f"In engineering team discussions: {original_query}",
            DataDomain.SLACK_SUPPORT: f"In support or customer discussions: {original_query}",
            DataDomain.GITHUB_CODE: f"In code, repositories, or commits: {original_query}",
            DataDomain.GITHUB_ISSUES: f"In issues, bugs, or problems: {original_query}",
            DataDomain.GITHUB_WIKI: f"In wiki or documentation: {original_query}",
            DataDomain.DOCUMENTATION: f"In documentation or guides: {original_query}",
            DataDomain.MEETING_NOTES: f"In meeting notes or minutes: {original_query}",
        }

        for domain in domains:
            adaptations[domain] = domain_adaptations.get(domain, original_query)

        return adaptations

    def _build_domain_relationships(self) -> Dict[DataDomain, List[DataDomain]]:
        """Build relationships between domains for intelligent routing."""
        return {
            DataDomain.GITHUB_CODE: [DataDomain.GITHUB_ISSUES, DataDomain.SLACK_ENGINEERING],
            DataDomain.GITHUB_ISSUES: [DataDomain.SLACK_SUPPORT, DataDomain.GITHUB_CODE],
            DataDomain.SLACK_ENGINEERING: [DataDomain.GITHUB_CODE, DataDomain.MEETING_NOTES],
            DataDomain.SLACK_SUPPORT: [DataDomain.GITHUB_ISSUES, DataDomain.CUSTOMER_DOCS],
            DataDomain.DOCUMENTATION: [DataDomain.GITHUB_WIKI, DataDomain.MEETING_NOTES],
        }

    def _build_domain_patterns(self) -> Dict[DataDomain, List[str]]:
        """Build domain-specific patterns for routing."""
        return {
            DataDomain.SLACK_CONVERSATIONS: [r'\b(chat|conversation|discussed|mentioned|said)\b'],
            DataDomain.GITHUB_CODE: [r'\b(code|repository|commit|branch|pull request)\b'],
            DataDomain.GITHUB_ISSUES: [r'\b(issue|bug|problem|error|failure)\b'],
            DataDomain.DOCUMENTATION: [r'\b(documentation|guide|manual|readme)\b'],
        }

    def _build_workflow_patterns(self) -> Dict[str, List[DataDomain]]:
        """Build workflow patterns for cross-domain routing."""
        return {
            'development_workflow': [DataDomain.GITHUB_CODE, DataDomain.SLACK_ENGINEERING, DataDomain.GITHUB_ISSUES],
            'support_workflow': [DataDomain.SLACK_SUPPORT, DataDomain.GITHUB_ISSUES, DataDomain.CUSTOMER_DOCS],
            'release_workflow': [DataDomain.GITHUB_CODE, DataDomain.MEETING_NOTES, DataDomain.SLACK_ENGINEERING],
        }

    def _generate_cache_key(self, query: str, intent: str, available_domains: Optional[List[DataDomain]]) -> str:
        """Generate cache key for domain routing decisions."""
        domain_key = "_".join(sorted([d.value for d in available_domains])) if available_domains else "all"
        query_key = query[:100].lower().strip()
        return f"{self.tenant_slug}_{intent}_{domain_key}_{hash(query_key)}"

    def clear_cache(self) -> None:
        """Clear the domain routing cache (thread-safe)."""
        with self._cache_lock:
            self._domain_cache.clear()
        logger.info("Cross-domain routing cache cleared")

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics for monitoring (thread-safe)."""
        with self._cache_lock:
            cache_size = len(self._domain_cache)
            cache_info = getattr(self._domain_cache, 'currsize', cache_size)

        return {
            'cache_size': cache_size,
            'cache_current_size': cache_info,
            'cache_max_size': getattr(self._domain_cache, 'maxsize', 500),
            'cache_ttl': getattr(self._domain_cache, 'ttl', 1800),
            'tenant_slug': self.tenant_slug,
            'available_domains': len(DataDomain)
        }
