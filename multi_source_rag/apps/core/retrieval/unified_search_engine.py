"""
Unified Agentic Search Engine
============================

The master search engine that orchestrates all retrieval strategies,
cross-domain intelligence, and advanced routing into a single,
intelligent interface.

Features:
- Unified interface for all search capabilities
- Automatic strategy orchestration
- Performance monitoring and optimization
- UI-configurable sophistication levels
- Comprehensive error handling and fallbacks
- Real-time adaptation based on results

Follows SOLID principles and enterprise patterns.
"""

import logging
import threading
from typing import Any, Dict, List, Optional, Union
from dataclasses import dataclass, field
from datetime import datetime
import asyncio
from collections import deque

from llama_index.core.schema import NodeWithScore

from apps.core.constants import RAGConstants, LlamaIndexConstants
from apps.core.exceptions import RetrievalError, ValidationError
from apps.core.retrieval import (
    RetrievalMode, SophisticationLevel, RetrievalStrategy,
    ChunkRetrievalStrategy, FileContentRetrievalStrategy, FileMetadataRetrievalStrategy,
    DataDomain, FusionStrategy, DomainResult
)
from apps.core.retrieval.strategy_orchestrator import (
    AdvancedStrategyOrchestrator, StrategyDecision, StrategyComplexity
)

logger = logging.getLogger(__name__)


@dataclass
class SearchExecution:
    """
    Represents a complete search execution with all metadata.
    """
    # Input parameters
    query: str
    intent: str
    sophistication: SophisticationLevel
    user_context: Optional[Dict[str, Any]] = None

    # Strategy decision
    strategy_decision: Optional[StrategyDecision] = None

    # Execution results
    results: List[NodeWithScore] = field(default_factory=list)
    domain_results: List[DomainResult] = field(default_factory=list)

    # Performance metrics
    execution_time_ms: int = 0
    strategy_confidence: float = 0.0
    result_quality_score: float = 0.0

    # Metadata
    execution_metadata: Dict[str, Any] = field(default_factory=dict)
    fallback_used: bool = False
    error_details: Optional[str] = None


class ResultQualityAnalyzer:
    """
    Analyzes result quality to enable adaptive improvements.
    """

    def __init__(self, tenant_slug: str):
        """Initialize result quality analyzer."""
        self.tenant_slug = tenant_slug

    def analyze_result_quality(self, execution: SearchExecution) -> float:
        """
        Analyze the quality of search results.

        Args:
            execution: Search execution to analyze

        Returns:
            Quality score between 0.0 and 1.0
        """
        try:
            quality_factors = []

            # Factor 1: Result count appropriateness
            result_count = len(execution.results)
            if result_count == 0:
                quality_factors.append(0.0)
            elif result_count < 3:
                quality_factors.append(0.6)
            elif result_count <= 10:
                quality_factors.append(1.0)
            else:
                quality_factors.append(0.8)  # Too many results might indicate poor filtering

            # Factor 2: Score distribution
            if execution.results:
                scores = [node.score for node in execution.results if node.score is not None]
                if scores:
                    avg_score = sum(scores) / len(scores)
                    score_variance = sum((s - avg_score) ** 2 for s in scores) / len(scores)

                    # Good results have high average scores and reasonable variance
                    quality_factors.append(min(avg_score * 2, 1.0))  # Scale 0.5 avg to 1.0
                    quality_factors.append(max(0.0, 1.0 - score_variance))  # Lower variance is better
                else:
                    quality_factors.extend([0.5, 0.5])
            else:
                quality_factors.extend([0.0, 0.0])

            # Factor 3: Domain diversity (if cross-domain search)
            if len(execution.domain_results) > 1:
                domain_counts = [len(dr.nodes) for dr in execution.domain_results]
                if domain_counts:
                    # Good diversity means results from multiple domains
                    non_empty_domains = sum(1 for count in domain_counts if count > 0)
                    diversity_score = non_empty_domains / len(execution.domain_results)
                    quality_factors.append(diversity_score)
                else:
                    quality_factors.append(0.0)
            else:
                quality_factors.append(0.8)  # Single domain is fine for simple queries

            # Factor 4: Strategy confidence alignment
            if execution.strategy_decision:
                confidence_factor = execution.strategy_decision.confidence
                quality_factors.append(confidence_factor)
            else:
                quality_factors.append(0.5)

            # Factor 5: Execution performance
            if execution.execution_time_ms > 0:
                # Penalize very slow executions
                if execution.execution_time_ms < 1000:  # Under 1 second
                    performance_factor = 1.0
                elif execution.execution_time_ms < 5000:  # Under 5 seconds
                    performance_factor = 0.8
                elif execution.execution_time_ms < 10000:  # Under 10 seconds
                    performance_factor = 0.6
                else:
                    performance_factor = 0.4
                quality_factors.append(performance_factor)
            else:
                quality_factors.append(0.7)

            # Calculate overall quality score
            overall_quality = sum(quality_factors) / len(quality_factors)

            logger.debug(f"Result quality analysis: {overall_quality:.2f} from factors {quality_factors}")
            return overall_quality

        except Exception as e:
            logger.error(f"Result quality analysis failed: {e}")
            return 0.5  # Default moderate quality


class UnifiedAgenticSearchEngine:
    """
    The master search engine that unifies all retrieval capabilities.

    This is the single entry point for all search operations, providing:
    - Automatic strategy orchestration
    - Cross-domain intelligence
    - Adaptive complexity management
    - Performance optimization
    - Quality monitoring
    """

    def __init__(self, tenant_slug: str):
        """
        Initialize unified agentic search engine.

        Args:
            tenant_slug: Tenant identifier for configuration
        """
        self.tenant_slug = tenant_slug

        # Initialize orchestrator and quality analyzer
        self.orchestrator = AdvancedStrategyOrchestrator(tenant_slug)
        self.quality_analyzer = ResultQualityAnalyzer(tenant_slug)

        # Strategy implementations
        self._strategy_implementations = {}
        self._initialize_strategy_implementations()

        # Thread-safe execution history for learning
        self._execution_history = deque(maxlen=1000)
        self._history_lock = threading.Lock()

        # Performance monitoring
        self._performance_metrics = {
            'total_searches': 0,
            'avg_execution_time_ms': 0,
            'avg_quality_score': 0.0,
            'strategy_success_rates': {},
            'complexity_performance': {}
        }

    def search(self, query: str, intent: str = "default",
               sophistication: Optional[SophisticationLevel] = None,
               user_context: Optional[Dict[str, Any]] = None,
               **kwargs) -> SearchExecution:
        """
        Perform unified agentic search with automatic strategy orchestration.

        Args:
            query: Search query
            intent: Query intent for context
            sophistication: Optional sophistication level override
            user_context: Optional user context for personalization
            **kwargs: Additional search parameters

        Returns:
            SearchExecution with comprehensive results and metadata

        Raises:
            ValidationError: If input validation fails
            RetrievalError: If search execution fails
        """
        start_time = datetime.now()

        try:
            # Input validation
            if not query or not query.strip():
                raise ValidationError("Query cannot be empty for unified search")

            # Create search execution object
            execution = SearchExecution(
                query=query,
                intent=intent,
                sophistication=sophistication or SophisticationLevel.STANDARD,
                user_context=user_context
            )

            # Step 1: Orchestrate optimal strategy
            strategy_decision = self.orchestrator.orchestrate_strategy(
                query=query,
                intent=intent,
                sophistication=sophistication,
                user_context=user_context,
                **kwargs
            )
            execution.strategy_decision = strategy_decision

            # Step 2: Execute strategy with fallback handling
            execution = self._execute_strategy_with_fallbacks(execution, **kwargs)

            # Step 3: Analyze result quality
            execution.result_quality_score = self.quality_analyzer.analyze_result_quality(execution)

            # Step 4: Adaptive improvement if quality is low
            if execution.result_quality_score < 0.6 and not execution.fallback_used:
                logger.info(f"Low quality results ({execution.result_quality_score:.2f}), attempting adaptive improvement")
                execution = self._attempt_adaptive_improvement(execution, **kwargs)

            # Step 5: Record execution metrics
            execution.execution_time_ms = int((datetime.now() - start_time).total_seconds() * 1000)
            execution.strategy_confidence = strategy_decision.confidence

            # Step 6: Update learning and performance metrics
            self._update_learning_metrics(execution)

            logger.info(f"Unified search completed: {len(execution.results)} results, "
                       f"quality {execution.result_quality_score:.2f}, "
                       f"time {execution.execution_time_ms}ms")

            return execution

        except Exception as e:
            logger.error(f"Unified search failed for query '{query}': {e}")
            # Create error execution
            execution = SearchExecution(
                query=query,
                intent=intent,
                sophistication=sophistication or SophisticationLevel.STANDARD,
                user_context=user_context,
                error_details=str(e),
                execution_time_ms=int((datetime.now() - start_time).total_seconds() * 1000)
            )
            return execution

    def _execute_strategy_with_fallbacks(self, execution: SearchExecution, **kwargs) -> SearchExecution:
        """Execute strategy with automatic fallback handling."""
        strategy_decision = execution.strategy_decision

        # Try primary strategy
        try:
            results = self._execute_single_strategy(strategy_decision, execution.query, **kwargs)
            execution.results = results
            execution.execution_metadata['strategy_used'] = 'primary'
            return execution

        except Exception as e:
            logger.warning(f"Primary strategy failed: {e}")

            # Try fallback strategies
            for i, fallback_strategy in enumerate(strategy_decision.fallback_strategies):
                try:
                    logger.info(f"Attempting fallback strategy {i+1}")
                    results = self._execute_single_strategy(fallback_strategy, execution.query, **kwargs)
                    execution.results = results
                    execution.fallback_used = True
                    execution.execution_metadata['strategy_used'] = f'fallback_{i+1}'
                    execution.execution_metadata['fallback_reason'] = str(e)
                    return execution

                except Exception as fallback_error:
                    logger.warning(f"Fallback strategy {i+1} failed: {fallback_error}")
                    continue

            # All strategies failed
            execution.error_details = f"All strategies failed. Primary: {e}"
            return execution

    def _execute_single_strategy(self, strategy_decision: StrategyDecision,
                                query: str, **kwargs) -> List[NodeWithScore]:
        """Execute a single strategy decision."""
        # Determine if this is a cross-domain search
        if len(strategy_decision.domains) > 1:
            return self._execute_cross_domain_strategy(strategy_decision, query, **kwargs)
        else:
            return self._execute_single_domain_strategy(strategy_decision, query, **kwargs)

    def _execute_cross_domain_strategy(self, strategy_decision: StrategyDecision,
                                     query: str, **kwargs) -> List[NodeWithScore]:
        """Execute cross-domain strategy."""
        # Use cross-domain router and fusion engine
        domain_routing = self.orchestrator.cross_domain_router.route_cross_domain_query(
            query=query,
            intent=kwargs.get('intent', 'default'),
            available_domains=strategy_decision.domains
        )

        # Enhance query for each domain
        query_enhancements = self.orchestrator.query_enhancer.enhance_queries_for_multiple_domains(
            query, strategy_decision.domains
        )

        # Search each domain (with parallel execution for performance)
        try:
            domain_results = self._execute_domains_with_proper_async_handling(
                strategy_decision, query_enhancements, **kwargs
            )
        except Exception as e:
            logger.warning(f"Parallel domain search failed, falling back to sequential: {e}")
            domain_results = self._execute_domains_sequential(
                strategy_decision, query_enhancements, **kwargs
            )

        # Fuse results
        if domain_results:
            fused_results = self.orchestrator.fusion_engine.fuse_domain_results(
                domain_results=domain_results,
                strategy=strategy_decision.fusion_strategy,
                target_count=kwargs.get('top_k', 10)
            )
            return fused_results.fused_nodes
        else:
            return []

    def _execute_domains_with_proper_async_handling(self, strategy_decision: StrategyDecision,
                                                   query_enhancements: Dict, **kwargs) -> List:
        """
        Execute domain searches with proper async/sync handling.

        This method properly handles async execution in sync context to avoid
        "RuntimeError: cannot be called from a running loop" errors.
        """
        import concurrent.futures

        try:
            # Check if we're already in an async context
            try:
                loop = asyncio.get_running_loop()
                # We're in an async context, use thread pool
                with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
                    future = executor.submit(
                        self._run_async_in_thread,
                        strategy_decision, query_enhancements, **kwargs
                    )
                    return future.result(timeout=30)
            except RuntimeError:
                # No running loop, we can use asyncio.run
                return asyncio.run(
                    self._execute_domains_parallel(strategy_decision, query_enhancements, **kwargs)
                )
        except Exception as e:
            logger.warning(f"Async execution failed: {e}, falling back to sequential")
            return self._execute_domains_sequential(strategy_decision, query_enhancements, **kwargs)

    def _run_async_in_thread(self, strategy_decision: StrategyDecision,
                           query_enhancements: Dict, **kwargs) -> List:
        """Run async function in a new thread with its own event loop."""
        return asyncio.run(
            self._execute_domains_parallel(strategy_decision, query_enhancements, **kwargs)
        )

    async def _execute_domains_parallel(self, strategy_decision: StrategyDecision,
                                       query_enhancements: Dict, **kwargs) -> List:
        """Execute domain searches in parallel for better performance."""
        # Create async tasks for each domain
        tasks = []
        for domain in strategy_decision.domains:
            enhanced_query = query_enhancements[domain].enhanced_query
            confidence = query_enhancements[domain].confidence

            task = self._search_domain_async(
                strategy_decision.retrieval_mode,
                domain,
                enhanced_query,
                confidence,
                **kwargs
            )
            tasks.append(task)

        # Execute all domain searches in parallel
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Process results and filter out exceptions
        domain_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                domain = strategy_decision.domains[i]
                logger.warning(f"Async domain search failed for {domain.value}: {result}")
            elif result:
                domain_results.append(result)

        return domain_results

    async def _search_domain_async(self, retrieval_mode: RetrievalMode, domain: DataDomain,
                                  enhanced_query: str, confidence: float, **kwargs):
        """Async wrapper for domain search."""
        try:
            # Run domain search in thread pool to avoid blocking
            loop = asyncio.get_event_loop()

            # Get strategy implementation
            strategy_impl = self._get_strategy_implementation(retrieval_mode, domain)

            # Execute search in thread pool
            nodes = await loop.run_in_executor(
                None,
                strategy_impl.retrieve,
                enhanced_query,
                kwargs.get('top_k', 10),
                **{k: v for k, v in kwargs.items() if k != 'top_k'}
            )

            if nodes:
                return DomainResult(
                    domain=domain,
                    nodes=nodes,
                    query_used=enhanced_query,
                    confidence=confidence
                )
        except Exception as e:
            logger.warning(f"Async domain search failed for {domain.value}: {e}")
            return None

    def _execute_domains_sequential(self, strategy_decision: StrategyDecision,
                                   query_enhancements: Dict, **kwargs) -> List:
        """Fallback sequential execution for domain searches."""
        domain_results = []

        for domain in strategy_decision.domains:
            try:
                enhanced_query = query_enhancements[domain].enhanced_query

                # Get strategy implementation
                strategy_impl = self._get_strategy_implementation(
                    strategy_decision.retrieval_mode, domain
                )

                # Execute search
                nodes = strategy_impl.retrieve(
                    enhanced_query,
                    top_k=kwargs.get('top_k', 10),
                    **kwargs
                )

                domain_result = DomainResult(
                    domain=domain,
                    nodes=nodes,
                    query_used=enhanced_query,
                    confidence=query_enhancements[domain].confidence
                )
                domain_results.append(domain_result)

            except Exception as e:
                logger.warning(f"Domain search failed for {domain.value}: {e}")
                continue

        return domain_results

    def _execute_single_domain_strategy(self, strategy_decision: StrategyDecision,
                                      query: str, **kwargs) -> List[NodeWithScore]:
        """Execute single domain strategy."""
        domain = strategy_decision.domains[0] if strategy_decision.domains else DataDomain.GITHUB_CODE

        # Get strategy implementation
        strategy_impl = self._get_strategy_implementation(
            strategy_decision.retrieval_mode, domain
        )

        # Execute search
        return strategy_impl.retrieve(
            query,
            top_k=kwargs.get('top_k', 10),
            **kwargs
        )

    def _get_strategy_implementation(self, mode: RetrievalMode,
                                   domain: DataDomain) -> RetrievalStrategy:
        """Get strategy implementation for mode and domain."""
        # Map domain to intent for strategy creation
        domain_intent_mapping = {
            DataDomain.GITHUB_CODE: 'code',
            DataDomain.GITHUB_ISSUES: 'technical',
            DataDomain.SLACK_CONVERSATIONS: 'conversational',
            DataDomain.SLACK_ENGINEERING: 'technical',
            DataDomain.DOCUMENTATION: 'document',
            DataDomain.MEETING_NOTES: 'document',
        }

        intent = domain_intent_mapping.get(domain, 'default')
        strategy_key = f"{mode.value}_{intent}"

        if strategy_key not in self._strategy_implementations:
            # Create strategy implementation
            if mode == RetrievalMode.CHUNKS:
                strategy = ChunkRetrievalStrategy(self.tenant_slug, intent)
            elif mode == RetrievalMode.FILES_VIA_CONTENT:
                strategy = FileContentRetrievalStrategy(self.tenant_slug, intent)
            elif mode == RetrievalMode.FILES_VIA_METADATA:
                strategy = FileMetadataRetrievalStrategy(self.tenant_slug, intent)
            else:
                strategy = ChunkRetrievalStrategy(self.tenant_slug, intent)

            self._strategy_implementations[strategy_key] = strategy

        return self._strategy_implementations[strategy_key]

    def _attempt_adaptive_improvement(self, execution: SearchExecution, **kwargs) -> SearchExecution:
        """Attempt to improve results adaptively."""
        try:
            logger.info("Attempting adaptive improvement with higher sophistication")

            # Try with higher sophistication level
            higher_sophistication = self._get_higher_sophistication(execution.sophistication)
            if higher_sophistication != execution.sophistication:
                improved_strategy = self.orchestrator.orchestrate_strategy(
                    query=execution.query,
                    intent=execution.intent,
                    sophistication=higher_sophistication,
                    user_context=execution.user_context,
                    **kwargs
                )

                improved_results = self._execute_single_strategy(
                    improved_strategy, execution.query, **kwargs
                )

                if len(improved_results) > len(execution.results):
                    execution.results = improved_results
                    execution.strategy_decision = improved_strategy
                    execution.execution_metadata['adaptive_improvement'] = 'higher_sophistication'
                    logger.info("Adaptive improvement successful with higher sophistication")

            return execution

        except Exception as e:
            logger.warning(f"Adaptive improvement failed: {e}")
            return execution

    def _get_higher_sophistication(self, current: SophisticationLevel) -> SophisticationLevel:
        """Get next higher sophistication level."""
        sophistication_order = [
            SophisticationLevel.BASIC,
            SophisticationLevel.STANDARD,
            SophisticationLevel.ADVANCED,
            SophisticationLevel.EXPERT
        ]

        try:
            current_index = sophistication_order.index(current)
            if current_index < len(sophistication_order) - 1:
                return sophistication_order[current_index + 1]
        except ValueError:
            pass

        return current  # Return current if can't find higher

    def _initialize_strategy_implementations(self) -> None:
        """Initialize strategy implementation cache."""
        # Pre-create common strategy implementations
        common_strategies = [
            (RetrievalMode.CHUNKS, 'default'),
            (RetrievalMode.FILES_VIA_CONTENT, 'code'),
            (RetrievalMode.FILES_VIA_METADATA, 'document'),
        ]

        for mode, intent in common_strategies:
            try:
                if mode == RetrievalMode.CHUNKS:
                    strategy = ChunkRetrievalStrategy(self.tenant_slug, intent)
                elif mode == RetrievalMode.FILES_VIA_CONTENT:
                    strategy = FileContentRetrievalStrategy(self.tenant_slug, intent)
                elif mode == RetrievalMode.FILES_VIA_METADATA:
                    strategy = FileMetadataRetrievalStrategy(self.tenant_slug, intent)
                else:
                    continue

                strategy_key = f"{mode.value}_{intent}"
                self._strategy_implementations[strategy_key] = strategy

            except Exception as e:
                logger.warning(f"Failed to initialize strategy {mode.value}_{intent}: {e}")

    def _update_learning_metrics(self, execution: SearchExecution) -> None:
        """Update learning and performance metrics (thread-safe)."""
        # Add to execution history (thread-safe with deque maxlen)
        with self._history_lock:
            self._execution_history.append(execution)

        # Update performance metrics
        self._performance_metrics['total_searches'] += 1

        # Update average execution time
        total_time = (self._performance_metrics['avg_execution_time_ms'] *
                     (self._performance_metrics['total_searches'] - 1) +
                     execution.execution_time_ms)
        self._performance_metrics['avg_execution_time_ms'] = total_time / self._performance_metrics['total_searches']

        # Update average quality score
        total_quality = (self._performance_metrics['avg_quality_score'] *
                        (self._performance_metrics['total_searches'] - 1) +
                        execution.result_quality_score)
        self._performance_metrics['avg_quality_score'] = total_quality / self._performance_metrics['total_searches']

        # Update strategy success rates
        if execution.strategy_decision:
            strategy_key = f"{execution.strategy_decision.retrieval_mode.value}_{execution.strategy_decision.complexity_level.value}"
            if strategy_key not in self._performance_metrics['strategy_success_rates']:
                self._performance_metrics['strategy_success_rates'][strategy_key] = {'total': 0, 'successful': 0}

            self._performance_metrics['strategy_success_rates'][strategy_key]['total'] += 1
            if execution.result_quality_score > 0.6:  # Consider successful if quality > 0.6
                self._performance_metrics['strategy_success_rates'][strategy_key]['successful'] += 1

    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get comprehensive performance metrics."""
        metrics = self._performance_metrics.copy()

        # Add orchestrator metrics
        metrics['orchestrator_stats'] = self.orchestrator.get_performance_stats()

        # Calculate success rates
        for strategy_key, stats in metrics['strategy_success_rates'].items():
            if stats['total'] > 0:
                stats['success_rate'] = stats['successful'] / stats['total']
            else:
                stats['success_rate'] = 0.0

        return metrics

    def cleanup_resources(self) -> None:
        """Clean up resources and caches."""
        self.orchestrator.cleanup_resources()
        self._strategy_implementations.clear()
        logger.info("Unified search engine resources cleaned up")
