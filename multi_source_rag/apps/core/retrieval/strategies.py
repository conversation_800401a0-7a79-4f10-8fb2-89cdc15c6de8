"""
File-Level Retrieval Strategies for Agentic RAG
==============================================

Implements different retrieval strategies following the Strategy pattern:
- Chunk-based retrieval (existing behavior)
- File-level content retrieval
- Metadata-based file retrieval
- Hybrid multi-level retrieval

Follows SOLID principles:
- Single Responsibility: Each strategy handles one retrieval approach
- Open/Closed: Easy to add new strategies without modifying existing code
- Liskov Substitution: All strategies are interchangeable
- Interface Segregation: Clean, focused interfaces
- Dependency Inversion: Depends on abstractions, not concretions
"""

import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Tuple
from enum import Enum

from llama_index.core.schema import NodeWithScore, QueryBundle
from llama_index.core.retrievers import VectorIndexRetriever
from llama_index.core.indices import VectorStoreIndex
from llama_index.core.postprocessor import SimilarityPostprocessor, MetadataReplacementPostProcessor

from apps.core.constants import RAGConstants, LlamaIndexConstants
from apps.core.exceptions import RetrievalError
from apps.core.utils.llama_index_embeddings import get_embedding_model_for_content
from apps.core.utils.llama_index_vectorstore import get_vector_store
from apps.core.utils.collection_manager import get_collection_name

logger = logging.getLogger(__name__)


class RetrievalMode(Enum):
    """Enumeration of available retrieval modes."""
    CHUNKS = "chunks"                    # Standard chunk-based retrieval
    FILES_VIA_CONTENT = "files_via_content"    # Retrieve full files by content similarity
    FILES_VIA_METADATA = "files_via_metadata"  # Retrieve files by filename/path matching
    HYBRID_MULTI_LEVEL = "hybrid_multi_level"  # Combine multiple strategies


class RetrievalStrategy(ABC):
    """
    Abstract base class for retrieval strategies.

    Implements the Strategy pattern for different retrieval approaches.
    Each strategy encapsulates a specific algorithm for document retrieval.
    """

    def __init__(self, tenant_slug: str, intent: str = "default"):
        """
        Initialize retrieval strategy.

        Args:
            tenant_slug: Tenant identifier for collection access
            intent: Query intent for specialized configuration
        """
        self.tenant_slug = tenant_slug
        self.intent = intent
        self._index = None
        self._retriever = None

    @abstractmethod
    def retrieve(self, query: str, top_k: int = 10, **kwargs) -> List[NodeWithScore]:
        """
        Retrieve documents using this strategy.

        Args:
            query: Search query
            top_k: Number of results to return
            **kwargs: Strategy-specific parameters

        Returns:
            List of nodes with scores

        Raises:
            RetrievalError: If retrieval fails
        """
        pass

    @abstractmethod
    def get_strategy_name(self) -> str:
        """Get human-readable strategy name."""
        pass

    @abstractmethod
    def get_strategy_description(self) -> str:
        """Get strategy description for UI/logging."""
        pass

    def _get_or_create_index(self, collection_name: str = None) -> VectorStoreIndex:
        """
        Get or create vector index for this strategy.

        Args:
            collection_name: Optional specific collection name

        Returns:
            VectorStoreIndex instance
        """
        if self._index is None:
            try:
                if not collection_name:
                    collection_name = get_collection_name(
                        self.tenant_slug,
                        self.intent,
                        check_existence=True
                    )

                vector_store = get_vector_store(collection_name=collection_name)
                embed_model = get_embedding_model_for_content(self.intent)

                self._index = VectorStoreIndex.from_vector_store(
                    vector_store=vector_store,
                    embed_model=embed_model
                )

                logger.info(f"Created index for strategy {self.get_strategy_name()} with collection {collection_name}")

            except Exception as e:
                logger.error(f"Failed to create index for strategy {self.get_strategy_name()}: {e}")
                raise RetrievalError(
                    f"Index creation failed for {self.get_strategy_name()}",
                    collection_name=collection_name,
                    query=None
                )

        return self._index


class ChunkRetrievalStrategy(RetrievalStrategy):
    """
    Standard chunk-based retrieval strategy.

    This is the existing behavior - retrieves document chunks based on
    semantic similarity to the query.
    """

    def retrieve(self, query: str, top_k: int = 10, **kwargs) -> List[NodeWithScore]:
        """Retrieve document chunks using vector similarity."""
        try:
            index = self._get_or_create_index()

            # Create vector retriever with standard configuration
            retriever = VectorIndexRetriever(
                index=index,
                similarity_top_k=top_k,
                embed_model=get_embedding_model_for_content(self.intent)
            )

            # Create query bundle
            query_bundle = QueryBundle(query_str=query)

            # Retrieve nodes
            nodes = retriever.retrieve(query_bundle)

            logger.info(f"Chunk retrieval completed: {len(nodes)} chunks retrieved")
            return nodes

        except Exception as e:
            logger.error(f"Chunk retrieval failed for query '{query}': {e}")
            raise RetrievalError(
                f"Chunk retrieval failed: {e}",
                collection_name=None,
                query=query
            )

    def get_strategy_name(self) -> str:
        return "Chunk-based Retrieval"

    def get_strategy_description(self) -> str:
        return "Retrieves relevant document chunks using semantic similarity"


class FileContentRetrievalStrategy(RetrievalStrategy):
    """
    File-level content retrieval strategy.

    Retrieves entire files based on content similarity, useful for
    comprehensive analysis or when the query requires full document context.
    """

    def retrieve(self, query: str, top_k: int = 10, **kwargs) -> List[NodeWithScore]:
        """Retrieve full files based on content similarity."""
        try:
            # CRITICAL FIX: First try file-level embeddings if available
            file_level_results = self._retrieve_by_file_embeddings(query, top_k)
            if file_level_results:
                logger.info(f"File-level embedding retrieval completed: {len(file_level_results)} files retrieved")
                return file_level_results

            # Fallback to chunk-based file retrieval
            index = self._get_or_create_index()

            # Create retriever with file-level focus
            retriever = VectorIndexRetriever(
                index=index,
                similarity_top_k=top_k * 2,  # Get more candidates for file-level filtering
                embed_model=get_embedding_model_for_content(self.intent)
            )

            # Create query bundle
            query_bundle = QueryBundle(query_str=query)

            # Retrieve nodes
            nodes = retriever.retrieve(query_bundle)

            # Group by file and select best representative chunks per file
            file_nodes = self._group_by_file_and_select_best(nodes, top_k)

            # Apply metadata replacement to get full file content where possible
            post_processor = MetadataReplacementPostProcessor(
                target_metadata_key="window"
            )

            file_nodes = post_processor.postprocess_nodes(file_nodes, query_bundle)

            logger.info(f"File content retrieval completed: {len(file_nodes)} files retrieved")
            return file_nodes

        except Exception as e:
            logger.error(f"File content retrieval failed for query '{query}': {e}")
            raise RetrievalError(
                f"File content retrieval failed: {e}",
                collection_name=None,
                query=query
            )

    def _retrieve_by_file_embeddings(self, query: str, top_k: int) -> List[NodeWithScore]:
        """
        CRITICAL FIX: Use file-level embeddings for direct file retrieval.

        This method leverages the file_level_embedding_id stored in DocumentContent
        to perform direct file-level retrieval using dedicated file embeddings.
        """
        try:
            from apps.documents.models import DocumentContent
            from apps.core.utils.vectorstore import search_vector_store
            from llama_index.core.schema import TextNode

            # Get documents with file-level embeddings
            file_contents = DocumentContent.objects.filter(
                document__tenant__slug=self.tenant_slug,
                file_level_embedding_id__isnull=False
            ).select_related('document')

            if not file_contents.exists():
                logger.debug("No file-level embeddings found")
                return []

            # Extract file-level embedding IDs
            file_embedding_ids = [fc.file_level_embedding_id for fc in file_contents]

            # Determine collection name for file-level embeddings
            collection_name = f"{self.tenant_slug}_file_level"

            # Search vector store specifically for file-level embeddings
            try:
                search_results = search_vector_store(
                    query=query,
                    collection_name=collection_name,
                    top_k=top_k,
                    filter_criteria={'embedding_type': 'file_level'}
                )

                if not search_results:
                    logger.debug("No file-level search results found")
                    return []

                # Convert search results to NodeWithScore objects
                file_nodes = []
                for result in search_results:
                    try:
                        # Find corresponding DocumentContent
                        doc_content = file_contents.filter(
                            file_level_embedding_id=result.get('id')
                        ).first()

                        if doc_content:
                            # Create TextNode with full file content
                            node = TextNode(
                                text=self._reconstruct_file_content(doc_content),
                                metadata={
                                    'document_id': doc_content.document.id,
                                    'document_title': doc_content.document.title,
                                    'file_summary': doc_content.file_summary,
                                    'file_keywords': doc_content.file_keywords,
                                    'embedding_type': 'file_level',
                                    'retrieval_strategy': 'file_level_embedding'
                                }
                            )

                            # Create NodeWithScore
                            node_with_score = NodeWithScore(
                                node=node,
                                score=result.get('score', 0.0)
                            )
                            file_nodes.append(node_with_score)

                    except Exception as e:
                        logger.warning(f"Error processing file-level result: {e}")
                        continue

                logger.info(f"Retrieved {len(file_nodes)} files using file-level embeddings")
                return file_nodes

            except Exception as e:
                logger.warning(f"File-level vector search failed: {e}")
                return []

        except Exception as e:
            logger.error(f"File-level embedding retrieval failed: {e}")
            return []

    def _reconstruct_file_content(self, doc_content) -> str:
        """Reconstruct full file content from DocumentContent and chunks."""
        try:
            # Start with file summary if available
            content_parts = []

            if doc_content.file_summary:
                content_parts.append(f"File Summary: {doc_content.file_summary}")

            # Add all chunks in order
            chunks = doc_content.document.chunks.all().order_by('chunk_index')
            for chunk in chunks:
                content_parts.append(chunk.text)

            return "\n\n".join(content_parts)

        except Exception as e:
            logger.error(f"Error reconstructing file content: {e}")
            return doc_content.content or ""

    def _group_by_file_and_select_best(self, nodes: List[NodeWithScore], top_k: int) -> List[NodeWithScore]:
        """Group nodes by file and select the best representative chunk per file."""
        file_groups = {}

        for node in nodes:
            # Extract file identifier from metadata
            file_id = self._extract_file_identifier(node)

            if file_id not in file_groups:
                file_groups[file_id] = []
            file_groups[file_id].append(node)

        # Select best node per file (highest score)
        best_nodes = []
        for file_id, file_nodes in file_groups.items():
            best_node = max(file_nodes, key=lambda n: n.score if n.score else 0.0)
            best_nodes.append(best_node)

        # Sort by score and return top_k
        best_nodes.sort(key=lambda n: n.score if n.score else 0.0, reverse=True)
        return best_nodes[:top_k]

    def _extract_file_identifier(self, node: NodeWithScore) -> str:
        """Extract file identifier from node metadata."""
        metadata = node.node.metadata or {}

        # Try different metadata keys for file identification
        for key in ['file_path', 'source_file', 'document_id', 'file_name', 'source']:
            if key in metadata:
                return str(metadata[key])

        # Fallback to node ID if no file identifier found
        return str(node.node.node_id)

    def get_strategy_name(self) -> str:
        return "File Content Retrieval"

    def get_strategy_description(self) -> str:
        return "Retrieves entire files based on content similarity for comprehensive analysis"


class FileMetadataRetrievalStrategy(RetrievalStrategy):
    """
    Metadata-based file retrieval strategy.

    Retrieves files based on filename, path, or other metadata matching.
    Useful for queries like "show me the deployment.yaml file" or
    "find the README in the project root".
    """

    def retrieve(self, query: str, top_k: int = 10, **kwargs) -> List[NodeWithScore]:
        """Retrieve files based on metadata matching."""
        try:
            index = self._get_or_create_index()

            # Extract potential file references from query
            file_keywords = self._extract_file_keywords(query)

            if not file_keywords:
                # If no file keywords found, fall back to content-based retrieval
                logger.info("No file keywords found, falling back to content retrieval")
                fallback_strategy = FileContentRetrievalStrategy(self.tenant_slug, self.intent)
                return fallback_strategy.retrieve(query, top_k, **kwargs)

            # Create retriever
            retriever = VectorIndexRetriever(
                index=index,
                similarity_top_k=top_k * 3,  # Get more candidates for metadata filtering
                embed_model=get_embedding_model_for_content(self.intent)
            )

            # Create query bundle
            query_bundle = QueryBundle(query_str=query)

            # Retrieve nodes
            nodes = retriever.retrieve(query_bundle)

            # Filter nodes based on metadata matching
            filtered_nodes = self._filter_by_metadata(nodes, file_keywords)

            # If no metadata matches found, fall back to semantic search
            if not filtered_nodes:
                logger.info("No metadata matches found, using semantic search")
                return nodes[:top_k]

            logger.info(f"File metadata retrieval completed: {len(filtered_nodes)} files retrieved")
            return filtered_nodes[:top_k]

        except Exception as e:
            logger.error(f"File metadata retrieval failed for query '{query}': {e}")
            raise RetrievalError(
                f"File metadata retrieval failed: {e}",
                collection_name=None,
                query=query
            )

    def _extract_file_keywords(self, query: str) -> List[str]:
        """Extract potential file references from query."""
        import re

        keywords = []

        # Common file extensions
        file_extensions = re.findall(r'\b\w+\.\w+\b', query)
        keywords.extend(file_extensions)

        # File path patterns
        path_patterns = re.findall(r'[/\\][\w/\\.-]+', query)
        keywords.extend(path_patterns)

        # Quoted filenames
        quoted_files = re.findall(r'["\']([^"\']+)["\']', query)
        keywords.extend(quoted_files)

        # Common file-related terms
        file_terms = ['file', 'document', 'readme', 'config', 'setup', 'install']
        for term in file_terms:
            if term.lower() in query.lower():
                keywords.append(term)

        return list(set(keywords))  # Remove duplicates

    def _filter_by_metadata(self, nodes: List[NodeWithScore], keywords: List[str]) -> List[NodeWithScore]:
        """Filter nodes based on metadata matching."""
        filtered_nodes = []

        for node in nodes:
            metadata = node.node.metadata or {}

            # Check if any keyword matches metadata values
            for keyword in keywords:
                for meta_key, meta_value in metadata.items():
                    if keyword.lower() in str(meta_value).lower():
                        filtered_nodes.append(node)
                        break
                else:
                    continue
                break

        return filtered_nodes

    def get_strategy_name(self) -> str:
        return "File Metadata Retrieval"

    def get_strategy_description(self) -> str:
        return "Retrieves files based on filename, path, or metadata matching"
