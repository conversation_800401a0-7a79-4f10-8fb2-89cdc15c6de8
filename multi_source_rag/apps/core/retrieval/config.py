"""
Retrieval Configuration Manager for Agentic RAG
==============================================

Manages configuration for different retrieval strategies and modes.
Provides UI-configurable parameters for controlling RAG sophistication.

Features:
- Intent-based configuration
- Strategy-specific parameters
- UI-friendly configuration interface
- Performance tuning options
- Validation and defaults

Follows configuration management best practices.
"""

import logging
from typing import Any, Dict, List, Optional
from dataclasses import dataclass, field
from enum import Enum

from apps.core.constants import RAGConstants, LlamaIndexConstants, ValidationConstants
from apps.core.exceptions import ConfigurationError, ValidationError
from apps.core.retrieval.router import RoutingMethod

logger = logging.getLogger(__name__)


class SophisticationLevel(Enum):
    """RAG sophistication levels for UI configuration."""
    BASIC = "basic"           # Simple chunk retrieval
    STANDARD = "standard"     # Hybrid search + HyDE
    ADVANCED = "advanced"     # Multi-step + file-level retrieval
    EXPERT = "expert"         # All features + custom tuning


@dataclass
class RetrievalConfig:
    """
    Configuration for retrieval strategies and features.
    
    Provides comprehensive configuration options that can be
    controlled from the UI to adjust RAG sophistication.
    """
    
    # Core retrieval parameters
    similarity_top_k: int = RAGConstants.DEFAULT_SIMILARITY_TOP_K
    enable_hyde: bool = True
    enable_hybrid: bool = True
    enable_multi_step: bool = False
    
    # File-level retrieval settings
    enable_file_retrieval: bool = True
    file_retrieval_threshold: float = 0.8  # Confidence threshold for file-level retrieval
    max_file_size_mb: int = 10  # Maximum file size to retrieve in full
    
    # Strategy routing configuration
    routing_method: RoutingMethod = RoutingMethod.HYBRID
    llm_routing_enabled: bool = True
    rule_routing_enabled: bool = True
    
    # Performance and quality settings
    min_relevance_score: float = RAGConstants.DEFAULT_MIN_RELEVANCE_SCORE
    response_mode: str = "tree_summarize"
    streaming: bool = False
    use_async: bool = False
    
    # Advanced features
    enable_reranking: bool = False
    enable_query_expansion: bool = False
    enable_context_compression: bool = False
    
    # Caching configuration
    enable_strategy_caching: bool = True
    cache_ttl_seconds: int = RAGConstants.SEARCH_CACHE_TIMEOUT
    
    # UI display options
    show_confidence_scores: bool = True
    show_retrieval_strategy: bool = True
    show_source_metadata: bool = True
    
    # Custom parameters for specific intents
    custom_parameters: Dict[str, Any] = field(default_factory=dict)
    
    def validate(self) -> None:
        """
        Validate configuration parameters.
        
        Raises:
            ValidationError: If configuration is invalid
        """
        if self.similarity_top_k < 1 or self.similarity_top_k > 50:
            raise ValidationError(
                "similarity_top_k must be between 1 and 50",
                field="similarity_top_k",
                value=self.similarity_top_k
            )
        
        if not 0.0 <= self.min_relevance_score <= 1.0:
            raise ValidationError(
                "min_relevance_score must be between 0.0 and 1.0",
                field="min_relevance_score", 
                value=self.min_relevance_score
            )
        
        if not 0.0 <= self.file_retrieval_threshold <= 1.0:
            raise ValidationError(
                "file_retrieval_threshold must be between 0.0 and 1.0",
                field="file_retrieval_threshold",
                value=self.file_retrieval_threshold
            )
        
        if self.max_file_size_mb < 1 or self.max_file_size_mb > 100:
            raise ValidationError(
                "max_file_size_mb must be between 1 and 100",
                field="max_file_size_mb",
                value=self.max_file_size_mb
            )
        
        if self.cache_ttl_seconds < 0:
            raise ValidationError(
                "cache_ttl_seconds must be non-negative",
                field="cache_ttl_seconds",
                value=self.cache_ttl_seconds
            )


class RetrievalConfigManager:
    """
    Manages retrieval configurations for different intents and sophistication levels.
    
    Provides a centralized way to configure RAG behavior that can be
    controlled from the UI and customized per tenant/intent.
    """
    
    def __init__(self, tenant_slug: str):
        """
        Initialize configuration manager.
        
        Args:
            tenant_slug: Tenant identifier for configuration isolation
        """
        self.tenant_slug = tenant_slug
        self._configs = {}
        self._sophistication_presets = self._create_sophistication_presets()
        
        # Load default configurations
        self._load_default_configs()
    
    def get_config(self, intent: str = "default", 
                   sophistication: Optional[SophisticationLevel] = None) -> RetrievalConfig:
        """
        Get retrieval configuration for intent and sophistication level.
        
        Args:
            intent: Query intent
            sophistication: Optional sophistication level override
            
        Returns:
            RetrievalConfig instance
        """
        config_key = f"{intent}_{sophistication.value if sophistication else 'default'}"
        
        if config_key not in self._configs:
            # Create config based on sophistication level or intent defaults
            if sophistication:
                config = self._create_config_from_sophistication(sophistication, intent)
            else:
                config = self._create_default_config_for_intent(intent)
            
            self._configs[config_key] = config
        
        return self._configs[config_key]
    
    def update_config(self, intent: str, config_updates: Dict[str, Any],
                     sophistication: Optional[SophisticationLevel] = None) -> None:
        """
        Update configuration for specific intent.
        
        Args:
            intent: Query intent to update
            config_updates: Dictionary of configuration updates
            sophistication: Optional sophistication level
            
        Raises:
            ValidationError: If updates are invalid
        """
        config_key = f"{intent}_{sophistication.value if sophistication else 'default'}"
        
        # Get current config or create new one
        current_config = self.get_config(intent, sophistication)
        
        # Apply updates
        for key, value in config_updates.items():
            if hasattr(current_config, key):
                setattr(current_config, key, value)
            else:
                # Store in custom_parameters for unknown keys
                current_config.custom_parameters[key] = value
        
        # Validate updated configuration
        current_config.validate()
        
        # Store updated config
        self._configs[config_key] = current_config
        
        logger.info(f"Updated retrieval config for {intent} (sophistication: {sophistication})")
    
    def set_sophistication_level(self, intent: str, level: SophisticationLevel) -> None:
        """
        Set sophistication level for an intent.
        
        Args:
            intent: Query intent
            level: Sophistication level to apply
        """
        config = self._create_config_from_sophistication(level, intent)
        config_key = f"{intent}_{level.value}"
        self._configs[config_key] = config
        
        logger.info(f"Set sophistication level {level.value} for intent {intent}")
    
    def get_available_sophistication_levels(self) -> List[Dict[str, Any]]:
        """
        Get available sophistication levels with descriptions.
        
        Returns:
            List of sophistication level information for UI
        """
        return [
            {
                'level': SophisticationLevel.BASIC.value,
                'name': 'Basic RAG',
                'description': 'Simple chunk-based retrieval for quick answers',
                'features': ['Vector similarity search', 'Basic relevance filtering']
            },
            {
                'level': SophisticationLevel.STANDARD.value,
                'name': 'Standard RAG',
                'description': 'Enhanced retrieval with hybrid search and query transformation',
                'features': ['Hybrid search (Vector + BM25)', 'HyDE query transformation', 'Improved relevance']
            },
            {
                'level': SophisticationLevel.ADVANCED.value,
                'name': 'Advanced RAG',
                'description': 'Multi-step reasoning and file-level retrieval',
                'features': ['Multi-step reasoning', 'File-level retrieval', 'Smart strategy routing', 'Query decomposition']
            },
            {
                'level': SophisticationLevel.EXPERT.value,
                'name': 'Expert RAG',
                'description': 'All features enabled with custom tuning options',
                'features': ['All advanced features', 'Custom parameter tuning', 'Performance optimization', 'Expert controls']
            }
        ]
    
    def _create_sophistication_presets(self) -> Dict[SophisticationLevel, RetrievalConfig]:
        """Create preset configurations for different sophistication levels."""
        return {
            SophisticationLevel.BASIC: RetrievalConfig(
                similarity_top_k=5,
                enable_hyde=False,
                enable_hybrid=False,
                enable_multi_step=False,
                enable_file_retrieval=False,
                routing_method=RoutingMethod.RULE_BASED,
                llm_routing_enabled=False,
                enable_reranking=False,
                enable_query_expansion=False,
                enable_context_compression=False
            ),
            
            SophisticationLevel.STANDARD: RetrievalConfig(
                similarity_top_k=10,
                enable_hyde=True,
                enable_hybrid=True,
                enable_multi_step=False,
                enable_file_retrieval=True,
                routing_method=RoutingMethod.RULE_BASED,
                llm_routing_enabled=False,
                enable_reranking=False,
                enable_query_expansion=False,
                enable_context_compression=False
            ),
            
            SophisticationLevel.ADVANCED: RetrievalConfig(
                similarity_top_k=15,
                enable_hyde=True,
                enable_hybrid=True,
                enable_multi_step=True,
                enable_file_retrieval=True,
                routing_method=RoutingMethod.HYBRID,
                llm_routing_enabled=True,
                enable_reranking=True,
                enable_query_expansion=False,
                enable_context_compression=False
            ),
            
            SophisticationLevel.EXPERT: RetrievalConfig(
                similarity_top_k=20,
                enable_hyde=True,
                enable_hybrid=True,
                enable_multi_step=True,
                enable_file_retrieval=True,
                routing_method=RoutingMethod.HYBRID,
                llm_routing_enabled=True,
                enable_reranking=True,
                enable_query_expansion=True,
                enable_context_compression=True
            )
        }
    
    def _create_config_from_sophistication(self, level: SophisticationLevel, intent: str) -> RetrievalConfig:
        """Create configuration from sophistication level."""
        base_config = self._sophistication_presets[level]
        
        # Apply intent-specific customizations
        intent_customizations = self._get_intent_customizations(intent)
        
        # Create new config with customizations
        config_dict = base_config.__dict__.copy()
        config_dict.update(intent_customizations)
        
        return RetrievalConfig(**config_dict)
    
    def _create_default_config_for_intent(self, intent: str) -> RetrievalConfig:
        """Create default configuration for specific intent."""
        # Start with standard sophistication
        base_config = self._sophistication_presets[SophisticationLevel.STANDARD]
        
        # Apply intent-specific customizations
        intent_customizations = self._get_intent_customizations(intent)
        
        config_dict = base_config.__dict__.copy()
        config_dict.update(intent_customizations)
        
        return RetrievalConfig(**config_dict)
    
    def _get_intent_customizations(self, intent: str) -> Dict[str, Any]:
        """Get intent-specific configuration customizations."""
        customizations = {
            'technical': {
                'enable_multi_step': True,
                'similarity_top_k': 15,
                'enable_file_retrieval': True,
                'file_retrieval_threshold': 0.7
            },
            'conversational': {
                'enable_hyde': False,
                'enable_multi_step': False,
                'similarity_top_k': 8,
                'response_mode': 'compact'
            },
            'factual': {
                'enable_hyde': True,
                'enable_hybrid': True,
                'min_relevance_score': 0.15,
                'similarity_top_k': 12
            },
            'code': {
                'enable_file_retrieval': True,
                'file_retrieval_threshold': 0.6,
                'enable_multi_step': True,
                'similarity_top_k': 20
            },
            'document': {
                'enable_file_retrieval': True,
                'file_retrieval_threshold': 0.8,
                'enable_context_compression': True,
                'similarity_top_k': 10
            }
        }
        
        return customizations.get(intent, {})
    
    def _load_default_configs(self) -> None:
        """Load default configurations for common intents."""
        common_intents = ['default', 'technical', 'conversational', 'factual', 'code', 'document']
        
        for intent in common_intents:
            config = self._create_default_config_for_intent(intent)
            self._configs[f"{intent}_default"] = config
        
        logger.info(f"Loaded default configurations for {len(common_intents)} intents")
    
    def export_config(self, intent: str, sophistication: Optional[SophisticationLevel] = None) -> Dict[str, Any]:
        """
        Export configuration as dictionary for API/UI.
        
        Args:
            intent: Query intent
            sophistication: Optional sophistication level
            
        Returns:
            Configuration dictionary
        """
        config = self.get_config(intent, sophistication)
        
        return {
            'tenant_slug': self.tenant_slug,
            'intent': intent,
            'sophistication': sophistication.value if sophistication else 'default',
            'config': config.__dict__
        }
    
    def import_config(self, config_data: Dict[str, Any]) -> None:
        """
        Import configuration from dictionary.
        
        Args:
            config_data: Configuration dictionary
            
        Raises:
            ValidationError: If configuration is invalid
        """
        intent = config_data.get('intent', 'default')
        sophistication_str = config_data.get('sophistication', 'default')
        config_dict = config_data.get('config', {})
        
        sophistication = None
        if sophistication_str != 'default':
            try:
                sophistication = SophisticationLevel(sophistication_str)
            except ValueError:
                raise ValidationError(f"Invalid sophistication level: {sophistication_str}")
        
        # Create and validate configuration
        config = RetrievalConfig(**config_dict)
        config.validate()
        
        # Store configuration
        config_key = f"{intent}_{sophistication_str}"
        self._configs[config_key] = config
        
        logger.info(f"Imported configuration for {intent} (sophistication: {sophistication_str})")
