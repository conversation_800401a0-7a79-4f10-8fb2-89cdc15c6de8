"""
Retrieval Strategy Router for Agentic RAG
=========================================

Implements intelligent routing between different retrieval strategies
based on query analysis and configuration.

Features:
- LLM-based strategy selection
- Rule-based fallbacks
- Configuration-driven routing
- Performance monitoring
- Strategy caching

Follows SOLID principles and enterprise patterns.
"""

import logging
import re
import threading
from typing import Any, Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from llama_index.core.schema import NodeWithScore
from apps.core.utils.memory_manager import memory_manager

from apps.core.constants import RAGConstants, LlamaIndexConstants
from apps.core.exceptions import RetrievalError, ValidationError
from apps.core.utils.llama_index_llm import get_llm
from apps.core.retrieval.strategies import (
    RetrievalStrategy, RetrievalMode,
    ChunkRetrievalStrategy, FileContentRetrievalStrategy,
    FileMetadataRetrievalStrategy
)

logger = logging.getLogger(__name__)


@dataclass
class RoutingDecision:
    """
    Represents a routing decision with confidence and reasoning.
    """
    strategy: RetrievalMode
    confidence: float
    reasoning: str
    fallback_strategy: Optional[RetrievalMode] = None
    parameters: Dict[str, Any] = None

    def __post_init__(self):
        if self.parameters is None:
            self.parameters = {}


class RoutingMethod(Enum):
    """Methods for determining retrieval strategy."""
    LLM_BASED = "llm_based"          # Use LLM to analyze query and decide
    RULE_BASED = "rule_based"        # Use predefined rules
    HYBRID = "hybrid"                # Combine LLM and rules
    CONFIGURATION = "configuration"  # Use explicit configuration


class RetrievalStrategyRouter:
    """
    Intelligent router for selecting optimal retrieval strategies.

    Uses multiple methods to determine the best retrieval approach:
    1. LLM-based analysis for complex queries
    2. Rule-based patterns for common cases
    3. Configuration overrides for explicit control
    4. Hybrid approaches combining multiple methods
    """

    def __init__(self, tenant_slug: str, routing_method: RoutingMethod = RoutingMethod.HYBRID):
        """
        Initialize the strategy router.

        Args:
            tenant_slug: Tenant identifier
            routing_method: Method to use for routing decisions
        """
        self.tenant_slug = tenant_slug
        self.routing_method = routing_method
        self._llm = None

        # Thread-safe cache manager to prevent memory leaks
        from apps.core.utils.memory_manager import CacheConfig
        cache_config = CacheConfig(max_size=1000, ttl_seconds=3600, cleanup_interval=900)
        self._strategy_cache = memory_manager.create_cache(f"strategy_router_{tenant_slug}", cache_config)

        # Rule-based patterns for quick routing
        self._file_patterns = [
            r'\b(show|display|open|view)\s+(me\s+)?(the\s+)?file\b',
            r'\b(full|entire|complete)\s+(file|document)\b',
            r'\b(download|export|save)\s+(file|document)\b',
            r'\.(py|js|java|cpp|c|h|md|txt|json|yaml|yml|xml|html|css|sql|sh|bat)(\s|$)',
            r'\b(readme|license|changelog|dockerfile|makefile|requirements\.txt)\b',
        ]

        self._metadata_patterns = [
            r'\b(find|locate|search\s+for)\s+(file|document)\s+named\b',
            r'\b(file|document)\s+(called|named)\b',
            r'\b(in\s+the\s+)?(folder|directory|path)\b',
            r'["\']([^"\']+\.(py|js|java|cpp|c|h|md|txt|json|yaml|yml|xml|html|css|sql|sh|bat))["\']',
        ]

        self._chunk_patterns = [
            r'\b(explain|describe|what\s+is|how\s+does|why\s+does)\b',
            r'\b(summary|summarize|overview)\b',
            r'\b(concept|idea|principle|theory)\b',
            r'\b(question|answer|help|assist)\b',
        ]

    def route_query(self, query: str, intent: str = "default",
                   explicit_mode: Optional[RetrievalMode] = None,
                   **kwargs) -> RoutingDecision:
        """
        Route query to optimal retrieval strategy.

        Args:
            query: Search query to analyze
            intent: Query intent for context
            explicit_mode: Explicitly requested retrieval mode
            **kwargs: Additional routing parameters

        Returns:
            RoutingDecision with selected strategy and reasoning

        Raises:
            ValidationError: If query is invalid
            RetrievalError: If routing fails
        """
        try:
            # Input validation
            if not query or not query.strip():
                raise ValidationError("Query cannot be empty for routing")

            # Check for explicit mode override
            if explicit_mode:
                return RoutingDecision(
                    strategy=explicit_mode,
                    confidence=1.0,
                    reasoning=f"Explicitly requested mode: {explicit_mode.value}",
                    parameters=kwargs
                )

            # Check cache for similar queries (thread-safe)
            cache_key = self._generate_cache_key(query, intent)
            cached_decision = self._strategy_cache.get(cache_key)
            if cached_decision is not None:
                logger.info(f"Using cached routing decision for query: {query[:50]}...")
                return cached_decision

            # Route based on configured method
            if self.routing_method == RoutingMethod.RULE_BASED:
                decision = self._route_with_rules(query, intent, **kwargs)
            elif self.routing_method == RoutingMethod.LLM_BASED:
                decision = self._route_with_llm(query, intent, **kwargs)
            elif self.routing_method == RoutingMethod.HYBRID:
                decision = self._route_with_hybrid(query, intent, **kwargs)
            else:  # CONFIGURATION
                decision = self._route_with_configuration(query, intent, **kwargs)

            # Cache the decision (thread-safe)
            self._strategy_cache.set(cache_key, decision)

            logger.info(f"Routed query to {decision.strategy.value} with confidence {decision.confidence:.2f}")
            logger.debug(f"Routing reasoning: {decision.reasoning}")

            return decision

        except Exception as e:
            logger.error(f"Routing failed for query '{query}': {e}")
            # Fallback to chunk retrieval
            return RoutingDecision(
                strategy=RetrievalMode.CHUNKS,
                confidence=0.5,
                reasoning=f"Fallback due to routing error: {e}",
                parameters=kwargs
            )

    def _route_with_rules(self, query: str, intent: str, **kwargs) -> RoutingDecision:
        """Route using predefined rule patterns."""
        query_lower = query.lower()

        # Check for file metadata patterns (highest priority)
        for pattern in self._metadata_patterns:
            if re.search(pattern, query_lower, re.IGNORECASE):
                return RoutingDecision(
                    strategy=RetrievalMode.FILES_VIA_METADATA,
                    confidence=0.9,
                    reasoning=f"Matched metadata pattern: {pattern}",
                    fallback_strategy=RetrievalMode.FILES_VIA_CONTENT,
                    parameters=kwargs
                )

        # Check for file content patterns
        for pattern in self._file_patterns:
            if re.search(pattern, query_lower, re.IGNORECASE):
                return RoutingDecision(
                    strategy=RetrievalMode.FILES_VIA_CONTENT,
                    confidence=0.8,
                    reasoning=f"Matched file content pattern: {pattern}",
                    fallback_strategy=RetrievalMode.CHUNKS,
                    parameters=kwargs
                )

        # Check for chunk patterns (default behavior)
        for pattern in self._chunk_patterns:
            if re.search(pattern, query_lower, re.IGNORECASE):
                return RoutingDecision(
                    strategy=RetrievalMode.CHUNKS,
                    confidence=0.7,
                    reasoning=f"Matched chunk pattern: {pattern}",
                    parameters=kwargs
                )

        # Default to chunks if no patterns match
        return RoutingDecision(
            strategy=RetrievalMode.CHUNKS,
            confidence=0.6,
            reasoning="No specific patterns matched, using default chunk retrieval",
            parameters=kwargs
        )

    def _route_with_llm(self, query: str, intent: str, **kwargs) -> RoutingDecision:
        """Route using LLM analysis."""
        try:
            if self._llm is None:
                self._llm = get_llm(intent)

            routing_prompt = f"""
            Analyze this search query and determine the optimal retrieval strategy:

            Query: "{query}"
            Intent: {intent}

            Available strategies:
            1. CHUNKS - Retrieve relevant document chunks (best for explanations, concepts, Q&A)
            2. FILES_VIA_CONTENT - Retrieve entire files based on content similarity (best for comprehensive analysis)
            3. FILES_VIA_METADATA - Retrieve files by name/path matching (best for specific file requests)

            Consider:
            - Does the user want a specific file or general information?
            - Would they benefit from seeing the complete file or just relevant sections?
            - Are there filename/path references in the query?

            Respond with ONLY the strategy name (CHUNKS, FILES_VIA_CONTENT, or FILES_VIA_METADATA) and confidence (0.0-1.0) and brief reasoning.
            Format: STRATEGY|CONFIDENCE|REASONING
            """

            response = self._llm.invoke(routing_prompt)

            # Parse LLM response
            parts = response.strip().split('|')
            if len(parts) >= 3:
                strategy_name = parts[0].strip()
                confidence = float(parts[1].strip())
                reasoning = parts[2].strip()

                # Map strategy name to enum
                strategy_map = {
                    'CHUNKS': RetrievalMode.CHUNKS,
                    'FILES_VIA_CONTENT': RetrievalMode.FILES_VIA_CONTENT,
                    'FILES_VIA_METADATA': RetrievalMode.FILES_VIA_METADATA
                }

                strategy = strategy_map.get(strategy_name, RetrievalMode.CHUNKS)

                return RoutingDecision(
                    strategy=strategy,
                    confidence=min(max(confidence, 0.0), 1.0),  # Clamp to [0,1]
                    reasoning=f"LLM analysis: {reasoning}",
                    parameters=kwargs
                )

        except Exception as e:
            logger.warning(f"LLM routing failed: {e}, falling back to rules")

        # Fallback to rule-based routing
        return self._route_with_rules(query, intent, **kwargs)

    def _route_with_hybrid(self, query: str, intent: str, **kwargs) -> RoutingDecision:
        """Route using combination of rules and LLM."""
        # Get both rule-based and LLM-based decisions
        rule_decision = self._route_with_rules(query, intent, **kwargs)
        llm_decision = self._route_with_llm(query, intent, **kwargs)

        # If both agree, use higher confidence
        if rule_decision.strategy == llm_decision.strategy:
            return RoutingDecision(
                strategy=rule_decision.strategy,
                confidence=max(rule_decision.confidence, llm_decision.confidence),
                reasoning=f"Hybrid consensus: {rule_decision.reasoning} + {llm_decision.reasoning}",
                fallback_strategy=rule_decision.fallback_strategy,
                parameters=kwargs
            )

        # If they disagree, use the one with higher confidence
        if llm_decision.confidence > rule_decision.confidence:
            return RoutingDecision(
                strategy=llm_decision.strategy,
                confidence=llm_decision.confidence * 0.9,  # Slight penalty for disagreement
                reasoning=f"Hybrid (LLM preferred): {llm_decision.reasoning}",
                fallback_strategy=rule_decision.strategy,
                parameters=kwargs
            )
        else:
            return RoutingDecision(
                strategy=rule_decision.strategy,
                confidence=rule_decision.confidence * 0.9,  # Slight penalty for disagreement
                reasoning=f"Hybrid (rules preferred): {rule_decision.reasoning}",
                fallback_strategy=llm_decision.strategy,
                parameters=kwargs
            )

    def _route_with_configuration(self, query: str, intent: str, **kwargs) -> RoutingDecision:
        """Route based on explicit configuration."""
        # Check for configuration overrides in kwargs
        config_mode = kwargs.get('retrieval_mode')
        if config_mode and isinstance(config_mode, str):
            try:
                strategy = RetrievalMode(config_mode)
                return RoutingDecision(
                    strategy=strategy,
                    confidence=1.0,
                    reasoning=f"Configuration override: {config_mode}",
                    parameters=kwargs
                )
            except ValueError:
                logger.warning(f"Invalid retrieval mode in configuration: {config_mode}")

        # Fallback to hybrid routing
        return self._route_with_hybrid(query, intent, **kwargs)

    def _generate_cache_key(self, query: str, intent: str) -> str:
        """Generate cache key for routing decisions."""
        # Use first 100 chars of query + intent for caching
        query_key = query[:100].lower().strip()
        return f"{self.tenant_slug}_{intent}_{hash(query_key)}"

    def create_strategy(self, mode: RetrievalMode, intent: str = "default") -> RetrievalStrategy:
        """
        Create retrieval strategy instance for the given mode.

        Args:
            mode: Retrieval mode to create
            intent: Query intent for configuration

        Returns:
            RetrievalStrategy instance

        Raises:
            RetrievalError: If strategy creation fails
        """
        try:
            strategy_map = {
                RetrievalMode.CHUNKS: ChunkRetrievalStrategy,
                RetrievalMode.FILES_VIA_CONTENT: FileContentRetrievalStrategy,
                RetrievalMode.FILES_VIA_METADATA: FileMetadataRetrievalStrategy,
            }

            strategy_class = strategy_map.get(mode)
            if not strategy_class:
                raise RetrievalError(f"Unknown retrieval mode: {mode}")

            return strategy_class(self.tenant_slug, intent)

        except Exception as e:
            logger.error(f"Failed to create strategy for mode {mode}: {e}")
            raise RetrievalError(f"Strategy creation failed: {e}")

    def clear_cache(self):
        """Clear the routing decision cache (thread-safe)."""
        self._strategy_cache.clear()
        logger.info("Routing cache cleared")

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics for monitoring (thread-safe)."""
        cache_stats = self._strategy_cache.get_stats()

        return {
            'cache_size': cache_stats['size'],
            'cache_current_size': cache_stats['size'],
            'cache_max_size': cache_stats['maxsize'],
            'cache_ttl': cache_stats['ttl'],
            'cache_hit_rate': cache_stats['hit_rate'],
            'cache_hits': cache_stats['hits'],
            'cache_misses': cache_stats['misses'],
            'tenant_slug': self.tenant_slug,
            'routing_method': self.routing_method.value
        }
