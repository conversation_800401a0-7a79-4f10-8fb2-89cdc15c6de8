"""
Agentic Retrieval Package for Multi-Source RAG
==============================================

This package implements advanced retrieval strategies for the RAG system,
providing file-level retrieval, intelligent routing, and configurable
sophistication levels.

Key Components:
- RetrievalStrategy: Abstract base for different retrieval approaches
- RetrievalStrategyRouter: Intelligent routing between strategies
- RetrievalConfigManager: Configuration management for UI control
- SophisticationLevel: Predefined complexity levels

Features:
- File-level vs chunk-level retrieval
- Metadata-based file finding
- LLM-based strategy routing
- UI-configurable sophistication levels
- Performance monitoring and caching

Usage:
    from apps.core.retrieval import (
        RetrievalStrategyRouter, RetrievalConfigManager,
        RetrievalMode, SophisticationLevel
    )

    # Create router and config manager
    router = RetrievalStrategyRouter(tenant_slug)
    config_manager = RetrievalConfigManager(tenant_slug)

    # Route query to optimal strategy
    decision = router.route_query(query, intent)
    strategy = router.create_strategy(decision.strategy, intent)

    # Retrieve documents
    results = strategy.retrieve(query, top_k=10)
"""

from .strategies import (
    RetrievalStrategy,
    RetrievalMode,
    ChunkRetrievalStrategy,
    FileContentRetrievalStrategy,
    FileMetadataRetrievalStrategy
)

from .router import (
    RetrievalStrategyRouter,
    RoutingDecision,
    RoutingMethod
)

from .config import (
    RetrievalConfigManager,
    RetrievalConfig,
    SophisticationLevel
)

from .cross_domain_router import (
    CrossDomainRouter,
    DataDomain,
    DomainRoutingDecision,
    QueryType
)

from .query_enhancer import (
    DomainQueryEnhancer,
    QueryEnhancement,
    EnhancementStrategy
)

from .result_fusion import (
    ResultFusionEngine,
    DomainResult,
    FusionResult,
    FusionStrategy
)

from .strategy_orchestrator import (
    AdvancedStrategyOrchestrator,
    QueryComplexityAnalyzer,
    StrategyDecision,
    StrategyComplexity
)

from .unified_search_engine import (
    UnifiedAgenticSearchEngine,
    SearchExecution,
    ResultQualityAnalyzer
)

__all__ = [
    # Strategy classes
    'RetrievalStrategy',
    'RetrievalMode',
    'ChunkRetrievalStrategy',
    'FileContentRetrievalStrategy',
    'FileMetadataRetrievalStrategy',

    # Router classes
    'RetrievalStrategyRouter',
    'RoutingDecision',
    'RoutingMethod',

    # Configuration classes
    'RetrievalConfigManager',
    'RetrievalConfig',
    'SophisticationLevel',

    # Cross-domain classes
    'CrossDomainRouter',
    'DataDomain',
    'DomainRoutingDecision',
    'QueryType',

    # Query enhancement classes
    'DomainQueryEnhancer',
    'QueryEnhancement',
    'EnhancementStrategy',

    # Result fusion classes
    'ResultFusionEngine',
    'DomainResult',
    'FusionResult',
    'FusionStrategy',

    # Advanced orchestration classes
    'AdvancedStrategyOrchestrator',
    'QueryComplexityAnalyzer',
    'StrategyDecision',
    'StrategyComplexity',

    # Unified search engine classes
    'UnifiedAgenticSearchEngine',
    'SearchExecution',
    'ResultQualityAnalyzer'
]

# Version information
__version__ = '1.0.0'
__author__ = 'RAG Development Team'
__description__ = 'Agentic retrieval strategies for multi-source RAG system'
