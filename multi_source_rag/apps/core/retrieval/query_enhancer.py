"""
Domain-Specific Query Enhancement for Cross-Domain Intelligence
==============================================================

Enhances queries for specific data domains to improve retrieval accuracy
and relevance across different types of content sources.

Features:
- Domain-specific query adaptation
- Context-aware query expansion
- Semantic query transformation
- Performance optimization with caching
- LLM-based query enhancement

Follows SOLID principles and enterprise patterns.
"""

import logging
import threading
from typing import Any, Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from apps.core.utils.memory_manager import memory_manager, CacheConfig
from apps.core.exceptions import ValidationError
from apps.core.utils.llama_index_llm import get_llm
from apps.core.retrieval.cross_domain_router import DataDomain

logger = logging.getLogger(__name__)


@dataclass
class QueryEnhancement:
    """
    Represents an enhanced query with domain-specific adaptations.
    """
    original_query: str
    enhanced_query: str
    domain: DataDomain
    enhancement_type: str
    confidence: float
    reasoning: str
    keywords_added: List[str]
    context_terms: List[str]


class EnhancementStrategy(Enum):
    """Query enhancement strategies."""
    KEYWORD_EXPANSION = "keyword_expansion"
    CONTEXT_INJECTION = "context_injection"
    SEMANTIC_TRANSFORMATION = "semantic_transformation"
    DOMAIN_SPECIALIZATION = "domain_specialization"
    HYBRID_ENHANCEMENT = "hybrid_enhancement"


class DomainQueryEnhancer:
    """
    Enhances queries for specific data domains to improve retrieval accuracy.

    Uses domain-specific knowledge and patterns to adapt queries for
    optimal performance in different content types and sources.
    """

    def __init__(self, tenant_slug: str):
        """
        Initialize domain query enhancer.

        Args:
            tenant_slug: Tenant identifier for configuration
        """
        self.tenant_slug = tenant_slug
        self._llm = None

        # Thread safety lock for cache operations
        self._cache_lock = threading.RLock()

        # Thread-safe cache manager to prevent memory leaks
        cache_config = CacheConfig(max_size=300, ttl_seconds=1200, cleanup_interval=600)
        self._enhancement_cache = memory_manager.create_cache(f"query_enhancer_{tenant_slug}", cache_config)

        # Domain-specific enhancement patterns
        self._domain_patterns = self._build_domain_patterns()

        # Context vocabularies for each domain
        self._domain_vocabularies = self._build_domain_vocabularies()

        # Enhancement templates
        self._enhancement_templates = self._build_enhancement_templates()

    def enhance_query_for_domain(self, query: str, domain: DataDomain,
                                strategy: EnhancementStrategy = EnhancementStrategy.HYBRID_ENHANCEMENT,
                                **_kwargs) -> QueryEnhancement:
        """
        Enhance query for specific domain.

        Args:
            query: Original search query
            domain: Target data domain
            strategy: Enhancement strategy to use
            **kwargs: Additional enhancement parameters

        Returns:
            QueryEnhancement with adapted query

        Raises:
            ValidationError: If query is invalid
            RetrievalError: If enhancement fails
        """
        try:
            # Input validation
            if not query or not query.strip():
                raise ValidationError("Query cannot be empty for enhancement")

            # Check cache (thread-safe)
            cache_key = self._generate_cache_key(query, domain, strategy)
            with self._cache_lock:
                if cache_key in self._enhancement_cache:
                    cached_enhancement = self._enhancement_cache[cache_key]
                    logger.debug(f"Using cached enhancement for query: {query[:50]}...")
                    return cached_enhancement

            # Apply enhancement strategy
            if strategy == EnhancementStrategy.KEYWORD_EXPANSION:
                enhancement = self._enhance_with_keywords(query, domain)
            elif strategy == EnhancementStrategy.CONTEXT_INJECTION:
                enhancement = self._enhance_with_context(query, domain)
            elif strategy == EnhancementStrategy.SEMANTIC_TRANSFORMATION:
                enhancement = self._enhance_with_semantics(query, domain)
            elif strategy == EnhancementStrategy.DOMAIN_SPECIALIZATION:
                enhancement = self._enhance_with_specialization(query, domain)
            else:  # HYBRID_ENHANCEMENT
                enhancement = self._enhance_with_hybrid(query, domain)

            # Cache the enhancement (thread-safe)
            with self._cache_lock:
                self._enhancement_cache[cache_key] = enhancement

            logger.info(f"Enhanced query for {domain.value}: {enhancement.confidence:.2f} confidence")
            logger.debug(f"Enhancement reasoning: {enhancement.reasoning}")

            return enhancement

        except Exception as e:
            logger.error(f"Query enhancement failed for domain {domain.value}: {e}")
            # Fallback to original query
            return QueryEnhancement(
                original_query=query,
                enhanced_query=query,
                domain=domain,
                enhancement_type="fallback",
                confidence=0.5,
                reasoning=f"Fallback due to enhancement error: {e}",
                keywords_added=[],
                context_terms=[]
            )

    def enhance_queries_for_multiple_domains(self, query: str, domains: List[DataDomain],
                                           strategy: EnhancementStrategy = EnhancementStrategy.HYBRID_ENHANCEMENT) -> Dict[DataDomain, QueryEnhancement]:
        """
        Enhance query for multiple domains simultaneously.

        Args:
            query: Original search query
            domains: List of target domains
            strategy: Enhancement strategy to use

        Returns:
            Dictionary mapping domains to their enhanced queries
        """
        enhancements = {}

        for domain in domains:
            try:
                enhancement = self.enhance_query_for_domain(query, domain, strategy)
                enhancements[domain] = enhancement
            except Exception as e:
                logger.warning(f"Failed to enhance query for domain {domain.value}: {e}")
                # Add fallback enhancement
                enhancements[domain] = QueryEnhancement(
                    original_query=query,
                    enhanced_query=query,
                    domain=domain,
                    enhancement_type="fallback",
                    confidence=0.5,
                    reasoning=f"Fallback for domain {domain.value}",
                    keywords_added=[],
                    context_terms=[]
                )

        return enhancements

    def _enhance_with_keywords(self, query: str, domain: DataDomain) -> QueryEnhancement:
        """Enhance query by adding domain-specific keywords."""
        domain_keywords = self._domain_vocabularies.get(domain, [])
        query_lower = query.lower()

        # Find relevant keywords not already in query
        relevant_keywords = []
        for keyword in domain_keywords:
            if keyword.lower() not in query_lower:
                # Check if keyword is contextually relevant
                if self._is_keyword_relevant(query, keyword, domain):
                    relevant_keywords.append(keyword)

        # Add top 3 most relevant keywords
        keywords_to_add = relevant_keywords[:3]

        if keywords_to_add:
            enhanced_query = f"{query} {' '.join(keywords_to_add)}"
            confidence = min(0.8, len(keywords_to_add) * 0.25)
        else:
            enhanced_query = query
            confidence = 0.6

        return QueryEnhancement(
            original_query=query,
            enhanced_query=enhanced_query,
            domain=domain,
            enhancement_type="keyword_expansion",
            confidence=confidence,
            reasoning=f"Added {len(keywords_to_add)} domain-specific keywords",
            keywords_added=keywords_to_add,
            context_terms=[]
        )

    def _enhance_with_context(self, query: str, domain: DataDomain) -> QueryEnhancement:
        """Enhance query by injecting domain context."""
        context_templates = self._enhancement_templates.get(domain, {})

        # Select appropriate context template
        template = self._select_context_template(query, context_templates)

        if template:
            enhanced_query = template.format(query=query)
            confidence = 0.75
            reasoning = f"Applied domain context template for {domain.value}"
        else:
            enhanced_query = query
            confidence = 0.6
            reasoning = "No suitable context template found"

        return QueryEnhancement(
            original_query=query,
            enhanced_query=enhanced_query,
            domain=domain,
            enhancement_type="context_injection",
            confidence=confidence,
            reasoning=reasoning,
            keywords_added=[],
            context_terms=[]
        )

    def _enhance_with_semantics(self, query: str, domain: DataDomain) -> QueryEnhancement:
        """Enhance query using semantic transformation."""
        try:
            if self._llm is None:
                self._llm = get_llm("default")

            semantic_prompt = f"""
            Transform this query to be more effective for searching {domain.value} content:

            Original query: "{query}"
            Domain: {domain.value}

            Consider:
            - Domain-specific terminology
            - Common patterns in {domain.value} content
            - Relevant context for this type of search

            Provide an enhanced query that maintains the original intent but is optimized for {domain.value}.
            Return only the enhanced query, no explanation.
            """

            enhanced_query = self._llm.invoke(semantic_prompt).strip()

            # Validate enhancement
            if enhanced_query and enhanced_query != query and len(enhanced_query) < 500:
                confidence = 0.8
                reasoning = f"LLM semantic transformation for {domain.value}"
            else:
                enhanced_query = query
                confidence = 0.6
                reasoning = "LLM enhancement failed validation, using original"

        except Exception as e:
            logger.warning(f"Semantic enhancement failed: {e}")
            enhanced_query = query
            confidence = 0.5
            reasoning = f"Semantic enhancement error: {e}"

        return QueryEnhancement(
            original_query=query,
            enhanced_query=enhanced_query,
            domain=domain,
            enhancement_type="semantic_transformation",
            confidence=confidence,
            reasoning=reasoning,
            keywords_added=[],
            context_terms=[]
        )

    def _enhance_with_specialization(self, query: str, domain: DataDomain) -> QueryEnhancement:
        """Enhance query with domain specialization."""
        specialization_patterns = self._domain_patterns.get(domain, [])

        enhanced_query = query
        applied_patterns = []

        for pattern_name, pattern_func in specialization_patterns:
            try:
                specialized_query = pattern_func(query)
                if specialized_query != query:
                    enhanced_query = specialized_query
                    applied_patterns.append(pattern_name)
                    break  # Apply only one specialization pattern
            except Exception as e:
                logger.warning(f"Specialization pattern {pattern_name} failed: {e}")

        confidence = 0.7 if applied_patterns else 0.6
        reasoning = f"Applied specialization patterns: {', '.join(applied_patterns)}" if applied_patterns else "No specialization patterns applied"

        return QueryEnhancement(
            original_query=query,
            enhanced_query=enhanced_query,
            domain=domain,
            enhancement_type="domain_specialization",
            confidence=confidence,
            reasoning=reasoning,
            keywords_added=[],
            context_terms=applied_patterns
        )

    def _enhance_with_hybrid(self, query: str, domain: DataDomain) -> QueryEnhancement:
        """Enhance query using hybrid approach combining multiple strategies."""
        # Apply keyword expansion first
        keyword_enhancement = self._enhance_with_keywords(query, domain)

        # Apply context injection to keyword-enhanced query
        context_enhancement = self._enhance_with_context(keyword_enhancement.enhanced_query, domain)

        # Combine results
        final_query = context_enhancement.enhanced_query
        combined_keywords = keyword_enhancement.keywords_added
        combined_confidence = (keyword_enhancement.confidence + context_enhancement.confidence) / 2

        return QueryEnhancement(
            original_query=query,
            enhanced_query=final_query,
            domain=domain,
            enhancement_type="hybrid_enhancement",
            confidence=combined_confidence,
            reasoning=f"Hybrid: {keyword_enhancement.reasoning} + {context_enhancement.reasoning}",
            keywords_added=combined_keywords,
            context_terms=[]
        )

    def _is_keyword_relevant(self, query: str, keyword: str, domain: DataDomain) -> bool:
        """Check if a keyword is relevant to the query and domain."""
        query_words = set(query.lower().split())
        keyword_lower = keyword.lower()

        # Simple relevance heuristics
        if len(query_words.intersection(keyword_lower.split())) > 0:
            return True

        # Domain-specific relevance rules
        domain_relevance_rules = {
            DataDomain.GITHUB_CODE: lambda q: any(term in q.lower() for term in ['code', 'function', 'class', 'method', 'implementation']),
            DataDomain.SLACK_CONVERSATIONS: lambda q: any(term in q.lower() for term in ['discuss', 'talk', 'mention', 'conversation']),
            DataDomain.GITHUB_ISSUES: lambda q: any(term in q.lower() for term in ['bug', 'issue', 'problem', 'error']),
        }

        relevance_rule = domain_relevance_rules.get(domain)
        if relevance_rule:
            return relevance_rule(query)

        return False

    def _select_context_template(self, query: str, templates: Dict[str, str]) -> Optional[str]:
        """Select the most appropriate context template for the query."""
        if not templates:
            return None

        # Simple template selection based on query characteristics
        query_lower = query.lower()

        if any(term in query_lower for term in ['how', 'what', 'why', 'when']):
            return templates.get('question', templates.get('default'))
        elif any(term in query_lower for term in ['find', 'search', 'locate']):
            return templates.get('search', templates.get('default'))
        elif any(term in query_lower for term in ['error', 'problem', 'issue']):
            return templates.get('problem', templates.get('default'))

        return templates.get('default')

    def _build_domain_patterns(self) -> Dict[DataDomain, List[Tuple[str, callable]]]:
        """Build domain-specific enhancement patterns."""
        return {
            DataDomain.GITHUB_CODE: [
                ("code_context", lambda q: f"in code or implementation: {q}"),
                ("repository_context", lambda q: f"in repository or codebase: {q}"),
            ],
            DataDomain.SLACK_CONVERSATIONS: [
                ("conversation_context", lambda q: f"in discussions or conversations: {q}"),
                ("team_context", lambda q: f"discussed by team or mentioned: {q}"),
            ],
            DataDomain.GITHUB_ISSUES: [
                ("issue_context", lambda q: f"in issues or bug reports: {q}"),
                ("problem_context", lambda q: f"reported problems or errors: {q}"),
            ],
            DataDomain.DOCUMENTATION: [
                ("docs_context", lambda q: f"in documentation or guides: {q}"),
                ("reference_context", lambda q: f"in reference materials: {q}"),
            ],
        }

    def _build_domain_vocabularies(self) -> Dict[DataDomain, List[str]]:
        """Build domain-specific vocabulary lists."""
        return {
            DataDomain.GITHUB_CODE: [
                "function", "method", "class", "variable", "implementation", "algorithm",
                "code", "repository", "commit", "branch", "pull request", "merge"
            ],
            DataDomain.SLACK_CONVERSATIONS: [
                "discussion", "conversation", "chat", "message", "thread", "channel",
                "mentioned", "discussed", "talked", "said", "team", "meeting"
            ],
            DataDomain.GITHUB_ISSUES: [
                "bug", "issue", "problem", "error", "failure", "broken",
                "reported", "ticket", "fix", "resolve", "solution"
            ],
            DataDomain.DOCUMENTATION: [
                "guide", "manual", "documentation", "readme", "wiki", "tutorial",
                "instructions", "reference", "specification", "overview"
            ],
            DataDomain.MEETING_NOTES: [
                "meeting", "notes", "minutes", "agenda", "discussion", "decision",
                "action items", "follow-up", "attendees", "summary"
            ],
        }

    def _build_enhancement_templates(self) -> Dict[DataDomain, Dict[str, str]]:
        """Build enhancement templates for different domains."""
        return {
            DataDomain.GITHUB_CODE: {
                "default": "in code, implementation, or repository: {query}",
                "question": "how is {query} implemented in code?",
                "search": "find code or implementation for: {query}",
                "problem": "code issues or bugs related to: {query}"
            },
            DataDomain.SLACK_CONVERSATIONS: {
                "default": "in team discussions or conversations: {query}",
                "question": "what was discussed about {query}?",
                "search": "find conversations or discussions about: {query}",
                "problem": "team discussions about problems with: {query}"
            },
            DataDomain.GITHUB_ISSUES: {
                "default": "in issues, bugs, or problem reports: {query}",
                "question": "what issues exist with {query}?",
                "search": "find issues or bugs related to: {query}",
                "problem": "reported problems or errors with: {query}"
            },
            DataDomain.DOCUMENTATION: {
                "default": "in documentation, guides, or manuals: {query}",
                "question": "how to {query} according to documentation?",
                "search": "find documentation or guides for: {query}",
                "problem": "documentation about troubleshooting: {query}"
            },
        }

    def _generate_cache_key(self, query: str, domain: DataDomain, strategy: EnhancementStrategy) -> str:
        """Generate cache key for query enhancements."""
        query_key = query[:100].lower().strip()
        return f"{self.tenant_slug}_{domain.value}_{strategy.value}_{hash(query_key)}"

    def clear_cache(self) -> None:
        """Clear the enhancement cache (thread-safe)."""
        with self._cache_lock:
            self._enhancement_cache.clear()
        logger.info("Query enhancement cache cleared")

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics for monitoring (thread-safe)."""
        with self._cache_lock:
            cache_size = len(self._enhancement_cache)
            cache_info = getattr(self._enhancement_cache, 'currsize', cache_size)

        return {
            'cache_size': cache_size,
            'cache_current_size': cache_info,
            'cache_max_size': getattr(self._enhancement_cache, 'maxsize', 300),
            'cache_ttl': getattr(self._enhancement_cache, 'ttl', 1200),
            'tenant_slug': self.tenant_slug,
            'available_domains': len(DataDomain),
            'available_strategies': len(EnhancementStrategy)
        }
