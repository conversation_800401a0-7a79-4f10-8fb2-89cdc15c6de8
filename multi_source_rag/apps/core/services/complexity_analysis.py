"""
Complexity Analysis Engine for Advanced RAG

This engine analyzes document complexity across 5 dimensions to enable
intelligent strategy selection in the ultimate agentic search system.
"""

import logging
import re
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


@dataclass
class ComplexityAnalysis:
    """Result of complexity analysis across 5 dimensions."""
    semantic_complexity: float
    domain_complexity: float
    temporal_complexity: float
    structural_complexity: float
    contextual_complexity: float
    overall_level: str  # SIMPLE, MODERATE, COMPLEX, ADAPTIVE
    strategy_recommendations: Dict[str, Any]
    performance_hints: Dict[str, Any]


class ComplexityAnalysisEngine:
    """Engine to analyze document complexity for strategy selection."""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # Technical vocabulary for semantic complexity
        self.technical_terms = [
            "algorithm", "implementation", "architecture", "framework",
            "optimization", "performance", "scalability", "security",
            "authentication", "authorization", "encryption", "database",
            "microservices", "containerization", "orchestration", "deployment"
        ]
        
        # Advanced technical terms (higher complexity)
        self.advanced_terms = [
            "distributed systems", "consensus algorithms", "eventual consistency",
            "circuit breaker", "bulkhead pattern", "saga pattern", "cqrs",
            "event sourcing", "machine learning", "artificial intelligence"
        ]
        
        # Cross-domain indicators
        self.cross_domain_indicators = [
            "slack", "github", "issue", "pr", "pull request", "commit",
            "branch", "channel", "thread", "discussion", "documentation"
        ]
    
    def analyze_document_complexity(self, document) -> ComplexityAnalysis:
        """
        Analyze document complexity across 5 dimensions.
        
        Args:
            document: RawDocument instance to analyze
            
        Returns:
            ComplexityAnalysis with complexity scores and recommendations
        """
        try:
            # Get document content and metadata
            content = self._get_document_content(document)
            metadata = document.metadata or {}
            
            # Analyze each dimension
            semantic_complexity = self._analyze_semantic_complexity(content, metadata)
            domain_complexity = self._analyze_domain_complexity(document, metadata, content)
            temporal_complexity = self._analyze_temporal_complexity(document, metadata)
            structural_complexity = self._analyze_structural_complexity(content, metadata)
            contextual_complexity = self._analyze_contextual_complexity(document, metadata, content)
            
            # Calculate overall complexity level
            overall_score = (
                semantic_complexity + domain_complexity + temporal_complexity +
                structural_complexity + contextual_complexity
            ) / 5
            
            overall_level = self._classify_complexity_level(overall_score)
            
            # Generate strategy recommendations
            strategy_recommendations = self._generate_strategy_recommendations(
                semantic_complexity, domain_complexity, temporal_complexity,
                structural_complexity, contextual_complexity, overall_level
            )
            
            # Generate performance hints
            performance_hints = self._generate_performance_hints(
                overall_score, content, metadata
            )
            
            return ComplexityAnalysis(
                semantic_complexity=semantic_complexity,
                domain_complexity=domain_complexity,
                temporal_complexity=temporal_complexity,
                structural_complexity=structural_complexity,
                contextual_complexity=contextual_complexity,
                overall_level=overall_level,
                strategy_recommendations=strategy_recommendations,
                performance_hints=performance_hints
            )
            
        except Exception as e:
            self.logger.error(f"Error analyzing complexity for document {document.id}: {str(e)}")
            # Return default complexity
            return ComplexityAnalysis(
                semantic_complexity=0.5,
                domain_complexity=0.5,
                temporal_complexity=0.5,
                structural_complexity=0.5,
                contextual_complexity=0.5,
                overall_level="MODERATE",
                strategy_recommendations={},
                performance_hints={}
            )
    
    def _get_document_content(self, document) -> str:
        """Get document content from various sources."""
        content = ""
        
        # Try to get content from DocumentContent model
        if hasattr(document, 'document_content') and document.document_content:
            content = document.document_content.content or ""
        
        # Fallback to direct content attribute
        elif hasattr(document, 'content'):
            content = document.content or ""
        
        # Try to get from chunks if no direct content
        elif hasattr(document, 'chunks') and document.chunks.exists():
            chunks = document.chunks.all().order_by('chunk_index')
            content = "\n".join(chunk.text for chunk in chunks)
        
        return content
    
    def _analyze_semantic_complexity(self, content: str, metadata: Dict[str, Any]) -> float:
        """Analyze semantic complexity based on content characteristics."""
        if not content:
            return 0.1
        
        complexity_score = 0.0
        
        # Length factor (longer content is generally more complex)
        content_length = len(content)
        if content_length > 10000:
            complexity_score += 0.4
        elif content_length > 5000:
            complexity_score += 0.3
        elif content_length > 2000:
            complexity_score += 0.2
        elif content_length > 500:
            complexity_score += 0.1
        
        # Technical vocabulary density
        content_lower = content.lower()
        technical_count = sum(1 for term in self.technical_terms if term in content_lower)
        advanced_count = sum(1 for term in self.advanced_terms if term in content_lower)
        
        # Weight advanced terms more heavily
        vocab_score = (technical_count * 0.05) + (advanced_count * 0.1)
        complexity_score += min(vocab_score, 0.4)
        
        # Sentence complexity (average sentence length)
        sentences = re.split(r'[.!?]+', content)
        if sentences:
            words = content.split()
            avg_sentence_length = len(words) / len(sentences)
            if avg_sentence_length > 25:
                complexity_score += 0.2
            elif avg_sentence_length > 20:
                complexity_score += 0.15
            elif avg_sentence_length > 15:
                complexity_score += 0.1
        
        # Code complexity (if content contains code)
        if "```" in content or content.count('\n') > content_length / 50:
            complexity_score += 0.2
        
        return min(complexity_score, 1.0)
    
    def _analyze_domain_complexity(self, document, metadata: Dict[str, Any], content: str) -> float:
        """Analyze domain-specific complexity."""
        complexity_score = 0.0
        
        # Source type complexity
        source_type = document.source.source_type if document.source else "unknown"
        if source_type == "github":
            complexity_score += 0.3  # GitHub content tends to be more technical
        elif source_type in ["slack", "local_slack"]:
            complexity_score += 0.2  # Slack can be conversational or technical
        
        # Content type complexity
        content_type = document.content_type
        complexity_mapping = {
            "code": 0.4,
            "github_pr": 0.4,
            "github_issue": 0.3,
            "slack_message": 0.2,
            "markdown": 0.2,
            "html": 0.2,
            "text": 0.1
        }
        complexity_score += complexity_mapping.get(content_type, 0.1)
        
        # Cross-domain indicators
        if content:
            cross_domain_count = sum(1 for indicator in self.cross_domain_indicators 
                                   if indicator in content.lower())
            complexity_score += min(cross_domain_count * 0.05, 0.3)
        
        # Multi-language content (if applicable)
        if metadata.get("programming_language"):
            complexity_score += 0.1
        
        return min(complexity_score, 1.0)
    
    def _analyze_temporal_complexity(self, document, metadata: Dict[str, Any]) -> float:
        """Analyze temporal complexity."""
        complexity_score = 0.0
        
        # Time-sensitive content indicators
        if metadata.get("is_time_sensitive"):
            complexity_score += 0.4
        
        # Historical context (older content might need more context)
        if hasattr(document, 'fetched_at') and document.fetched_at:
            age_days = (datetime.now().date() - document.fetched_at.date()).days
            if age_days > 365:  # More than a year old
                complexity_score += 0.3
            elif age_days > 90:  # More than 3 months old
                complexity_score += 0.2
            elif age_days > 30:  # More than a month old
                complexity_score += 0.1
        
        # Thread/conversation context
        if metadata.get("thread_id") or metadata.get("conversation_id"):
            complexity_score += 0.2
        
        # Version-specific content
        if metadata.get("version") or metadata.get("commit_sha"):
            complexity_score += 0.1
        
        # Scheduled or recurring content
        if metadata.get("is_recurring") or metadata.get("schedule"):
            complexity_score += 0.1
        
        return min(complexity_score, 1.0)
    
    def _analyze_structural_complexity(self, content: str, metadata: Dict[str, Any]) -> float:
        """Analyze structural complexity."""
        complexity_score = 0.0
        
        if not content:
            return complexity_score
        
        # Document structure indicators
        structure_indicators = {
            "```": 0.2,  # Code blocks
            "#": 0.1,    # Headers (per 5 headers)
            "*": 0.05,   # Lists (per 10 items)
            "|": 0.1,    # Tables (per 5 tables)
            "http": 0.05 # Links (per 10 links)
        }
        
        for indicator, weight in structure_indicators.items():
            count = content.count(indicator)
            if indicator == "#":
                complexity_score += min((count / 5) * weight, 0.3)
            elif indicator == "*":
                complexity_score += min((count / 10) * weight, 0.2)
            elif indicator == "|":
                complexity_score += min((count / 5) * weight, 0.2)
            elif indicator == "http":
                complexity_score += min((count / 10) * weight, 0.2)
            else:
                complexity_score += min(count * weight, 0.3)
        
        # Line count complexity
        line_count = content.count('\n')
        if line_count > 200:
            complexity_score += 0.3
        elif line_count > 100:
            complexity_score += 0.2
        elif line_count > 50:
            complexity_score += 0.1
        
        # Metadata structure complexity
        metadata_complexity = len(metadata) / 20  # Normalize by expected metadata size
        complexity_score += min(metadata_complexity, 0.2)
        
        # File structure (for code files)
        if metadata.get("file_path"):
            path_depth = metadata["file_path"].count("/")
            complexity_score += min(path_depth * 0.02, 0.1)
        
        return min(complexity_score, 1.0)
    
    def _analyze_contextual_complexity(self, document, metadata: Dict[str, Any], content: str) -> float:
        """Analyze contextual complexity."""
        complexity_score = 0.0
        
        # Multi-source context
        if metadata.get("cross_references") or metadata.get("related_documents"):
            complexity_score += 0.3
        
        # User/participant context
        participants = metadata.get("participants", [])
        user_mentions = metadata.get("user_mentions", [])
        if len(participants) > 5 or len(user_mentions) > 3:
            complexity_score += 0.2
        elif len(participants) > 2 or len(user_mentions) > 1:
            complexity_score += 0.1
        
        # Project/repository context
        if metadata.get("project_context") or metadata.get("repository"):
            complexity_score += 0.2
        
        # Quality indicators (high quality content might be more complex)
        quality_score = document.quality_score or 0.5
        if quality_score > 0.8:
            complexity_score += 0.1
        
        # External dependencies
        if content and any(term in content.lower() for term in ["dependency", "import", "require"]):
            complexity_score += 0.1
        
        # Business context
        if metadata.get("business_impact") or metadata.get("stakeholders"):
            complexity_score += 0.1
        
        return min(complexity_score, 1.0)
    
    def _classify_complexity_level(self, overall_score: float) -> str:
        """Classify overall complexity level."""
        if overall_score < 0.3:
            return "SIMPLE"
        elif overall_score < 0.6:
            return "MODERATE"
        elif overall_score < 0.8:
            return "COMPLEX"
        else:
            return "ADAPTIVE"
    
    def _generate_strategy_recommendations(self, semantic: float, domain: float, 
                                         temporal: float, structural: float, 
                                         contextual: float, level: str) -> Dict[str, Any]:
        """Generate strategy recommendations based on complexity analysis."""
        recommendations = {
            "optimal_retrieval_mode": "chunks",  # Default
            "enable_file_retrieval": False,
            "enable_cross_domain": False,
            "enable_multi_step": False,
            "similarity_top_k": 10,
            "rerank_enabled": False,
            "reasoning": []
        }
        
        # High semantic complexity -> file-level retrieval
        if semantic > 0.7:
            recommendations["optimal_retrieval_mode"] = "files_via_content"
            recommendations["enable_file_retrieval"] = True
            recommendations["reasoning"].append("High semantic complexity favors file-level retrieval")
        
        # High domain complexity -> cross-domain search
        if domain > 0.6:
            recommendations["enable_cross_domain"] = True
            recommendations["reasoning"].append("High domain complexity benefits from cross-domain search")
        
        # High contextual complexity -> multi-step reasoning
        if contextual > 0.7:
            recommendations["enable_multi_step"] = True
            recommendations["reasoning"].append("High contextual complexity requires multi-step reasoning")
        
        # Adjust top_k based on complexity
        if level == "ADAPTIVE":
            recommendations["similarity_top_k"] = 20
            recommendations["rerank_enabled"] = True
        elif level == "COMPLEX":
            recommendations["similarity_top_k"] = 15
            recommendations["rerank_enabled"] = True
        elif level == "MODERATE":
            recommendations["similarity_top_k"] = 10
        else:  # SIMPLE
            recommendations["similarity_top_k"] = 5
        
        return recommendations
    
    def _generate_performance_hints(self, overall_score: float, content: str, 
                                  metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Generate performance optimization hints."""
        hints = {
            "estimated_retrieval_time_ms": 1000,  # Base estimate
            "cache_priority": "medium",
            "rerank_boost": 1.0,
            "parallel_processing": False,
            "optimization_notes": []
        }
        
        # Adjust estimates based on complexity
        if overall_score > 0.8:
            hints["estimated_retrieval_time_ms"] = 3000
            hints["cache_priority"] = "high"
            hints["rerank_boost"] = 1.3
            hints["parallel_processing"] = True
            hints["optimization_notes"].append("High complexity - enable parallel processing")
        elif overall_score > 0.6:
            hints["estimated_retrieval_time_ms"] = 2000
            hints["cache_priority"] = "high"
            hints["rerank_boost"] = 1.2
            hints["optimization_notes"].append("Moderate-high complexity - increase cache priority")
        elif overall_score < 0.3:
            hints["estimated_retrieval_time_ms"] = 500
            hints["cache_priority"] = "low"
            hints["optimization_notes"].append("Low complexity - fast retrieval expected")
        
        # Content-based optimizations
        if content and len(content) > 10000:
            hints["optimization_notes"].append("Large content - consider chunking optimization")
        
        if metadata.get("file_path"):
            hints["optimization_notes"].append("File-based content - enable file-level caching")
        
        return hints
