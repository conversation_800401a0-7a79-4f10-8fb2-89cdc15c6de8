"""
File-Level Content Processor for Advanced RAG

This processor prepares documents for file-level retrieval strategies,
generating file summaries, keywords, and metadata for enhanced search.
"""

import logging
import re
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class FileProcessingResult:
    """Result of file-level processing."""
    file_summary: str
    file_keywords: List[str]
    file_metadata: Dict[str, Any]
    is_complete_file: bool
    file_complexity_score: float


class FileContentProcessor:
    """Process documents for file-level retrieval strategies."""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # Programming language extensions
        self.language_extensions = {
            '.py': 'python',
            '.js': 'javascript',
            '.ts': 'typescript',
            '.java': 'java',
            '.cpp': 'cpp',
            '.c': 'c',
            '.go': 'go',
            '.rs': 'rust',
            '.php': 'php',
            '.rb': 'ruby',
            '.swift': 'swift',
            '.kt': 'kotlin',
            '.scala': 'scala',
            '.sh': 'shell',
            '.sql': 'sql',
            '.html': 'html',
            '.css': 'css',
            '.json': 'json',
            '.xml': 'xml',
            '.yaml': 'yaml',
            '.yml': 'yaml',
            '.md': 'markdown',
            '.txt': 'text'
        }
        
        # Technical keywords for extraction
        self.technical_keywords = [
            'api', 'endpoint', 'database', 'server', 'client', 'authentication',
            'authorization', 'security', 'encryption', 'configuration', 'deployment',
            'monitoring', 'logging', 'testing', 'validation', 'optimization',
            'performance', 'scalability', 'architecture', 'framework', 'library'
        ]
    
    def process_for_file_retrieval(self, document) -> FileProcessingResult:
        """
        Prepare document for file-level retrieval.
        
        Args:
            document: RawDocument instance to process
            
        Returns:
            FileProcessingResult with file-level metadata
        """
        try:
            # Get document content and metadata
            content = self._get_document_content(document)
            metadata = document.metadata or {}
            
            # Determine if this is a complete file
            is_complete_file = self._is_complete_file(document, metadata, content)
            
            # Generate file summary
            file_summary = self._generate_file_summary(content, metadata, document)
            
            # Extract keywords
            file_keywords = self._extract_file_keywords(content, metadata)
            
            # Calculate file complexity
            file_complexity_score = self._calculate_file_complexity(content, metadata)
            
            # Generate file metadata
            file_metadata = self._generate_file_metadata(document, metadata, content)
            
            return FileProcessingResult(
                file_summary=file_summary,
                file_keywords=file_keywords,
                file_metadata=file_metadata,
                is_complete_file=is_complete_file,
                file_complexity_score=file_complexity_score
            )
            
        except Exception as e:
            self.logger.error(f"Error processing file for document {document.id}: {str(e)}")
            # Return default result
            return FileProcessingResult(
                file_summary="",
                file_keywords=[],
                file_metadata={},
                is_complete_file=False,
                file_complexity_score=0.5
            )
    
    def _get_document_content(self, document) -> str:
        """Get document content from various sources."""
        content = ""
        
        # Try to get content from DocumentContent model
        if hasattr(document, 'document_content') and document.document_content:
            content = document.document_content.content or ""
        
        # Fallback to direct content attribute
        elif hasattr(document, 'content'):
            content = document.content or ""
        
        # Try to get from chunks if no direct content
        elif hasattr(document, 'chunks') and document.chunks.exists():
            chunks = document.chunks.all().order_by('chunk_index')
            content = "\n".join(chunk.text for chunk in chunks)
        
        return content
    
    def _is_complete_file(self, document, metadata: Dict[str, Any], content: str) -> bool:
        """Determine if document represents a complete file."""
        
        # Check if it's explicitly marked as a complete file
        if metadata.get("is_complete_file"):
            return True
        
        # Check source type and content type
        source_type = document.source.source_type if document.source else ""
        content_type = document.content_type
        
        # GitHub code files are typically complete
        if source_type == "github" and content_type in ["code", "github_code"]:
            return True
        
        # Files with file paths are likely complete
        if metadata.get("file_path"):
            return True
        
        # Check content characteristics
        if content:
            # Look for file-like structure
            if any(indicator in content for indicator in ["import ", "from ", "class ", "function ", "def "]):
                return True
            
            # Check for complete document structure
            if content.count('\n') > 10 and len(content) > 500:
                return True
        
        return False
    
    def _generate_file_summary(self, content: str, metadata: Dict[str, Any], document) -> str:
        """Generate file-level summary for metadata retrieval."""
        
        if not content:
            return f"Document: {document.title}"
        
        # Get basic information
        title = document.title or "Untitled Document"
        content_type = document.content_type
        file_path = metadata.get("file_path", "")
        
        # Detect programming language
        language = self._detect_programming_language(file_path, content)
        
        # Extract key information from content
        key_info = self._extract_key_information(content, language)
        
        # Build summary
        summary_parts = [f"File: {title}"]
        
        if file_path:
            summary_parts.append(f"Path: {file_path}")
        
        if language and language != "text":
            summary_parts.append(f"Language: {language}")
        
        if key_info["functions"]:
            summary_parts.append(f"Functions: {', '.join(key_info['functions'][:5])}")
        
        if key_info["classes"]:
            summary_parts.append(f"Classes: {', '.join(key_info['classes'][:3])}")
        
        if key_info["imports"]:
            summary_parts.append(f"Imports: {', '.join(key_info['imports'][:5])}")
        
        # Add content description
        content_length = len(content)
        line_count = content.count('\n')
        summary_parts.append(f"Size: {content_length} chars, {line_count} lines")
        
        # Add purpose if detectable
        purpose = self._detect_file_purpose(content, metadata)
        if purpose:
            summary_parts.append(f"Purpose: {purpose}")
        
        return " | ".join(summary_parts)
    
    def _extract_file_keywords(self, content: str, metadata: Dict[str, Any]) -> List[str]:
        """Extract keywords for file-level search."""
        keywords = []
        
        if not content:
            return keywords
        
        # Add file path components
        file_path = metadata.get("file_path", "")
        if file_path:
            path_parts = file_path.replace("/", " ").replace("_", " ").replace("-", " ").split()
            keywords.extend([part.lower() for part in path_parts if len(part) > 2])
        
        # Add programming language
        language = self._detect_programming_language(file_path, content)
        if language:
            keywords.append(language)
        
        # Extract technical keywords from content
        content_lower = content.lower()
        for keyword in self.technical_keywords:
            if keyword in content_lower:
                keywords.append(keyword)
        
        # Extract function and class names
        key_info = self._extract_key_information(content, language)
        keywords.extend(key_info["functions"][:10])  # Limit to top 10
        keywords.extend(key_info["classes"][:5])     # Limit to top 5
        
        # Extract domain-specific terms
        domain_terms = self._extract_domain_terms(content)
        keywords.extend(domain_terms[:10])
        
        # Remove duplicates and filter
        keywords = list(set(keywords))
        keywords = [k for k in keywords if len(k) > 2 and k.isalnum()]
        
        return keywords[:20]  # Limit total keywords
    
    def _calculate_file_complexity(self, content: str, metadata: Dict[str, Any]) -> float:
        """Calculate file complexity score."""
        if not content:
            return 0.1
        
        complexity_score = 0.0
        
        # Length-based complexity
        content_length = len(content)
        if content_length > 10000:
            complexity_score += 0.4
        elif content_length > 5000:
            complexity_score += 0.3
        elif content_length > 2000:
            complexity_score += 0.2
        elif content_length > 500:
            complexity_score += 0.1
        
        # Structure-based complexity
        line_count = content.count('\n')
        if line_count > 500:
            complexity_score += 0.3
        elif line_count > 200:
            complexity_score += 0.2
        elif line_count > 100:
            complexity_score += 0.1
        
        # Code complexity (if applicable)
        language = self._detect_programming_language(metadata.get("file_path", ""), content)
        if language and language != "text":
            # Count functions, classes, imports
            key_info = self._extract_key_information(content, language)
            function_count = len(key_info["functions"])
            class_count = len(key_info["classes"])
            import_count = len(key_info["imports"])
            
            complexity_score += min((function_count / 10) * 0.2, 0.2)
            complexity_score += min((class_count / 5) * 0.2, 0.2)
            complexity_score += min((import_count / 20) * 0.1, 0.1)
        
        # Technical content complexity
        technical_count = sum(1 for term in self.technical_keywords if term in content.lower())
        complexity_score += min(technical_count * 0.02, 0.2)
        
        return min(complexity_score, 1.0)
    
    def _generate_file_metadata(self, document, metadata: Dict[str, Any], content: str) -> Dict[str, Any]:
        """Generate comprehensive file metadata."""
        file_metadata = {
            "file_type": self._detect_file_type(metadata.get("file_path", ""), content),
            "programming_language": self._detect_programming_language(metadata.get("file_path", ""), content),
            "content_length": len(content),
            "line_count": content.count('\n'),
            "estimated_tokens": len(content.split()) * 1.3,  # Rough estimate
        }
        
        # Add file path information
        file_path = metadata.get("file_path", "")
        if file_path:
            file_metadata.update({
                "file_path": file_path,
                "file_name": file_path.split("/")[-1] if "/" in file_path else file_path,
                "directory": "/".join(file_path.split("/")[:-1]) if "/" in file_path else "",
                "file_extension": "." + file_path.split(".")[-1] if "." in file_path else ""
            })
        
        # Add structural information
        if content:
            key_info = self._extract_key_information(content, file_metadata.get("programming_language"))
            file_metadata.update({
                "function_count": len(key_info["functions"]),
                "class_count": len(key_info["classes"]),
                "import_count": len(key_info["imports"]),
                "has_main_function": "main" in key_info["functions"],
                "has_tests": any(term in content.lower() for term in ["test", "spec", "assert"])
            })
        
        # Add purpose and category
        file_metadata["purpose"] = self._detect_file_purpose(content, metadata)
        file_metadata["category"] = self._categorize_file(file_metadata, content)
        
        return file_metadata
    
    def _detect_programming_language(self, file_path: str, content: str) -> str:
        """Detect programming language from file path and content."""
        
        # Check file extension first
        if file_path:
            for ext, lang in self.language_extensions.items():
                if file_path.lower().endswith(ext):
                    return lang
        
        # Fallback to content analysis
        if content:
            content_lower = content.lower()
            
            # Language-specific patterns
            if any(pattern in content for pattern in ["def ", "import ", "from ", "class "]):
                return "python"
            elif any(pattern in content for pattern in ["function ", "const ", "let ", "var "]):
                return "javascript"
            elif any(pattern in content for pattern in ["public class", "private ", "import java"]):
                return "java"
            elif any(pattern in content for pattern in ["#include", "int main", "void "]):
                return "c"
        
        return "text"
    
    def _detect_file_type(self, file_path: str, content: str) -> str:
        """Detect general file type."""
        
        if file_path:
            if any(file_path.lower().endswith(ext) for ext in ['.py', '.js', '.java', '.cpp', '.c', '.go']):
                return "source_code"
            elif any(file_path.lower().endswith(ext) for ext in ['.md', '.txt', '.rst']):
                return "documentation"
            elif any(file_path.lower().endswith(ext) for ext in ['.json', '.yaml', '.yml', '.xml']):
                return "configuration"
            elif any(file_path.lower().endswith(ext) for ext in ['.html', '.css']):
                return "web"
        
        # Content-based detection
        if content:
            if "```" in content or any(pattern in content for pattern in ["def ", "function ", "class "]):
                return "source_code"
            elif any(pattern in content.lower() for pattern in ["# ", "## ", "### "]):
                return "documentation"
        
        return "text"
    
    def _extract_key_information(self, content: str, language: str) -> Dict[str, List[str]]:
        """Extract key structural information from content."""
        info = {
            "functions": [],
            "classes": [],
            "imports": []
        }
        
        if not content:
            return info
        
        lines = content.split('\n')
        
        # Language-specific extraction
        if language == "python":
            for line in lines:
                line = line.strip()
                if line.startswith("def "):
                    func_name = line.split("(")[0].replace("def ", "").strip()
                    info["functions"].append(func_name)
                elif line.startswith("class "):
                    class_name = line.split("(")[0].split(":")[0].replace("class ", "").strip()
                    info["classes"].append(class_name)
                elif line.startswith("import ") or line.startswith("from "):
                    import_name = line.split()[1].split(".")[0]
                    info["imports"].append(import_name)
        
        elif language in ["javascript", "typescript"]:
            for line in lines:
                line = line.strip()
                if "function " in line:
                    match = re.search(r'function\s+(\w+)', line)
                    if match:
                        info["functions"].append(match.group(1))
                elif "class " in line:
                    match = re.search(r'class\s+(\w+)', line)
                    if match:
                        info["classes"].append(match.group(1))
                elif line.startswith("import "):
                    match = re.search(r'import.*from\s+[\'"]([^\'"]+)', line)
                    if match:
                        info["imports"].append(match.group(1))
        
        return info
    
    def _extract_domain_terms(self, content: str) -> List[str]:
        """Extract domain-specific terms from content."""
        terms = []
        
        if not content:
            return terms
        
        # Common domain patterns
        domain_patterns = {
            r'\b[A-Z][a-z]+(?:[A-Z][a-z]+)*\b': 'CamelCase terms',  # CamelCase
            r'\b[a-z]+_[a-z_]+\b': 'snake_case terms',              # snake_case
            r'\b[A-Z_]{3,}\b': 'CONSTANTS',                         # CONSTANTS
        }
        
        for pattern, description in domain_patterns.items():
            matches = re.findall(pattern, content)
            terms.extend(matches[:5])  # Limit per pattern
        
        return terms
    
    def _detect_file_purpose(self, content: str, metadata: Dict[str, Any]) -> str:
        """Detect the purpose/role of the file."""
        
        if not content:
            return "unknown"
        
        content_lower = content.lower()
        file_path = metadata.get("file_path", "").lower()
        
        # Purpose detection patterns
        if any(term in file_path for term in ["test", "spec"]):
            return "testing"
        elif any(term in file_path for term in ["config", "settings"]):
            return "configuration"
        elif any(term in file_path for term in ["util", "helper", "common"]):
            return "utility"
        elif any(term in file_path for term in ["model", "entity"]):
            return "data_model"
        elif any(term in file_path for term in ["service", "api"]):
            return "service"
        elif any(term in file_path for term in ["view", "template", "ui"]):
            return "interface"
        elif "main" in content_lower or "entry" in content_lower:
            return "entry_point"
        elif any(term in content_lower for term in ["readme", "documentation", "guide"]):
            return "documentation"
        
        return "implementation"
    
    def _categorize_file(self, file_metadata: Dict[str, Any], content: str) -> str:
        """Categorize file based on metadata and content."""
        
        file_type = file_metadata.get("file_type", "text")
        purpose = file_metadata.get("purpose", "unknown")
        language = file_metadata.get("programming_language", "text")
        
        # High-level categorization
        if file_type == "source_code":
            if purpose == "testing":
                return "test_code"
            elif purpose in ["service", "api"]:
                return "business_logic"
            elif purpose == "configuration":
                return "config_code"
            else:
                return "application_code"
        
        elif file_type == "documentation":
            return "documentation"
        
        elif file_type == "configuration":
            return "configuration"
        
        else:
            return "general"
