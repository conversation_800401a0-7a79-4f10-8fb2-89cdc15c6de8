"""
Domain Classification Service for Advanced RAG

This service classifies documents into data domains for intelligent routing
and cross-domain intelligence in the advanced agentic retrieval system.
"""

import logging
import re
from typing import Dict, Any, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class DataDomain(Enum):
    """Data domains for cross-domain intelligence."""
    SLACK_CONVERSATIONS = "slack_conversations"
    SLACK_ENGINEERING = "slack_engineering"
    SLACK_SUPPORT = "slack_support"
    GITHUB_CODE = "github_code"
    GITHUB_ISSUES = "github_issues"
    GITHUB_WIKI = "github_wiki"
    GITHUB_DISCUSSIONS = "github_discussions"
    DOCUMENTATION = "documentation"
    MEETING_NOTES = "meeting_notes"
    CUSTOMER_DOCS = "customer_docs"


@dataclass
class DomainClassification:
    """Result of domain classification."""
    primary_domain: DataDomain
    confidence: float
    secondary_domains: List[DataDomain]
    routing_metadata: Dict[str, Any]


class DomainClassificationService:
    """Service to classify documents into data domains for intelligent routing."""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # Technical indicators for domain classification
        self.technical_indicators = [
            "api", "endpoint", "database", "server", "deployment", "code",
            "function", "class", "method", "variable", "error", "bug",
            "fix", "implementation", "algorithm", "architecture", "framework",
            "library", "configuration", "security", "authentication"
        ]
        
        # Code indicators
        self.code_indicators = [
            "```", "function", "class", "def ", "import ", "from ",
            "git", "commit", "branch", "pull request", "merge", "repository"
        ]
        
        # Issue/support indicators
        self.issue_indicators = [
            "issue", "bug", "problem", "error", "fix", "resolve",
            "ticket", "#", "reported", "reproduce", "troubleshoot"
        ]
        
        # Meeting/discussion indicators
        self.meeting_indicators = [
            "meeting", "discussion", "agenda", "minutes", "action items",
            "decision", "follow up", "next steps", "attendees"
        ]
    
    def classify_document(self, document) -> DomainClassification:
        """
        Classify document into primary and secondary domains.
        
        Args:
            document: RawDocument instance to classify
            
        Returns:
            DomainClassification with domain assignments
        """
        try:
            # Get document metadata and content
            metadata = document.metadata or {}
            content_type = document.content_type
            source_type = document.source.source_type if document.source else "unknown"
            
            # Get content if available
            content = ""
            if hasattr(document, 'document_content') and document.document_content:
                content = document.document_content.content or ""
            elif hasattr(document, 'content'):
                content = document.content or ""
            
            # Classify based on source type and content
            primary_domain, confidence = self._classify_primary_domain(
                source_type, content_type, metadata, content
            )
            
            # Identify secondary domains
            secondary_domains = self._identify_secondary_domains(
                document, primary_domain, content, metadata
            )
            
            # Generate routing metadata
            routing_metadata = self._generate_routing_metadata(
                document, primary_domain, secondary_domains, content, metadata
            )
            
            return DomainClassification(
                primary_domain=primary_domain,
                confidence=confidence,
                secondary_domains=secondary_domains,
                routing_metadata=routing_metadata
            )
            
        except Exception as e:
            self.logger.error(f"Error classifying document {document.id}: {str(e)}")
            # Return default classification
            return DomainClassification(
                primary_domain=DataDomain.DOCUMENTATION,
                confidence=0.5,
                secondary_domains=[],
                routing_metadata={}
            )
    
    def _classify_primary_domain(self, source_type: str, content_type: str, 
                               metadata: Dict[str, Any], content: str) -> Tuple[DataDomain, float]:
        """Classify primary domain based on source and content type."""
        
        # Slack classification
        if source_type in ["slack", "local_slack"]:
            channel_name = metadata.get("channel_name", "").lower()
            channel_id = metadata.get("channel_id", "")
            
            # Engineering channels
            if any(term in channel_name for term in ["engineering", "dev", "tech", "code"]):
                return DataDomain.SLACK_ENGINEERING, 0.9
            
            # Support channels
            elif any(term in channel_name for term in ["support", "help", "customer", "issue"]):
                return DataDomain.SLACK_SUPPORT, 0.9
            
            # Check content for technical indicators
            elif self._has_technical_content(content):
                return DataDomain.SLACK_ENGINEERING, 0.7
            
            # Check content for support indicators
            elif self._has_support_content(content):
                return DataDomain.SLACK_SUPPORT, 0.7
            
            # Default to conversations
            else:
                return DataDomain.SLACK_CONVERSATIONS, 0.8
        
        # GitHub classification
        elif source_type == "github":
            if content_type in ["code", "github_code"]:
                return DataDomain.GITHUB_CODE, 0.95
            elif content_type in ["github_issue", "issue"]:
                return DataDomain.GITHUB_ISSUES, 0.95
            elif content_type in ["github_pr", "pull_request"]:
                return DataDomain.GITHUB_CODE, 0.9
            elif content_type == "github_wiki":
                return DataDomain.GITHUB_WIKI, 0.95
            elif content_type == "github_discussion":
                return DataDomain.GITHUB_DISCUSSIONS, 0.95
            else:
                return DataDomain.GITHUB_CODE, 0.7
        
        # Document classification based on content
        elif self._has_meeting_content(content):
            return DataDomain.MEETING_NOTES, 0.8
        
        elif self._has_customer_content(content, metadata):
            return DataDomain.CUSTOMER_DOCS, 0.8
        
        # Default classification
        else:
            return DataDomain.DOCUMENTATION, 0.6
    
    def _identify_secondary_domains(self, document, primary_domain: DataDomain, 
                                  content: str, metadata: Dict[str, Any]) -> List[DataDomain]:
        """Identify secondary domains based on content analysis."""
        secondary_domains = []
        
        # Look for technical content in non-technical domains
        if primary_domain in [DataDomain.SLACK_CONVERSATIONS, DataDomain.SLACK_SUPPORT]:
            if self._has_technical_content(content):
                secondary_domains.append(DataDomain.SLACK_ENGINEERING)
        
        # Look for code references in non-code domains
        if primary_domain != DataDomain.GITHUB_CODE:
            if self._has_code_references(content):
                secondary_domains.append(DataDomain.GITHUB_CODE)
        
        # Look for issue references
        if primary_domain != DataDomain.GITHUB_ISSUES:
            if self._has_issue_references(content):
                secondary_domains.append(DataDomain.GITHUB_ISSUES)
        
        # Look for documentation references
        if primary_domain != DataDomain.DOCUMENTATION:
            if self._has_documentation_references(content):
                secondary_domains.append(DataDomain.DOCUMENTATION)
        
        # Look for meeting content
        if primary_domain != DataDomain.MEETING_NOTES:
            if self._has_meeting_content(content):
                secondary_domains.append(DataDomain.MEETING_NOTES)
        
        return secondary_domains
    
    def _generate_routing_metadata(self, document, primary_domain: DataDomain,
                                 secondary_domains: List[DataDomain], content: str,
                                 metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Generate routing metadata for intelligent query routing."""
        
        routing_metadata = {
            "primary_intent": self._detect_primary_intent(content, primary_domain),
            "query_patterns": self._extract_query_patterns(content, metadata),
            "workflow_context": self._detect_workflow_context(content, metadata),
            "technical_level": self._assess_technical_level(content),
            "cross_domain_hints": [domain.value for domain in secondary_domains],
            "content_indicators": self._extract_content_indicators(content),
            "routing_confidence": self._calculate_routing_confidence(primary_domain, content, metadata)
        }
        
        return routing_metadata
    
    def _has_technical_content(self, content: str) -> bool:
        """Check if content has technical indicators."""
        if not content:
            return False
        content_lower = content.lower()
        return any(indicator in content_lower for indicator in self.technical_indicators)
    
    def _has_support_content(self, content: str) -> bool:
        """Check if content has support indicators."""
        if not content:
            return False
        content_lower = content.lower()
        support_terms = ["help", "support", "issue", "problem", "error", "bug", "fix"]
        return any(term in content_lower for term in support_terms)
    
    def _has_code_references(self, content: str) -> bool:
        """Check if content references code."""
        if not content:
            return False
        return any(indicator in content for indicator in self.code_indicators)
    
    def _has_issue_references(self, content: str) -> bool:
        """Check if content references issues."""
        if not content:
            return False
        content_lower = content.lower()
        return any(indicator in content_lower for indicator in self.issue_indicators)
    
    def _has_documentation_references(self, content: str) -> bool:
        """Check if content references documentation."""
        if not content:
            return False
        content_lower = content.lower()
        doc_terms = ["documentation", "docs", "guide", "manual", "readme", "wiki"]
        return any(term in content_lower for term in doc_terms)
    
    def _has_meeting_content(self, content: str) -> bool:
        """Check if content has meeting indicators."""
        if not content:
            return False
        content_lower = content.lower()
        return any(indicator in content_lower for indicator in self.meeting_indicators)
    
    def _has_customer_content(self, content: str, metadata: Dict[str, Any]) -> bool:
        """Check if content is customer-facing."""
        if not content:
            return False
        content_lower = content.lower()
        customer_terms = ["customer", "client", "user", "public", "external", "faq"]
        return any(term in content_lower for term in customer_terms)
    
    def _detect_primary_intent(self, content: str, domain: DataDomain) -> str:
        """Detect primary intent based on content and domain."""
        if not content:
            return "informational"
        
        content_lower = content.lower()
        
        if domain in [DataDomain.GITHUB_CODE, DataDomain.SLACK_ENGINEERING]:
            return "technical"
        elif domain in [DataDomain.SLACK_SUPPORT, DataDomain.GITHUB_ISSUES]:
            return "support"
        elif "meeting" in content_lower or "discussion" in content_lower:
            return "conversational"
        elif any(term in content_lower for term in ["how to", "guide", "tutorial"]):
            return "instructional"
        else:
            return "informational"
    
    def _extract_query_patterns(self, content: str, metadata: Dict[str, Any]) -> List[str]:
        """Extract potential query patterns from content."""
        patterns = []
        
        if not content:
            return patterns
        
        content_lower = content.lower()
        
        # Technical patterns
        pattern_mapping = {
            "deployment": ["deploy", "deployment", "release"],
            "authentication": ["auth", "authentication", "login", "oauth"],
            "api": ["api", "endpoint", "rest", "graphql"],
            "database": ["database", "db", "sql", "query"],
            "configuration": ["config", "configuration", "settings"],
            "testing": ["test", "testing", "qa", "quality"],
            "monitoring": ["monitor", "monitoring", "metrics", "logs"],
            "security": ["security", "secure", "vulnerability", "encryption"]
        }
        
        for pattern, terms in pattern_mapping.items():
            if any(term in content_lower for term in terms):
                patterns.append(pattern)
        
        # Add metadata-based patterns
        if metadata.get("file_path"):
            file_path = metadata["file_path"].lower()
            if "config" in file_path:
                patterns.append("configuration")
            if "test" in file_path:
                patterns.append("testing")
        
        return patterns
    
    def _detect_workflow_context(self, content: str, metadata: Dict[str, Any]) -> str:
        """Detect workflow context."""
        if not content:
            return "general"
        
        content_lower = content.lower()
        
        workflow_mapping = {
            "development": ["develop", "code", "implement", "programming"],
            "deployment": ["deploy", "release", "production", "staging"],
            "testing": ["test", "qa", "quality", "validation"],
            "support": ["support", "help", "issue", "troubleshoot"],
            "planning": ["plan", "planning", "roadmap", "strategy"],
            "review": ["review", "feedback", "approval", "assessment"]
        }
        
        for workflow, terms in workflow_mapping.items():
            if any(term in content_lower for term in terms):
                return workflow
        
        return "general"
    
    def _assess_technical_level(self, content: str) -> str:
        """Assess technical complexity level."""
        if not content:
            return "non-technical"
        
        content_lower = content.lower()
        
        technical_count = sum(1 for term in self.technical_indicators if term in content_lower)
        
        if technical_count >= 5:
            return "expert"
        elif technical_count >= 3:
            return "intermediate"
        elif technical_count >= 1:
            return "basic"
        else:
            return "non-technical"
    
    def _extract_content_indicators(self, content: str) -> Dict[str, bool]:
        """Extract content type indicators."""
        if not content:
            return {}
        
        return {
            "has_code": self._has_code_references(content),
            "has_technical_content": self._has_technical_content(content),
            "has_issues": self._has_issue_references(content),
            "has_documentation": self._has_documentation_references(content),
            "has_meeting_content": self._has_meeting_content(content),
            "has_support_content": self._has_support_content(content)
        }
    
    def _calculate_routing_confidence(self, primary_domain: DataDomain, 
                                    content: str, metadata: Dict[str, Any]) -> float:
        """Calculate confidence score for routing decisions."""
        confidence = 0.5  # Base confidence
        
        # Increase confidence based on strong indicators
        if self._has_technical_content(content):
            confidence += 0.2
        
        if metadata.get("source_type") in ["github", "slack"]:
            confidence += 0.2
        
        if len(content) > 100:  # Sufficient content for analysis
            confidence += 0.1
        
        return min(confidence, 1.0)
