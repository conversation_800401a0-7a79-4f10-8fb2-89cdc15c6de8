"""
Enhanced Slack Ingestion Service for Advanced RAG

This service extends Slack ingestion with advanced metadata for:
- Domain classification (engineering vs support vs general)
- Conversation complexity analysis
- Technical content scoring
- Cross-domain reference extraction
"""

import logging
import re
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class EnhancedSlackIngestion:
    """Enhanced Slack ingestion with advanced metadata for RAG."""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # Channel classification patterns
        self.engineering_channels = [
            'engineering', 'dev', 'development', 'tech', 'technical', 'code',
            'backend', 'frontend', 'infrastructure', 'devops', 'platform',
            'architecture', 'api', 'database', 'deployment'
        ]
        
        self.support_channels = [
            'support', 'help', 'customer', 'issue', 'bug', 'problem',
            'troubleshoot', 'incident', 'outage', 'maintenance', 'qa',
            'quality', 'testing'
        ]
        
        # Technical indicators for content analysis
        self.technical_indicators = [
            'api', 'endpoint', 'database', 'server', 'deployment', 'code',
            'function', 'class', 'method', 'variable', 'error', 'bug',
            'fix', 'implementation', 'algorithm', 'architecture', 'framework',
            'library', 'configuration', 'security', 'authentication', 'docker',
            'kubernetes', 'aws', 'cloud', 'microservice', 'repository', 'git'
        ]
        
        # Support indicators
        self.support_indicators = [
            'help', 'issue', 'problem', 'error', 'broken', 'not working',
            'troubleshoot', 'debug', 'fix', 'resolve', 'solution', 'workaround',
            'customer', 'user', 'report', 'complaint', 'feedback'
        ]
        
        # Conversation complexity indicators
        self.complexity_indicators = [
            'architecture', 'design', 'implementation', 'optimization',
            'performance', 'scalability', 'security', 'integration',
            'migration', 'refactor', 'technical debt', 'best practice'
        ]
    
    def enhance_slack_document(self, document: Dict[str, Any]) -> Dict[str, Any]:
        """
        Add Slack-specific advanced metadata for RAG features.
        
        Args:
            document: Slack document data
            
        Returns:
            Enhanced document with advanced metadata
        """
        try:
            content = document.get('content', '')
            metadata = document.get('metadata', {})
            
            # Initialize enhanced metadata
            enhanced_metadata = metadata.copy()
            
            # Determine Slack domain
            data_domain = self._classify_slack_domain(metadata, content)
            enhanced_metadata['data_domain'] = data_domain
            
            # Add conversation analysis
            conversation_metadata = self._analyze_conversation(content, metadata)
            enhanced_metadata.update(conversation_metadata)
            
            # Add technical content analysis
            technical_metadata = self._analyze_technical_content(content)
            enhanced_metadata.update(technical_metadata)
            
            # Add cross-domain references
            cross_domain_metadata = self._extract_cross_domain_references(content)
            enhanced_metadata.update(cross_domain_metadata)
            
            # Add engagement and quality metrics
            engagement_metadata = self._analyze_engagement(metadata, content)
            enhanced_metadata.update(engagement_metadata)
            
            # Add temporal context
            temporal_metadata = self._analyze_temporal_context(metadata)
            enhanced_metadata.update(temporal_metadata)
            
            # Update document
            document['metadata'] = enhanced_metadata
            
            return document
            
        except Exception as e:
            self.logger.error(f"Error enhancing Slack document: {str(e)}")
            return document
    
    def _classify_slack_domain(self, metadata: Dict[str, Any], content: str) -> str:
        """Classify Slack message into appropriate domain."""
        
        channel_name = metadata.get('channel_name', '').lower()
        channel_id = metadata.get('channel_id', '')
        
        # Check channel name patterns
        if any(pattern in channel_name for pattern in self.engineering_channels):
            return 'SLACK_ENGINEERING'
        
        if any(pattern in channel_name for pattern in self.support_channels):
            return 'SLACK_SUPPORT'
        
        # Check content for domain indicators
        if content:
            content_lower = content.lower()
            
            # Count technical vs support indicators
            technical_count = sum(1 for indicator in self.technical_indicators 
                                if indicator in content_lower)
            support_count = sum(1 for indicator in self.support_indicators 
                              if indicator in content_lower)
            
            if technical_count > support_count and technical_count > 2:
                return 'SLACK_ENGINEERING'
            elif support_count > technical_count and support_count > 1:
                return 'SLACK_SUPPORT'
        
        # Default to conversations
        return 'SLACK_CONVERSATIONS'
    
    def _analyze_conversation(self, content: str, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze conversation characteristics."""
        
        analysis = {
            'conversation_complexity': self._calculate_conversation_complexity(content),
            'message_type': self._classify_message_type(content, metadata),
            'urgency_level': self._detect_urgency_level(content),
            'discussion_depth': self._assess_discussion_depth(metadata),
            'participant_engagement': self._analyze_participant_engagement(metadata)
        }
        
        return analysis
    
    def _analyze_technical_content(self, content: str) -> Dict[str, Any]:
        """Analyze technical aspects of content."""
        
        if not content:
            return {
                'technical_content_score': 0.0,
                'technical_topics': [],
                'code_snippets': [],
                'technical_level': 'non-technical'
            }
        
        content_lower = content.lower()
        
        # Calculate technical content score
        technical_count = sum(1 for indicator in self.technical_indicators 
                            if indicator in content_lower)
        words = len(content.split())
        technical_density = technical_count / max(words / 50, 1)  # Per 50 words
        technical_score = min(technical_density, 1.0)
        
        # Extract technical topics
        technical_topics = [indicator for indicator in self.technical_indicators 
                          if indicator in content_lower][:10]
        
        # Extract code snippets
        code_snippets = self._extract_code_snippets(content)
        
        # Assess technical level
        technical_level = self._assess_technical_level(technical_score, content)
        
        return {
            'technical_content_score': technical_score,
            'technical_topics': technical_topics,
            'code_snippets': code_snippets,
            'technical_level': technical_level,
            'has_code_blocks': bool(re.search(r'```', content)),
            'has_links': bool(re.search(r'https?://', content)),
            'mentions_tools': self._extract_tool_mentions(content)
        }
    
    def _extract_cross_domain_references(self, content: str) -> Dict[str, Any]:
        """Extract references to other domains and systems."""
        
        if not content:
            return {'cross_domain_references': []}
        
        references = []
        content_lower = content.lower()
        
        # GitHub references
        github_patterns = [
            r'github\.com/[\w-]+/[\w-]+',
            r'gh-\d+',
            r'issue #?\d+',
            r'pr #?\d+',
            r'pull request #?\d+'
        ]
        
        for pattern in github_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            references.extend([f"github_{match}" for match in matches])
        
        # Documentation references
        doc_patterns = [
            r'docs?\.[\w.-]+',
            r'wiki\.[\w.-]+',
            r'confluence\.[\w.-]+',
            r'notion\.[\w.-]+'
        ]
        
        for pattern in doc_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            references.extend([f"docs_{match}" for match in matches])
        
        # Ticket/Issue system references
        ticket_patterns = [
            r'jira-\d+',
            r'ticket #?\d+',
            r'incident #?\d+'
        ]
        
        for pattern in ticket_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            references.extend([f"ticket_{match}" for match in matches])
        
        # Service/System references
        service_mentions = self._extract_service_mentions(content)
        references.extend([f"service_{service}" for service in service_mentions])
        
        return {
            'cross_domain_references': references[:20],  # Limit to 20 references
            'external_links': self._extract_external_links(content),
            'internal_references': self._extract_internal_references(content)
        }
    
    def _analyze_engagement(self, metadata: Dict[str, Any], content: str) -> Dict[str, Any]:
        """Analyze engagement metrics and quality indicators."""
        
        # Get engagement metrics from metadata
        reactions = metadata.get('reactions', [])
        replies = metadata.get('replies', [])
        thread_length = len(replies) if replies else 0
        
        # Calculate engagement score
        engagement_score = 0.0
        
        # Reaction-based engagement
        if reactions:
            total_reactions = sum(reaction.get('count', 0) for reaction in reactions)
            engagement_score += min(total_reactions * 0.1, 0.4)
        
        # Reply-based engagement
        if thread_length > 0:
            engagement_score += min(thread_length * 0.05, 0.3)
        
        # Content quality indicators
        quality_score = self._calculate_content_quality(content)
        
        return {
            'engagement_score': min(engagement_score, 1.0),
            'thread_length': thread_length,
            'reaction_count': len(reactions),
            'total_reactions': sum(reaction.get('count', 0) for reaction in reactions) if reactions else 0,
            'content_quality': quality_score,
            'is_thread_starter': metadata.get('thread_ts') == metadata.get('ts'),
            'has_attachments': bool(metadata.get('files', [])),
            'message_importance': self._assess_message_importance(content, metadata)
        }
    
    def _analyze_temporal_context(self, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze temporal context of the message."""
        
        timestamp = metadata.get('ts')
        if not timestamp:
            return {'temporal_context': {}}
        
        try:
            # Convert timestamp to datetime
            if isinstance(timestamp, str):
                if '.' in timestamp:
                    dt = datetime.fromtimestamp(float(timestamp))
                else:
                    dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            else:
                dt = datetime.fromtimestamp(float(timestamp))
            
            now = datetime.now()
            age_hours = (now - dt).total_seconds() / 3600
            
            # Determine time-based characteristics
            is_recent = age_hours < 24
            is_business_hours = 9 <= dt.hour <= 17
            is_weekend = dt.weekday() >= 5
            
            # Determine urgency based on timing
            urgency_context = 'normal'
            if not is_business_hours or is_weekend:
                urgency_context = 'off_hours'
            if age_hours < 1:
                urgency_context = 'immediate'
            
            return {
                'temporal_context': {
                    'age_hours': age_hours,
                    'is_recent': is_recent,
                    'is_business_hours': is_business_hours,
                    'is_weekend': is_weekend,
                    'urgency_context': urgency_context,
                    'hour_of_day': dt.hour,
                    'day_of_week': dt.weekday()
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing temporal context: {str(e)}")
            return {'temporal_context': {}}
    
    def _calculate_conversation_complexity(self, content: str) -> float:
        """Calculate conversation complexity score."""
        
        if not content:
            return 0.0
        
        complexity = 0.0
        content_lower = content.lower()
        
        # Length-based complexity
        word_count = len(content.split())
        if word_count > 200:
            complexity += 0.3
        elif word_count > 100:
            complexity += 0.2
        elif word_count > 50:
            complexity += 0.1
        
        # Technical complexity
        technical_count = sum(1 for indicator in self.technical_indicators 
                            if indicator in content_lower)
        complexity += min(technical_count * 0.05, 0.3)
        
        # Complexity indicators
        complexity_count = sum(1 for indicator in self.complexity_indicators 
                             if indicator in content_lower)
        complexity += min(complexity_count * 0.1, 0.4)
        
        # Question complexity
        question_count = content.count('?')
        complexity += min(question_count * 0.05, 0.2)
        
        return min(complexity, 1.0)
    
    def _classify_message_type(self, content: str, metadata: Dict[str, Any]) -> str:
        """Classify the type of message."""
        
        if not content:
            return 'unknown'
        
        content_lower = content.lower()
        
        # Question
        if '?' in content or any(word in content_lower for word in ['how', 'what', 'why', 'when', 'where']):
            return 'question'
        
        # Announcement
        if any(word in content_lower for word in ['announce', 'announcement', 'fyi', 'heads up']):
            return 'announcement'
        
        # Problem/Issue
        if any(word in content_lower for word in ['problem', 'issue', 'error', 'broken', 'not working']):
            return 'problem_report'
        
        # Solution/Answer
        if any(word in content_lower for word in ['solution', 'fix', 'resolved', 'answer', 'try this']):
            return 'solution'
        
        # Discussion
        if any(word in content_lower for word in ['think', 'opinion', 'suggest', 'propose']):
            return 'discussion'
        
        # Update/Status
        if any(word in content_lower for word in ['update', 'status', 'progress', 'done', 'completed']):
            return 'update'
        
        return 'general'
    
    def _detect_urgency_level(self, content: str) -> str:
        """Detect urgency level from content."""
        
        if not content:
            return 'normal'
        
        content_lower = content.lower()
        
        # High urgency indicators
        high_urgency = ['urgent', 'asap', 'emergency', 'critical', 'immediately', 'now', 'broken', 'down']
        if any(indicator in content_lower for indicator in high_urgency):
            return 'high'
        
        # Medium urgency indicators
        medium_urgency = ['soon', 'quickly', 'important', 'priority', 'issue', 'problem']
        if any(indicator in content_lower for indicator in medium_urgency):
            return 'medium'
        
        return 'normal'
    
    def _assess_discussion_depth(self, metadata: Dict[str, Any]) -> str:
        """Assess the depth of discussion based on thread length."""
        
        replies = metadata.get('replies', [])
        thread_length = len(replies) if replies else 0
        
        if thread_length > 20:
            return 'deep'
        elif thread_length > 10:
            return 'moderate'
        elif thread_length > 3:
            return 'shallow'
        else:
            return 'none'
    
    def _analyze_participant_engagement(self, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze participant engagement in the conversation."""
        
        replies = metadata.get('replies', [])
        reactions = metadata.get('reactions', [])
        
        # Count unique participants
        participants = set()
        if replies:
            for reply in replies:
                if 'user' in reply:
                    participants.add(reply['user'])
        
        # Count total reactions
        total_reactions = sum(reaction.get('count', 0) for reaction in reactions) if reactions else 0
        
        return {
            'unique_participants': len(participants),
            'total_replies': len(replies) if replies else 0,
            'total_reactions': total_reactions,
            'engagement_ratio': total_reactions / max(len(participants), 1) if participants else 0
        }
    
    def _extract_code_snippets(self, content: str) -> List[str]:
        """Extract code snippets from content."""
        
        if not content:
            return []
        
        # Extract code blocks
        code_blocks = re.findall(r'```(\w+)?\n(.*?)\n```', content, re.DOTALL)
        snippets = []
        
        for lang, code in code_blocks:
            snippets.append({
                'language': lang or 'unknown',
                'code': code.strip()[:200]  # Limit code length
            })
        
        # Extract inline code
        inline_code = re.findall(r'`([^`]+)`', content)
        for code in inline_code[:5]:  # Limit to 5 inline snippets
            snippets.append({
                'language': 'inline',
                'code': code
            })
        
        return snippets[:10]  # Limit total snippets
    
    def _assess_technical_level(self, technical_score: float, content: str) -> str:
        """Assess technical level of content."""
        
        if technical_score > 0.7:
            return 'expert'
        elif technical_score > 0.4:
            return 'intermediate'
        elif technical_score > 0.1:
            return 'basic'
        else:
            return 'non-technical'
    
    def _extract_tool_mentions(self, content: str) -> List[str]:
        """Extract mentions of tools and technologies."""
        
        if not content:
            return []
        
        tools = [
            'docker', 'kubernetes', 'aws', 'azure', 'gcp', 'jenkins', 'gitlab',
            'github', 'jira', 'confluence', 'slack', 'teams', 'zoom', 'postgres',
            'mysql', 'redis', 'mongodb', 'elasticsearch', 'kafka', 'rabbitmq',
            'nginx', 'apache', 'node', 'python', 'java', 'javascript', 'react',
            'angular', 'vue', 'django', 'flask', 'spring', 'express'
        ]
        
        content_lower = content.lower()
        mentioned_tools = [tool for tool in tools if tool in content_lower]
        
        return mentioned_tools[:10]  # Limit to 10 tools
    
    def _extract_service_mentions(self, content: str) -> List[str]:
        """Extract mentions of services and systems."""
        
        if not content:
            return []
        
        # Common service patterns
        service_patterns = [
            r'(\w+)-service',
            r'(\w+)-api',
            r'(\w+)\.service',
            r'service-(\w+)'
        ]
        
        services = []
        for pattern in service_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            services.extend(matches)
        
        return list(set(services))[:10]  # Deduplicate and limit
    
    def _extract_external_links(self, content: str) -> List[str]:
        """Extract external links from content."""
        
        if not content:
            return []
        
        # Extract URLs
        url_pattern = r'https?://[^\s<>"{}|\\^`\[\]]+'
        urls = re.findall(url_pattern, content)
        
        return urls[:10]  # Limit to 10 URLs
    
    def _extract_internal_references(self, content: str) -> List[str]:
        """Extract internal references (channels, users, etc.)."""
        
        if not content:
            return []
        
        references = []
        
        # Channel references
        channel_refs = re.findall(r'<#([A-Z0-9]+)\|([^>]+)>', content)
        references.extend([f"channel_{ref[1]}" for ref in channel_refs])
        
        # User mentions
        user_refs = re.findall(r'<@([A-Z0-9]+)>', content)
        references.extend([f"user_{ref}" for ref in user_refs])
        
        return references[:20]  # Limit to 20 references
    
    def _calculate_content_quality(self, content: str) -> float:
        """Calculate content quality score."""
        
        if not content:
            return 0.0
        
        quality = 0.0
        
        # Length quality
        word_count = len(content.split())
        if 10 <= word_count <= 500:
            quality += 0.3
        elif word_count > 5:
            quality += 0.1
        
        # Structure quality
        if '\n' in content:  # Has line breaks
            quality += 0.1
        
        if any(char in content for char in ['.', '!', '?']):  # Has punctuation
            quality += 0.1
        
        # Content richness
        if re.search(r'https?://', content):  # Has links
            quality += 0.1
        
        if re.search(r'```', content):  # Has code blocks
            quality += 0.2
        
        # Readability
        sentences = content.split('.')
        if sentences:
            avg_sentence_length = sum(len(s.split()) for s in sentences) / len(sentences)
            if 5 <= avg_sentence_length <= 25:  # Reasonable sentence length
                quality += 0.2
        
        return min(quality, 1.0)
    
    def _assess_message_importance(self, content: str, metadata: Dict[str, Any]) -> str:
        """Assess the importance of the message."""
        
        # Check for importance indicators
        if not content:
            return 'low'
        
        content_lower = content.lower()
        
        # High importance indicators
        high_importance = [
            'critical', 'urgent', 'important', 'emergency', 'outage',
            'security', 'breach', 'incident', 'alert', 'announcement'
        ]
        
        if any(indicator in content_lower for indicator in high_importance):
            return 'high'
        
        # Medium importance indicators
        medium_importance = [
            'issue', 'problem', 'bug', 'error', 'question', 'help',
            'update', 'change', 'new', 'release'
        ]
        
        if any(indicator in content_lower for indicator in medium_importance):
            return 'medium'
        
        # Check engagement as importance indicator
        reactions = metadata.get('reactions', [])
        total_reactions = sum(reaction.get('count', 0) for reaction in reactions) if reactions else 0
        
        if total_reactions > 5:
            return 'medium'
        
        return 'low'
