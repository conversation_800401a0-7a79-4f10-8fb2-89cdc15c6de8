"""
Enhanced GitHub Ingestion Service for Advanced RAG

This service extends GitHub ingestion with advanced metadata for:
- File-level retrieval support
- Domain classification
- Technical complexity analysis
- Cross-domain reference extraction
"""

import logging
import re
from typing import Dict, Any, List, Optional

logger = logging.getLogger(__name__)


class EnhancedGitHubIngestion:
    """Enhanced GitHub ingestion with advanced metadata for RAG."""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # Programming language detection
        self.language_extensions = {
            '.py': 'python',
            '.js': 'javascript',
            '.ts': 'typescript',
            '.java': 'java',
            '.cpp': 'cpp',
            '.c': 'c',
            '.go': 'go',
            '.rs': 'rust',
            '.php': 'php',
            '.rb': 'ruby',
            '.swift': 'swift',
            '.kt': 'kotlin',
            '.scala': 'scala',
            '.sh': 'shell',
            '.sql': 'sql',
            '.html': 'html',
            '.css': 'css',
            '.json': 'json',
            '.xml': 'xml',
            '.yaml': 'yaml',
            '.yml': 'yaml',
            '.md': 'markdown'
        }
        
        # Technical complexity indicators
        self.technical_indicators = [
            'algorithm', 'architecture', 'framework', 'library', 'api',
            'database', 'server', 'deployment', 'configuration', 'security',
            'authentication', 'authorization', 'encryption', 'optimization',
            'performance', 'scalability', 'microservices', 'containerization'
        ]
        
        # Workflow stage indicators
        self.workflow_stages = {
            'draft': ['draft', 'wip', 'work in progress'],
            'review': ['review', 'feedback', 'comments'],
            'testing': ['test', 'testing', 'qa', 'quality'],
            'deployment': ['deploy', 'deployment', 'release', 'production'],
            'maintenance': ['fix', 'bug', 'hotfix', 'patch'],
            'feature': ['feature', 'enhancement', 'improvement']
        }
    
    def enhance_github_document(self, document: Dict[str, Any]) -> Dict[str, Any]:
        """
        Add GitHub-specific advanced metadata for RAG features.
        
        Args:
            document: GitHub document data
            
        Returns:
            Enhanced document with advanced metadata
        """
        try:
            content_type = document.get('content_type', '')
            content = document.get('content', '')
            metadata = document.get('metadata', {})
            
            # Initialize enhanced metadata
            enhanced_metadata = metadata.copy()
            
            # Add file-level metadata for code files
            if content_type in ['code', 'github_code']:
                file_metadata = self._enhance_code_file_metadata(document, content, metadata)
                enhanced_metadata.update(file_metadata)
            
            # Add issue/PR specific metadata
            elif content_type in ['github_pr', 'github_issue']:
                workflow_metadata = self._enhance_workflow_metadata(document, content, metadata)
                enhanced_metadata.update(workflow_metadata)
            
            # Add wiki/documentation metadata
            elif content_type in ['github_wiki', 'markdown']:
                doc_metadata = self._enhance_documentation_metadata(document, content, metadata)
                enhanced_metadata.update(doc_metadata)
            
            # Add discussion metadata
            elif content_type == 'github_discussion':
                discussion_metadata = self._enhance_discussion_metadata(document, content, metadata)
                enhanced_metadata.update(discussion_metadata)
            
            # Add common GitHub enhancements
            common_metadata = self._add_common_github_metadata(document, content, metadata)
            enhanced_metadata.update(common_metadata)
            
            # Update document
            document['metadata'] = enhanced_metadata
            
            return document
            
        except Exception as e:
            self.logger.error(f"Error enhancing GitHub document: {str(e)}")
            return document
    
    def _enhance_code_file_metadata(self, document: Dict[str, Any], 
                                  content: str, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Enhance metadata for code files."""
        
        file_path = document.get('path', '') or metadata.get('file_path', '')
        
        enhancements = {
            # File-level retrieval support
            'file_path': file_path,
            'programming_language': self._detect_language(file_path, content),
            'file_complexity': self._analyze_code_complexity(content),
            'is_complete_file': True,
            'data_domain': 'GITHUB_CODE',
            
            # Code-specific metadata
            'function_count': self._count_functions(content),
            'class_count': self._count_classes(content),
            'import_count': self._count_imports(content),
            'line_count': content.count('\n') if content else 0,
            'has_tests': self._has_tests(file_path, content),
            'has_documentation': self._has_documentation(content),
            
            # Technical analysis
            'technical_complexity': self._analyze_technical_content(content),
            'code_patterns': self._extract_code_patterns(content),
            'dependencies': self._extract_dependencies(content)
        }
        
        return enhancements
    
    def _enhance_workflow_metadata(self, document: Dict[str, Any], 
                                 content: str, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Enhance metadata for PRs and issues."""
        
        content_type = document.get('content_type', '')
        
        enhancements = {
            # Domain classification
            'data_domain': 'GITHUB_ISSUES' if 'issue' in content_type else 'GITHUB_CODE',
            
            # Workflow analysis
            'workflow_stage': self._detect_workflow_stage(document, content),
            'technical_complexity': self._analyze_technical_content(content),
            'priority_level': self._detect_priority_level(document, metadata),
            'impact_scope': self._analyze_impact_scope(document, content, metadata),
            
            # Collaboration metadata
            'participant_count': len(metadata.get('assignees', [])) + len(metadata.get('reviewers', [])),
            'discussion_complexity': self._analyze_discussion_complexity(content),
            'has_code_changes': self._has_code_changes(metadata),
            
            # Cross-domain references
            'cross_references': self._extract_cross_references(content),
            'related_issues': self._extract_issue_references(content),
            'related_prs': self._extract_pr_references(content)
        }
        
        return enhancements
    
    def _enhance_documentation_metadata(self, document: Dict[str, Any], 
                                      content: str, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Enhance metadata for documentation."""
        
        enhancements = {
            # Domain classification
            'data_domain': 'GITHUB_WIKI' if 'wiki' in document.get('content_type', '') else 'DOCUMENTATION',
            
            # Documentation analysis
            'documentation_type': self._classify_documentation_type(content),
            'section_count': len(re.findall(r'^#{1,6}\s+', content, re.MULTILINE)) if content else 0,
            'has_code_examples': bool(re.search(r'```', content)) if content else False,
            'has_links': bool(re.search(r'\[.*?\]\(.*?\)', content)) if content else False,
            'readability_score': self._calculate_readability_score(content),
            
            # Content structure
            'table_of_contents': self._extract_table_of_contents(content),
            'code_snippets': self._extract_code_snippets(content),
            'external_links': self._extract_external_links(content)
        }
        
        return enhancements
    
    def _enhance_discussion_metadata(self, document: Dict[str, Any], 
                                   content: str, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Enhance metadata for discussions."""
        
        enhancements = {
            # Domain classification
            'data_domain': 'GITHUB_DISCUSSIONS',
            
            # Discussion analysis
            'discussion_category': metadata.get('category', 'general'),
            'participant_count': len(metadata.get('participants', [])),
            'response_count': metadata.get('comments_count', 0),
            'discussion_complexity': self._analyze_discussion_complexity(content),
            'has_resolution': self._has_resolution(content),
            
            # Content analysis
            'question_count': content.count('?') if content else 0,
            'technical_level': self._assess_technical_level(content),
            'sentiment': self._analyze_sentiment(content)
        }
        
        return enhancements
    
    def _add_common_github_metadata(self, document: Dict[str, Any], 
                                   content: str, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Add common GitHub metadata enhancements."""
        
        enhancements = {
            # Repository context
            'repository_context': {
                'name': metadata.get('repository', ''),
                'is_public': metadata.get('is_public', True),
                'language': metadata.get('repository_language', ''),
                'topics': metadata.get('repository_topics', [])
            },
            
            # Quality metrics
            'quality_metrics': {
                'content_length': len(content) if content else 0,
                'word_count': len(content.split()) if content else 0,
                'has_proper_formatting': self._has_proper_formatting(content),
                'completeness_score': self._calculate_completeness_score(document, content, metadata)
            },
            
            # Cross-domain hints
            'cross_domain_hints': self._generate_cross_domain_hints(content, metadata),
            
            # Performance hints
            'performance_hints': {
                'cache_priority': self._determine_cache_priority(document, metadata),
                'indexing_priority': self._determine_indexing_priority(document, metadata),
                'search_boost': self._calculate_search_boost(document, metadata)
            }
        }
        
        return enhancements
    
    def _detect_language(self, file_path: str, content: str) -> str:
        """Detect programming language from file path and content."""
        
        if file_path:
            for ext, lang in self.language_extensions.items():
                if file_path.lower().endswith(ext):
                    return lang
        
        # Fallback to content analysis
        if content:
            if any(pattern in content for pattern in ['def ', 'import ', 'from ']):
                return 'python'
            elif any(pattern in content for pattern in ['function ', 'const ', 'let ']):
                return 'javascript'
            elif any(pattern in content for pattern in ['public class', 'private ']):
                return 'java'
        
        return 'text'
    
    def _analyze_code_complexity(self, content: str) -> float:
        """Analyze code complexity."""
        if not content:
            return 0.0
        
        complexity = 0.0
        
        # Length-based complexity
        lines = content.count('\n')
        if lines > 500:
            complexity += 0.4
        elif lines > 200:
            complexity += 0.3
        elif lines > 100:
            complexity += 0.2
        elif lines > 50:
            complexity += 0.1
        
        # Structural complexity
        complexity += min(self._count_functions(content) * 0.02, 0.3)
        complexity += min(self._count_classes(content) * 0.05, 0.2)
        complexity += min(content.count('if ') * 0.01, 0.2)
        complexity += min(content.count('for ') * 0.01, 0.1)
        
        return min(complexity, 1.0)
    
    def _count_functions(self, content: str) -> int:
        """Count functions in code."""
        if not content:
            return 0
        
        # Simple pattern matching for common languages
        patterns = [
            r'def\s+\w+',      # Python
            r'function\s+\w+', # JavaScript
            r'public\s+\w+\s+\w+\s*\(',  # Java methods
            r'private\s+\w+\s+\w+\s*\(', # Java methods
        ]
        
        count = 0
        for pattern in patterns:
            count += len(re.findall(pattern, content))
        
        return count
    
    def _count_classes(self, content: str) -> int:
        """Count classes in code."""
        if not content:
            return 0
        
        patterns = [
            r'class\s+\w+',        # Python, JavaScript
            r'public\s+class\s+\w+', # Java
            r'private\s+class\s+\w+' # Java
        ]
        
        count = 0
        for pattern in patterns:
            count += len(re.findall(pattern, content))
        
        return count
    
    def _count_imports(self, content: str) -> int:
        """Count import statements."""
        if not content:
            return 0
        
        patterns = [
            r'import\s+',      # Python, JavaScript, Java
            r'from\s+\w+\s+import', # Python
            r'#include\s+',    # C/C++
            r'require\s*\('    # Node.js
        ]
        
        count = 0
        for pattern in patterns:
            count += len(re.findall(pattern, content))
        
        return count
    
    def _has_tests(self, file_path: str, content: str) -> bool:
        """Check if file contains tests."""
        if 'test' in file_path.lower() or 'spec' in file_path.lower():
            return True
        
        if content:
            test_indicators = ['test_', 'def test', 'it(', 'describe(', 'assert', 'expect(']
            return any(indicator in content for indicator in test_indicators)
        
        return False
    
    def _has_documentation(self, content: str) -> bool:
        """Check if code has documentation."""
        if not content:
            return False
        
        doc_indicators = ['"""', "'''", '/**', '/*', '##', 'README']
        return any(indicator in content for indicator in doc_indicators)
    
    def _analyze_technical_content(self, content: str) -> float:
        """Analyze technical complexity of content."""
        if not content:
            return 0.0
        
        content_lower = content.lower()
        technical_count = sum(1 for term in self.technical_indicators if term in content_lower)
        
        # Normalize by content length
        words = len(content.split())
        if words == 0:
            return 0.0
        
        technical_density = technical_count / max(words / 100, 1)  # Per 100 words
        return min(technical_density, 1.0)
    
    def _extract_code_patterns(self, content: str) -> List[str]:
        """Extract code patterns from content."""
        patterns = []
        
        if not content:
            return patterns
        
        # Common patterns
        if 'async' in content and 'await' in content:
            patterns.append('async_programming')
        if 'class' in content:
            patterns.append('object_oriented')
        if 'try:' in content or 'except:' in content:
            patterns.append('error_handling')
        if 'test_' in content or 'assert' in content:
            patterns.append('testing')
        
        return patterns
    
    def _extract_dependencies(self, content: str) -> List[str]:
        """Extract dependencies from code."""
        dependencies = []
        
        if not content:
            return dependencies
        
        # Python imports
        import_matches = re.findall(r'import\s+(\w+)', content)
        from_matches = re.findall(r'from\s+(\w+)', content)
        dependencies.extend(import_matches + from_matches)
        
        # JavaScript requires
        require_matches = re.findall(r'require\s*\(\s*[\'"]([^\'"]+)', content)
        dependencies.extend(require_matches)
        
        return list(set(dependencies))[:10]  # Limit and deduplicate
    
    def _detect_workflow_stage(self, document: Dict[str, Any], content: str) -> str:
        """Detect workflow stage from document and content."""
        
        # Check labels first
        labels = document.get('metadata', {}).get('labels', [])
        for stage, indicators in self.workflow_stages.items():
            if any(indicator in ' '.join(labels).lower() for indicator in indicators):
                return stage
        
        # Check title and content
        title = document.get('title', '').lower()
        content_lower = content.lower() if content else ''
        
        for stage, indicators in self.workflow_stages.items():
            if any(indicator in title or indicator in content_lower for indicator in indicators):
                return stage
        
        return 'unknown'
    
    def _detect_priority_level(self, document: Dict[str, Any], metadata: Dict[str, Any]) -> str:
        """Detect priority level from labels and content."""
        
        labels = metadata.get('labels', [])
        label_text = ' '.join(labels).lower()
        
        if any(term in label_text for term in ['critical', 'urgent', 'high']):
            return 'high'
        elif any(term in label_text for term in ['medium', 'normal']):
            return 'medium'
        elif any(term in label_text for term in ['low', 'minor']):
            return 'low'
        
        return 'medium'  # Default
    
    def _analyze_impact_scope(self, document: Dict[str, Any], content: str, metadata: Dict[str, Any]) -> str:
        """Analyze impact scope of changes."""
        
        # Check changed files count for PRs
        changed_files = metadata.get('changed_files', 0)
        if changed_files > 20:
            return 'major'
        elif changed_files > 5:
            return 'moderate'
        elif changed_files > 0:
            return 'minor'
        
        # Check content for scope indicators
        if content:
            content_lower = content.lower()
            if any(term in content_lower for term in ['breaking', 'major', 'architecture']):
                return 'major'
            elif any(term in content_lower for term in ['feature', 'enhancement']):
                return 'moderate'
        
        return 'minor'
    
    def _analyze_discussion_complexity(self, content: str) -> float:
        """Analyze complexity of discussion content."""
        if not content:
            return 0.0
        
        complexity = 0.0
        
        # Length factor
        word_count = len(content.split())
        if word_count > 1000:
            complexity += 0.4
        elif word_count > 500:
            complexity += 0.3
        elif word_count > 200:
            complexity += 0.2
        elif word_count > 50:
            complexity += 0.1
        
        # Technical content
        complexity += self._analyze_technical_content(content) * 0.3
        
        # Question complexity
        question_count = content.count('?')
        complexity += min(question_count * 0.05, 0.3)
        
        return min(complexity, 1.0)
    
    def _has_code_changes(self, metadata: Dict[str, Any]) -> bool:
        """Check if PR has code changes."""
        return metadata.get('changed_files', 0) > 0 or metadata.get('additions', 0) > 0
    
    def _extract_cross_references(self, content: str) -> List[str]:
        """Extract cross-references to other GitHub items."""
        if not content:
            return []
        
        references = []
        
        # Issue references (#123)
        issue_refs = re.findall(r'#(\d+)', content)
        references.extend([f"issue_{ref}" for ref in issue_refs])
        
        # PR references
        pr_refs = re.findall(r'PR\s*#(\d+)', content, re.IGNORECASE)
        references.extend([f"pr_{ref}" for ref in pr_refs])
        
        # Commit references
        commit_refs = re.findall(r'\b([a-f0-9]{7,40})\b', content)
        references.extend([f"commit_{ref}" for ref in commit_refs[:5]])  # Limit commits
        
        return references[:20]  # Limit total references
    
    def _extract_issue_references(self, content: str) -> List[str]:
        """Extract issue references."""
        if not content:
            return []
        
        return re.findall(r'#(\d+)', content)[:10]
    
    def _extract_pr_references(self, content: str) -> List[str]:
        """Extract PR references."""
        if not content:
            return []
        
        return re.findall(r'PR\s*#(\d+)', content, re.IGNORECASE)[:10]
    
    def _classify_documentation_type(self, content: str) -> str:
        """Classify type of documentation."""
        if not content:
            return 'unknown'
        
        content_lower = content.lower()
        
        if 'readme' in content_lower:
            return 'readme'
        elif 'api' in content_lower and 'endpoint' in content_lower:
            return 'api_documentation'
        elif 'install' in content_lower or 'setup' in content_lower:
            return 'installation_guide'
        elif 'tutorial' in content_lower or 'guide' in content_lower:
            return 'tutorial'
        elif 'changelog' in content_lower or 'release' in content_lower:
            return 'changelog'
        
        return 'general'
    
    def _calculate_readability_score(self, content: str) -> float:
        """Calculate readability score for documentation."""
        if not content:
            return 0.0
        
        score = 0.0
        
        # Has proper structure
        if re.search(r'^#{1,6}\s+', content, re.MULTILINE):
            score += 0.3
        
        # Has code examples
        if '```' in content:
            score += 0.2
        
        # Has links
        if re.search(r'\[.*?\]\(.*?\)', content):
            score += 0.2
        
        # Reasonable length
        word_count = len(content.split())
        if 100 < word_count < 2000:
            score += 0.3
        
        return min(score, 1.0)
    
    def _extract_table_of_contents(self, content: str) -> List[str]:
        """Extract table of contents from documentation."""
        if not content:
            return []
        
        headers = re.findall(r'^(#{1,6})\s+(.+)$', content, re.MULTILINE)
        return [header[1].strip() for header in headers[:10]]  # Limit to 10 headers
    
    def _extract_code_snippets(self, content: str) -> List[str]:
        """Extract code snippets from documentation."""
        if not content:
            return []
        
        snippets = re.findall(r'```(\w+)?\n(.*?)\n```', content, re.DOTALL)
        return [snippet[0] or 'unknown' for snippet in snippets[:5]]  # Language or 'unknown'
    
    def _extract_external_links(self, content: str) -> List[str]:
        """Extract external links from documentation."""
        if not content:
            return []
        
        links = re.findall(r'\[.*?\]\((https?://[^\)]+)\)', content)
        return links[:10]  # Limit to 10 links
    
    def _has_resolution(self, content: str) -> bool:
        """Check if discussion has resolution."""
        if not content:
            return False
        
        resolution_indicators = ['solved', 'resolved', 'fixed', 'closed', 'answered']
        content_lower = content.lower()
        return any(indicator in content_lower for indicator in resolution_indicators)
    
    def _assess_technical_level(self, content: str) -> str:
        """Assess technical level of content."""
        if not content:
            return 'non-technical'
        
        technical_score = self._analyze_technical_content(content)
        
        if technical_score > 0.7:
            return 'expert'
        elif technical_score > 0.4:
            return 'intermediate'
        elif technical_score > 0.1:
            return 'basic'
        else:
            return 'non-technical'
    
    def _analyze_sentiment(self, content: str) -> str:
        """Analyze sentiment of content (simple approach)."""
        if not content:
            return 'neutral'
        
        content_lower = content.lower()
        
        positive_words = ['good', 'great', 'excellent', 'awesome', 'perfect', 'love']
        negative_words = ['bad', 'terrible', 'awful', 'hate', 'broken', 'issue', 'problem']
        
        positive_count = sum(1 for word in positive_words if word in content_lower)
        negative_count = sum(1 for word in negative_words if word in content_lower)
        
        if positive_count > negative_count:
            return 'positive'
        elif negative_count > positive_count:
            return 'negative'
        else:
            return 'neutral'
    
    def _has_proper_formatting(self, content: str) -> bool:
        """Check if content has proper formatting."""
        if not content:
            return False
        
        # Check for basic formatting elements
        has_structure = bool(re.search(r'^#{1,6}\s+', content, re.MULTILINE))
        has_paragraphs = '\n\n' in content
        reasonable_length = len(content.split()) > 10
        
        return has_structure or (has_paragraphs and reasonable_length)
    
    def _calculate_completeness_score(self, document: Dict[str, Any], 
                                    content: str, metadata: Dict[str, Any]) -> float:
        """Calculate completeness score for document."""
        score = 0.0
        
        # Has title
        if document.get('title'):
            score += 0.2
        
        # Has content
        if content and len(content.strip()) > 50:
            score += 0.3
        
        # Has metadata
        if len(metadata) > 3:
            score += 0.2
        
        # Has proper structure
        if self._has_proper_formatting(content):
            score += 0.3
        
        return min(score, 1.0)
    
    def _generate_cross_domain_hints(self, content: str, metadata: Dict[str, Any]) -> List[str]:
        """Generate cross-domain hints for routing."""
        hints = []
        
        if not content:
            return hints
        
        content_lower = content.lower()
        
        # Check for Slack references
        if any(term in content_lower for term in ['slack', 'channel', 'message']):
            hints.append('slack_conversations')
        
        # Check for documentation references
        if any(term in content_lower for term in ['docs', 'documentation', 'readme', 'wiki']):
            hints.append('documentation')
        
        # Check for meeting references
        if any(term in content_lower for term in ['meeting', 'discussion', 'agenda']):
            hints.append('meeting_notes')
        
        return hints
    
    def _determine_cache_priority(self, document: Dict[str, Any], metadata: Dict[str, Any]) -> str:
        """Determine cache priority for document."""
        
        # High priority for recent, active content
        if metadata.get('comments_count', 0) > 10:
            return 'high'
        
        # High priority for code files
        if document.get('content_type') in ['code', 'github_code']:
            return 'high'
        
        # Medium priority for PRs and issues
        if document.get('content_type') in ['github_pr', 'github_issue']:
            return 'medium'
        
        return 'low'
    
    def _determine_indexing_priority(self, document: Dict[str, Any], metadata: Dict[str, Any]) -> str:
        """Determine indexing priority for document."""
        
        # High priority for main branch code
        if metadata.get('branch') == 'main' or metadata.get('branch') == 'master':
            return 'high'
        
        # High priority for open PRs/issues
        if metadata.get('state') == 'open':
            return 'high'
        
        return 'medium'
    
    def _calculate_search_boost(self, document: Dict[str, Any], metadata: Dict[str, Any]) -> float:
        """Calculate search boost factor."""
        boost = 1.0
        
        # Boost for recent content
        if metadata.get('is_recent', False):
            boost += 0.2
        
        # Boost for high-quality content
        quality_score = metadata.get('quality_score', 0.5)
        boost += (quality_score - 0.5) * 0.4
        
        # Boost for active content
        if metadata.get('comments_count', 0) > 5:
            boost += 0.1
        
        return min(boost, 2.0)  # Cap at 2x boost
