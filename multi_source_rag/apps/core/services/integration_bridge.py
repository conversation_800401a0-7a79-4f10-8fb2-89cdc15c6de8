"""
Integration Bridge Service
=========================

Critical component that bridges the gap between enhanced ingestion and retrieval systems.
This service ensures that rich metadata created during ingestion is properly coordinated
with vector store updates and used during query routing.

This addresses the major integration gaps identified in the code review:
1. Ingestion → Vector Store coordination
2. Domain metadata → Query routing integration
3. File-level embedding coordination
4. Complexity analysis → Strategy selection integration
"""

import logging
import uuid
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass

from django.db import transaction
from django.utils import timezone

from apps.core.llama_index_manager import LlamaIndexManager
from apps.core.utils.vectorstore import get_vector_store, add_documents_to_vector_store
from apps.core.utils.embedding_consistency import get_embedding_model_info
from apps.documents.models import (
    RawDocument, DocumentChunk, DocumentContent,
    DocumentDomainMetadata, DocumentComplexityProfile, EmbeddingMetadata
)
from apps.core.services.domain_classification import DataDomain
from apps.core.utils.cache_manager import get_cache

logger = logging.getLogger(__name__)


@dataclass
class IntegrationResult:
    """Result of integration bridge processing."""
    document_id: int
    vector_embeddings_created: int
    file_level_embedding_id: Optional[str]
    domain_metadata_updated: bool
    complexity_profile_updated: bool
    collection_name: str
    integration_metadata: Dict[str, Any]


class IngestionVectorBridge:
    """
    CRITICAL MISSING COMPONENT: Bridge between ingestion and vector store.

    This service ensures that enhanced metadata from ingestion is properly
    coordinated with vector store updates and embedding generation.
    """

    def __init__(self, tenant_slug: str):
        self.tenant_slug = tenant_slug
        self.llama_manager = LlamaIndexManager(tenant_slug)
        self.cache = get_cache(f"integration_bridge_{tenant_slug}", maxsize=500, ttl=1800)
        self.logger = logging.getLogger(f"{__name__}.{tenant_slug}")

    def process_document_end_to_end(self, document: RawDocument) -> IntegrationResult:
        """
        CRITICAL FIX: Complete document processing with vector coordination.

        This method ensures that enhanced metadata from ingestion is properly
        integrated with vector store embeddings and search indexes.
        """
        try:
            with transaction.atomic():
                # Step 1: Extract enhanced metadata
                metadata = self._extract_enhanced_metadata(document)

                # Step 2: Generate embeddings with metadata
                embeddings_result = self._generate_embeddings_with_metadata(document, metadata)

                # Step 3: Update vector store with enhanced metadata
                vector_result = self._update_vector_store_with_metadata(document, embeddings_result, metadata)

                # Step 4: Update search indexes
                self._update_search_indexes(document, metadata)

                # Step 5: Create integration result
                result = IntegrationResult(
                    document_id=document.id,
                    vector_embeddings_created=len(embeddings_result['chunk_embeddings']),
                    file_level_embedding_id=embeddings_result.get('file_embedding_id'),
                    domain_metadata_updated=bool(metadata.get('domain_metadata')),
                    complexity_profile_updated=bool(metadata.get('complexity_profile')),
                    collection_name=vector_result['collection_name'],
                    integration_metadata=metadata
                )

                self.logger.info(f"Successfully integrated document {document.id} end-to-end")
                return result

        except Exception as e:
            self.logger.error(f"End-to-end integration failed for document {document.id}: {e}")
            raise

    def _extract_enhanced_metadata(self, document: RawDocument) -> Dict[str, Any]:
        """Extract all enhanced metadata created during ingestion."""
        metadata = {
            'document_metadata': document.metadata or {},
            'domain_metadata': None,
            'complexity_profile': None,
            'file_content': None,
            'chunks_metadata': []
        }

        # Extract domain metadata
        try:
            domain_metadata = DocumentDomainMetadata.objects.filter(document=document).first()
            if domain_metadata:
                metadata['domain_metadata'] = {
                    'primary_domain': domain_metadata.primary_domain,
                    'domain_confidence': domain_metadata.domain_confidence,
                    'secondary_domains': domain_metadata.secondary_domains,
                    'routing_metadata': domain_metadata.routing_metadata
                }
        except Exception as e:
            self.logger.warning(f"Could not extract domain metadata: {e}")

        # Extract complexity profile
        try:
            complexity_profile = DocumentComplexityProfile.objects.filter(document=document).first()
            if complexity_profile:
                metadata['complexity_profile'] = {
                    'semantic_complexity': complexity_profile.semantic_complexity,
                    'domain_complexity': complexity_profile.domain_complexity,
                    'strategy_recommendations': complexity_profile.strategy_recommendations,
                    'performance_hints': complexity_profile.performance_hints
                }
        except Exception as e:
            self.logger.warning(f"Could not extract complexity profile: {e}")

        # Extract file content metadata
        try:
            file_content = DocumentContent.objects.filter(document=document).first()
            if file_content:
                metadata['file_content'] = {
                    'file_level_embedding_id': file_content.file_level_embedding_id,
                    'file_summary': file_content.file_summary,
                    'file_keywords': file_content.file_keywords,
                    'domain_specific_content': file_content.domain_specific_content,
                    'content': file_content.content
                }
        except Exception as e:
            self.logger.warning(f"Could not extract file content metadata: {e}")

        # Extract chunk metadata
        try:
            chunks = DocumentChunk.objects.filter(document=document).order_by('chunk_index')
            for chunk in chunks:
                chunk_meta = {
                    'chunk_id': chunk.id,
                    'chunk_index': chunk.chunk_index,
                    'metadata': chunk.metadata or {},
                    'text': chunk.text
                }
                metadata['chunks_metadata'].append(chunk_meta)
        except Exception as e:
            self.logger.warning(f"Could not extract chunk metadata: {e}")

        return metadata

    def _generate_embeddings_with_metadata(self, document: RawDocument, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """
        CRITICAL FIX: Generate both chunk and file-level embeddings with enhanced metadata.
        """
        embeddings_result = {
            'chunk_embeddings': [],
            'file_embedding_id': None,
            'enhanced_metadata': {}
        }

        # Determine collection strategy based on domain metadata
        domain_info = metadata.get('domain_metadata', {})
        primary_domain = domain_info.get('primary_domain', 'default')

        # Generate chunk-level embeddings with enhanced metadata
        for chunk_meta in metadata.get('chunks_metadata', []):
            try:
                # Enhance chunk metadata with domain and complexity info
                enhanced_chunk_metadata = self._enhance_chunk_metadata_for_vector_store(
                    chunk_meta, domain_info, metadata.get('complexity_profile', {})
                )

                embeddings_result['chunk_embeddings'].append({
                    'chunk_id': chunk_meta['chunk_id'],
                    'text': chunk_meta['text'],
                    'metadata': enhanced_chunk_metadata
                })

            except Exception as e:
                self.logger.error(f"Failed to prepare chunk embedding for chunk {chunk_meta['chunk_id']}: {e}")

        # Generate file-level embedding if applicable
        file_content = metadata.get('file_content', {})
        if file_content and file_content.get('file_level_embedding_id'):
            try:
                file_embedding_metadata = self._create_file_level_embedding_metadata(
                    document, file_content, domain_info, metadata.get('complexity_profile', {})
                )

                embeddings_result['file_embedding_id'] = file_content['file_level_embedding_id']
                embeddings_result['file_embedding_metadata'] = file_embedding_metadata

            except Exception as e:
                self.logger.error(f"Failed to prepare file-level embedding: {e}")

        return embeddings_result

    def _enhance_chunk_metadata_for_vector_store(self, chunk_meta: Dict[str, Any],
                                                domain_info: Dict[str, Any],
                                                complexity_info: Dict[str, Any]) -> Dict[str, Any]:
        """Enhance chunk metadata with domain and complexity information for vector store."""
        enhanced_metadata = chunk_meta['metadata'].copy()

        # Add domain classification
        if domain_info:
            enhanced_metadata.update({
                'primary_domain': domain_info.get('primary_domain'),
                'domain_confidence': domain_info.get('domain_confidence'),
                'secondary_domains': domain_info.get('secondary_domains', []),
                'routing_hints': domain_info.get('routing_metadata', {})
            })

        # Add complexity information
        if complexity_info:
            enhanced_metadata.update({
                'semantic_complexity': complexity_info.get('semantic_complexity'),
                'domain_complexity': complexity_info.get('domain_complexity'),
                'strategy_hints': complexity_info.get('strategy_recommendations', {}),
                'performance_hints': complexity_info.get('performance_hints', {})
            })

        # Add integration metadata
        enhanced_metadata.update({
            'integration_timestamp': timezone.now().isoformat(),
            'integration_version': '2.0.0',
            'chunk_id': chunk_meta['chunk_id'],
            'embedding_type': 'chunk_level'
        })

        return enhanced_metadata

    def _create_file_level_embedding_metadata(self, document: RawDocument,
                                            file_content: Dict[str, Any],
                                            domain_info: Dict[str, Any],
                                            complexity_info: Dict[str, Any]) -> Dict[str, Any]:
        """Create metadata for file-level embeddings."""
        file_metadata = {
            'document_id': document.id,
            'document_title': document.title,
            'file_summary': file_content.get('file_summary'),
            'file_keywords': file_content.get('file_keywords', []),
            'domain_specific_content': file_content.get('domain_specific_content', {}),
            'embedding_type': 'file_level',
            'integration_timestamp': timezone.now().isoformat()
        }

        # Add domain information
        if domain_info:
            file_metadata.update({
                'primary_domain': domain_info.get('primary_domain'),
                'domain_confidence': domain_info.get('domain_confidence'),
                'routing_metadata': domain_info.get('routing_metadata', {})
            })

        # Add complexity information
        if complexity_info:
            file_metadata.update({
                'file_complexity': complexity_info.get('semantic_complexity'),
                'strategy_recommendations': complexity_info.get('strategy_recommendations', {}),
                'performance_hints': complexity_info.get('performance_hints', {})
            })

        return file_metadata

    def _update_vector_store_with_metadata(self, document: RawDocument,
                                         embeddings_result: Dict[str, Any],
                                         metadata: Dict[str, Any]) -> Dict[str, Any]:
        """
        CRITICAL FIX: Update vector store with enhanced metadata.
        """
        domain_info = metadata.get('domain_metadata', {})
        primary_domain = domain_info.get('primary_domain', 'default')

        # Determine collection name based on domain
        collection_name = self._get_collection_name_for_domain(primary_domain)

        try:
            # Prepare documents for vector store
            documents_to_add = []
            vector_ids = []

            # Add chunk-level embeddings
            for chunk_embedding in embeddings_result['chunk_embeddings']:
                from langchain_core.documents import Document

                doc = Document(
                    page_content=chunk_embedding['text'],
                    metadata=chunk_embedding['metadata']
                )
                documents_to_add.append(doc)

                # Generate or use existing vector ID
                vector_id = str(uuid.uuid4())
                vector_ids.append(vector_id)

                # Update EmbeddingMetadata with enhanced information
                self._update_embedding_metadata(
                    chunk_embedding['chunk_id'],
                    vector_id,
                    chunk_embedding['metadata']
                )

            # Add file-level embedding if exists
            if embeddings_result.get('file_embedding_id'):
                file_content_data = metadata.get('file_content', {})
                file_text = file_content_data.get('content') or file_content_data.get('file_summary', '')

                file_doc = Document(
                    page_content=file_text,
                    metadata=embeddings_result.get('file_embedding_metadata', {})
                )
                documents_to_add.append(file_doc)
                vector_ids.append(embeddings_result['file_embedding_id'])

            # Add to vector store
            if documents_to_add:
                add_documents_to_vector_store(
                    documents=documents_to_add,
                    collection_name=collection_name,
                    ids=vector_ids,
                    content_type=primary_domain.lower() if primary_domain else 'default'
                )

                self.logger.info(f"Added {len(documents_to_add)} documents to vector store collection {collection_name}")

            return {
                'collection_name': collection_name,
                'documents_added': len(documents_to_add),
                'vector_ids': vector_ids
            }

        except Exception as e:
            self.logger.error(f"Failed to update vector store: {e}")
            raise

    def _get_collection_name_for_domain(self, primary_domain: str) -> str:
        """Get appropriate collection name based on domain."""
        domain_collection_mapping = {
            'GITHUB_CODE': f"{self.tenant_slug}_github_code",
            'GITHUB_ISSUES': f"{self.tenant_slug}_github_issues",
            'SLACK_CONVERSATIONS': f"{self.tenant_slug}_slack_conversations",
            'SLACK_ENGINEERING': f"{self.tenant_slug}_slack_engineering",
            'DOCUMENTATION': f"{self.tenant_slug}_documentation",
            'MEETING_NOTES': f"{self.tenant_slug}_meetings"
        }

        return domain_collection_mapping.get(primary_domain, f"{self.tenant_slug}_default")

    def _update_embedding_metadata(self, chunk_id: int, vector_id: str, enhanced_metadata: Dict[str, Any]):
        """Update EmbeddingMetadata with enhanced information."""
        try:
            chunk = DocumentChunk.objects.get(id=chunk_id)
            model_info = get_embedding_model_info()

            embedding_metadata, created = EmbeddingMetadata.objects.update_or_create(
                chunk=chunk,
                defaults={
                    'vector_id': vector_id,
                    'model_name': model_info.get("model_name", "BAAI/bge-base-en-v1.5"),
                    'vector_dimensions': model_info.get("dimensions", 768),
                    'is_synced': True
                }
            )

            # Update chunk metadata with enhanced information
            chunk.metadata = enhanced_metadata
            chunk.save(update_fields=['metadata'])

            self.logger.debug(f"Updated embedding metadata for chunk {chunk_id} with vector_id {vector_id}")

        except Exception as e:
            self.logger.error(f"Failed to update embedding metadata for chunk {chunk_id}: {e}")

    def _update_search_indexes(self, document: RawDocument, metadata: Dict[str, Any]):
        """Update search indexes with enhanced metadata."""
        try:
            # Update document metadata with integration information
            document_metadata = document.metadata or {}
            document_metadata.update({
                'integration_processed': True,
                'integration_timestamp': timezone.now().isoformat(),
                'domain_integrated': bool(metadata.get('domain_metadata')),
                'complexity_integrated': bool(metadata.get('complexity_profile')),
                'file_level_integrated': bool(metadata.get('file_content', {}).get('file_level_embedding_id'))
            })

            document.metadata = document_metadata
            document.save(update_fields=['metadata'])

            self.logger.debug(f"Updated search indexes for document {document.id}")

        except Exception as e:
            self.logger.error(f"Failed to update search indexes for document {document.id}: {e}")


class MetadataAwareQueryEngine:
    """
    CRITICAL MISSING COMPONENT: Query engine that uses ingestion metadata.

    This engine leverages the rich metadata created during ingestion to make
    smarter routing and strategy decisions.
    """

    def __init__(self, tenant_slug: str):
        self.tenant_slug = tenant_slug
        self.cache = get_cache(f"metadata_query_engine_{tenant_slug}", maxsize=300, ttl=1200)
        self.logger = logging.getLogger(f"{__name__}.{tenant_slug}")

    def query_with_metadata_intelligence(self, query: str, intent: str = "default") -> Dict[str, Any]:
        """
        CRITICAL FIX: Use ingestion metadata to enhance query processing.
        """
        try:
            # Step 1: Analyze query against known complexity patterns
            complexity_match = self._match_complexity_patterns(query)

            # Step 2: Use domain metadata for intelligent routing
            domain_routing = self._route_with_domain_metadata(query)

            # Step 3: Apply learned strategy recommendations
            strategy = self._select_strategy_from_metadata(complexity_match, domain_routing)

            # Step 4: Execute with metadata-enhanced retrieval
            return self._execute_metadata_enhanced_retrieval(query, strategy)

        except Exception as e:
            self.logger.error(f"Metadata-aware query failed: {e}")
            raise

    def _match_complexity_patterns(self, query: str) -> Dict[str, Any]:
        """Match query against learned complexity patterns from ingestion."""
        # Implementation for complexity pattern matching
        # This would use the DocumentComplexityProfile data
        pass

    def _route_with_domain_metadata(self, query: str) -> Dict[str, Any]:
        """Use domain metadata for intelligent routing."""
        # Implementation for domain-based routing using DocumentDomainMetadata
        pass

    def _select_strategy_from_metadata(self, complexity_match: Dict[str, Any],
                                     domain_routing: Dict[str, Any]) -> Dict[str, Any]:
        """Select strategy based on learned patterns."""
        # Implementation for strategy selection using metadata
        pass

    def _execute_metadata_enhanced_retrieval(self, query: str, strategy: Dict[str, Any]) -> Dict[str, Any]:
        """Execute retrieval with metadata enhancements."""
        # Implementation for enhanced retrieval
        pass
