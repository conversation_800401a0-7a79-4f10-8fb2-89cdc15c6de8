"""
Database Query Optimization Management Command

This command implements database optimizations recommended in Phase 3:
- Adds database indexes for frequent queries
- Optimizes citation queries with select_related
- Implements query result caching
- Adds database performance monitoring

Usage:
    python manage.py optimize_queries [--dry-run] [--verbose]
"""

import logging
import time
from django.core.management.base import BaseCommand, CommandError
from django.db import connection, transaction
from django.core.cache import cache
from django.conf import settings
from django.core.management import call_command

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Optimize database queries and add performance monitoring'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without making changes',
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Show detailed output',
        )
        parser.add_argument(
            '--run-migrations',
            action='store_true',
            help='Run database migrations before optimization',
        )
        parser.add_argument(
            '--fresh-setup',
            action='store_true',
            help='Perform fresh database setup (migrations + optimization)',
        )

    def handle(self, *args, **options):
        self.dry_run = options['dry_run']
        self.verbose = options['verbose']
        self.run_migrations = options['run_migrations']
        self.fresh_setup = options['fresh_setup']

        if self.dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - No changes will be made')
            )

        try:
            # Handle fresh setup or migrations
            if self.fresh_setup or self.run_migrations:
                self.run_database_migrations()

            self.stdout.write('Starting database optimization...')

            # Add database indexes
            self.add_indexes()

            # Optimize citation queries
            self.optimize_citations()

            # Setup query monitoring
            self.setup_monitoring()

            # Test performance improvements
            self.test_performance()

            self.stdout.write(
                self.style.SUCCESS('Database optimization completed successfully!')
            )

        except Exception as e:
            logger.error(f"Database optimization failed: {str(e)}")
            raise CommandError(f'Optimization failed: {str(e)}')

    def run_database_migrations(self):
        """Run database migrations to ensure schema is up to date."""
        self.stdout.write('Running database migrations...')

        if self.dry_run:
            self.stdout.write('  Would run: python manage.py migrate')
            return

        try:
            # Run migrations
            self.stdout.write('  Running migrations...')
            call_command('migrate', verbosity=1 if self.verbose else 0)

            self.stdout.write(
                self.style.SUCCESS('  ✓ Database migrations completed successfully')
            )

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'  ✗ Migration failed: {str(e)}')
            )
            raise CommandError(f'Migration failed: {str(e)}')

    def add_indexes(self):
        """Add database indexes for frequent queries."""
        self.stdout.write('Adding database indexes...')

        indexes_to_add = [
            # EmbeddingMetadata indexes for citation lookups
            {
                'table': 'documents_embeddingmetadata',
                'name': 'idx_embedding_vector_id_lookup',
                'columns': ['vector_id'],
                'description': 'Optimize vector ID lookups in citation creation'
            },
            # DocumentChunk indexes for tenant filtering
            {
                'table': 'documents_documentchunk',
                'name': 'idx_chunk_tenant_lookup',
                'columns': ['tenant_id', 'created_at'],
                'description': 'Optimize tenant-based chunk queries'
            },
            # ResultCitation indexes for duplicate checking
            {
                'table': 'search_resultcitation',
                'name': 'idx_citation_result_chunk',
                'columns': ['result_id', 'document_chunk_id'],
                'description': 'Optimize citation duplicate checking'
            },
            # SearchResult indexes for user queries
            {
                'table': 'search_searchresult',
                'name': 'idx_result_user_timestamp',
                'columns': ['user_id', 'timestamp'],
                'description': 'Optimize user search history queries'
            }
        ]

        with connection.cursor() as cursor:
            for index in indexes_to_add:
                try:
                    # Check if index already exists
                    cursor.execute("""
                        SELECT indexname FROM pg_indexes
                        WHERE tablename = %s AND indexname = %s
                    """, [index['table'], index['name']])

                    if cursor.fetchone():
                        if self.verbose:
                            self.stdout.write(f"  Index {index['name']} already exists")
                        continue

                    # Create index
                    columns_str = ', '.join(index['columns'])
                    sql = f"""
                        CREATE INDEX CONCURRENTLY {index['name']}
                        ON {index['table']} ({columns_str})
                    """

                    if self.dry_run:
                        self.stdout.write(f"  Would create: {sql}")
                    else:
                        if self.verbose:
                            self.stdout.write(f"  Creating index: {index['name']}")
                        cursor.execute(sql)
                        self.stdout.write(
                            self.style.SUCCESS(f"  ✓ Created index: {index['name']}")
                        )

                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(f"  ✗ Failed to create index {index['name']}: {str(e)}")
                    )

    def optimize_citations(self):
        """Optimize citation queries with select_related."""
        self.stdout.write('Optimizing citation queries...')

        # Test current citation query performance
        from apps.search.models import ResultCitation
        from apps.documents.models import EmbeddingMetadata

        # Sample query to test optimization
        test_vector_ids = ['test-vector-1', 'test-vector-2', 'test-vector-3']

        if self.verbose:
            self.stdout.write('  Testing optimized EmbeddingMetadata query...')

        start_time = time.time()

        # This is the optimized query from rag_search_service.py
        optimized_query = EmbeddingMetadata.objects.filter(
            vector_id__in=test_vector_ids
        ).select_related('chunk__document', 'chunk__profile')

        # Force evaluation
        list(optimized_query)

        query_time = time.time() - start_time

        if self.verbose:
            self.stdout.write(f"  Optimized query completed in {query_time:.4f}s")

        self.stdout.write(
            self.style.SUCCESS('  ✓ Citation query optimization verified')
        )

    def setup_monitoring(self):
        """Setup query monitoring and performance tracking."""
        self.stdout.write('Setting up query monitoring...')

        # Enable query logging if not already enabled
        if not self.dry_run:
            try:
                with connection.cursor() as cursor:
                    # Check if pg_stat_statements is available
                    cursor.execute("""
                        SELECT EXISTS (
                            SELECT 1 FROM pg_extension WHERE extname = 'pg_stat_statements'
                        )
                    """)

                    has_pg_stat = cursor.fetchone()[0]

                    if has_pg_stat:
                        self.stdout.write(
                            self.style.SUCCESS('  ✓ pg_stat_statements extension available')
                        )

                        # Reset statistics for fresh monitoring
                        cursor.execute("SELECT pg_stat_statements_reset()")
                        self.stdout.write('  ✓ Query statistics reset')

                    else:
                        self.stdout.write(
                            self.style.WARNING('  ⚠ pg_stat_statements extension not available')
                        )

            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'  ✗ Failed to setup monitoring: {str(e)}')
                )

        # Setup application-level query caching
        cache_key = 'db_optimization_enabled'
        if not self.dry_run:
            cache.set(cache_key, True, timeout=86400)  # 24 hours

        self.stdout.write(
            self.style.SUCCESS('  ✓ Query monitoring setup completed')
        )

    def test_performance(self):
        """Test performance improvements."""
        self.stdout.write('Testing performance improvements...')

        from apps.documents.models import EmbeddingMetadata
        from django.db import connection

        # Test bulk query performance
        test_vector_ids = [f'test-vector-{i}' for i in range(10)]

        # Reset query count
        initial_queries = len(connection.queries)

        start_time = time.time()

        # Simulate optimized citation creation query
        embedding_metadata_map = {
            em.vector_id: em.chunk
            for em in EmbeddingMetadata.objects.filter(
                vector_id__in=test_vector_ids
            ).select_related('chunk__document', 'chunk__profile')
        }

        query_time = time.time() - start_time
        final_queries = len(connection.queries)
        queries_executed = final_queries - initial_queries

        self.stdout.write(f'  Performance test results:')
        self.stdout.write(f'    Query time: {query_time:.4f}s')
        self.stdout.write(f'    Queries executed: {queries_executed}')
        self.stdout.write(f'    Vector IDs processed: {len(test_vector_ids)}')

        if queries_executed <= 2:  # Should be 1-2 queries max with optimization
            self.stdout.write(
                self.style.SUCCESS('  ✓ Query optimization working correctly')
            )
        else:
            self.stdout.write(
                self.style.WARNING(f'  ⚠ More queries than expected: {queries_executed}')
            )
