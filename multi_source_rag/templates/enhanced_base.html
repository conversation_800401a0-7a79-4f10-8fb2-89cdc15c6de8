<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Intelligent AI-powered search across your team's knowledge base">
    <meta name="theme-color" content="#2563eb">
    
    <title>{% block title %}Enhanced RAG Search System{% endblock %}</title>

    <!-- Preload critical resources -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://cdn.jsdelivr.net">
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">

    <!-- Critical CSS -->
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-light: #3b82f6;
            --primary-dark: #1d4ed8;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --background: #ffffff;
            --surface: #f8fafc;
            --border-color: #e2e8f0;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background: var(--background);
        }
        
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            transition: opacity 0.3s ease;
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid var(--border-color);
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>

    <!-- External CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Loading screen -->
    <div class="loading-screen" id="loadingScreen">
        <div class="loading-spinner"></div>
    </div>

    <!-- Skip to content link for accessibility -->
    <a href="#main-content" class="visually-hidden-focusable">Skip to main content</a>

    <!-- Enhanced Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white border-bottom sticky-top" id="mainNavbar">
        <div class="container-fluid">
            <a class="navbar-brand d-flex align-items-center" href="{% url 'search:search' %}">
                <i class="fas fa-brain text-primary me-2"></i>
                <span class="fw-bold">RAG Search</span>
                <span class="badge bg-primary ms-2 small">Enhanced</span>
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'search:search' %}">
                            <i class="fas fa-search me-1"></i>Search
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'search:conversations' %}">
                            <i class="fas fa-comments me-1"></i>Conversations
                        </a>
                    </li>
                    {% if user.is_staff %}
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/">
                            <i class="fas fa-cog me-1"></i>Admin
                        </a>
                    </li>
                    {% endif %}
                </ul>

                <!-- Search bar in navbar -->
                <div class="d-flex align-items-center me-3">
                    <form class="d-flex" method="get" action="{% url 'search:search' %}" id="navSearchForm">
                        <div class="input-group input-group-sm">
                            <input class="form-control" type="search" placeholder="Quick search..." name="q" style="min-width: 200px;">
                            <button class="btn btn-outline-primary" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>

                <!-- User menu -->
                <ul class="navbar-nav">
                    {% if user.is_authenticated %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <div class="user-avatar me-2">
                                <i class="fas fa-user-circle fs-5"></i>
                            </div>
                            <span>{{ user.username }}</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><h6 class="dropdown-header">{{ user.get_full_name|default:user.username }}</h6></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="#">
                                    <i class="fas fa-user me-2"></i>Profile
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="#">
                                    <i class="fas fa-cog me-2"></i>Settings
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{% url 'search:conversations' %}">
                                    <i class="fas fa-history me-2"></i>Search History
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form method="post" action="/accounts/logout/" class="m-0">
                                    {% csrf_token %}
                                    <button type="submit" class="dropdown-item text-danger">
                                        <i class="fas fa-sign-out-alt me-2"></i>Logout
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="/accounts/login/">
                            <i class="fas fa-sign-in-alt me-1"></i>Login
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main content area -->
    <main id="main-content" class="main-content">
        <!-- Status messages -->
        {% if messages %}
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        <div class="d-flex align-items-center">
                            {% if message.tags == 'error' or message.tags == 'danger' %}
                                <i class="fas fa-exclamation-triangle me-2"></i>
                            {% elif message.tags == 'warning' %}
                                <i class="fas fa-exclamation-circle me-2"></i>
                            {% elif message.tags == 'success' %}
                                <i class="fas fa-check-circle me-2"></i>
                            {% else %}
                                <i class="fas fa-info-circle me-2"></i>
                            {% endif %}
                            {{ message }}
                        </div>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Page content -->
        {% block content %}{% endblock %}
    </main>

    <!-- Enhanced Footer -->
    <footer class="footer bg-light border-top mt-auto">
        <div class="container-fluid py-4">
            <div class="row">
                <div class="col-md-6">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-brain text-primary me-2"></i>
                        <span class="fw-bold">Enhanced RAG Search System</span>
                    </div>
                    <p class="text-muted small mb-0 mt-1">
                        Intelligent AI-powered search across your team's knowledge base
                    </p>
                </div>
                <div class="col-md-6">
                    <div class="d-flex justify-content-md-end align-items-center">
                        <span class="text-muted small me-3">
                            &copy; {% now "Y" %} Enhanced RAG System
                        </span>
                        <div class="footer-links">
                            <a href="#" class="text-muted me-3 small">Privacy</a>
                            <a href="#" class="text-muted me-3 small">Terms</a>
                            <a href="#" class="text-muted small">Support</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Remove loading screen
            const loadingScreen = document.getElementById('loadingScreen');
            if (loadingScreen) {
                setTimeout(() => {
                    loadingScreen.style.opacity = '0';
                    setTimeout(() => {
                        loadingScreen.style.display = 'none';
                    }, 300);
                }, 100);
            }

            // Initialize tooltips
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function(tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // Initialize popovers
            const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
            popoverTriggerList.map(function(popoverTriggerEl) {
                return new bootstrap.Popover(popoverTriggerEl);
            });

            // Auto-dismiss alerts after 5 seconds
            document.querySelectorAll('.alert:not(.alert-permanent)').forEach(alert => {
                setTimeout(() => {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }, 5000);
            });

            // Enhanced keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                // Ctrl/Cmd + K for global search focus
                if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                    e.preventDefault();
                    const searchInput = document.querySelector('#navSearchForm input[name="q"]');
                    if (searchInput) {
                        searchInput.focus();
                        searchInput.select();
                    }
                }

                // Escape to close modals/dropdowns
                if (e.key === 'Escape') {
                    const openModals = document.querySelectorAll('.modal.show');
                    openModals.forEach(modal => {
                        const bsModal = bootstrap.Modal.getInstance(modal);
                        if (bsModal) bsModal.hide();
                    });

                    const openDropdowns = document.querySelectorAll('.dropdown-menu.show');
                    openDropdowns.forEach(dropdown => {
                        dropdown.classList.remove('show');
                    });
                }
            });

            // Navbar search functionality
            const navSearchForm = document.getElementById('navSearchForm');
            if (navSearchForm) {
                navSearchForm.addEventListener('submit', function(e) {
                    const query = this.querySelector('input[name="q"]').value.trim();
                    if (query.length < 3) {
                        e.preventDefault();
                        alert('Please enter at least 3 characters for search.');
                        return;
                    }
                });
            }
        });

        // Global utility functions
        window.RAGSearch = {
            showToast: function(message, type = 'info') {
                const toastContainer = document.querySelector('.toast-container') || this.createToastContainer();
                const toast = this.createToast(message, type);
                toastContainer.appendChild(toast);
                const bsToast = new bootstrap.Toast(toast);
                bsToast.show();
            },

            createToastContainer: function() {
                const container = document.createElement('div');
                container.className = 'toast-container position-fixed top-0 end-0 p-3';
                container.style.zIndex = '9999';
                document.body.appendChild(container);
                return container;
            },

            createToast: function(message, type) {
                const toast = document.createElement('div');
                toast.className = 'toast';
                toast.setAttribute('role', 'alert');
                toast.innerHTML = `
                    <div class="toast-header">
                        <i class="fas fa-${this.getToastIcon(type)} text-${type} me-2"></i>
                        <strong class="me-auto">RAG Search</strong>
                        <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                    </div>
                    <div class="toast-body">${message}</div>
                `;
                return toast;
            },

            getToastIcon: function(type) {
                const icons = {
                    'success': 'check-circle',
                    'danger': 'exclamation-triangle',
                    'warning': 'exclamation-circle',
                    'info': 'info-circle'
                };
                return icons[type] || 'info-circle';
            }
        };
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>