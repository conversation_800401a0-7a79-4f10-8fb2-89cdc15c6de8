{"timestamp": "2025-05-30T14:50:33.575859", "tenant": "stride", "summary": {"total_tests": 25, "passed_tests": 19, "success_rate": 76.0}, "detailed_results": [{"category": "Basic Retrieval", "name": "Simple Keyword Search", "query": "authentication", "passed": true, "response_time": 150.1869478225708, "result_count": 2, "details": "2 results found", "timestamp": "2025-05-30T14:15:30.715864"}, {"category": "Basic Retrieval", "name": "Phrase Search", "query": "user login process", "passed": true, "response_time": 71.26874017715454, "result_count": 2, "details": "2 results found", "timestamp": "2025-05-30T14:16:41.984651"}, {"category": "Basic Retrieval", "name": "Technical Term Search", "query": "API endpoint configuration", "passed": true, "response_time": 93.*************, "result_count": 2, "details": "2 results found", "timestamp": "2025-05-30T14:18:15.796834"}, {"category": "Basic Retrieval", "name": "Date-based Query", "query": "recent discussions about deployment", "passed": true, "response_time": 76.**************, "result_count": 2, "details": "2 results found", "timestamp": "2025-05-30T14:19:32.020270"}, {"category": "Domain Routing", "name": "Engineering Domain Query", "query": "How do we handle authentication in our system?", "passed": true, "response_time": 79.**************, "result_count": 2, "details": "2 results found", "timestamp": "2025-05-30T14:20:51.805629"}, {"category": "Domain Routing", "name": "Technical Discussion Query", "query": "What are the best practices for API design?", "passed": true, "response_time": 103.**************, "result_count": 2, "details": "2 results found", "timestamp": "2025-05-30T14:22:35.509434"}, {"category": "Domain Routing", "name": "Problem-solving Query", "query": "How to debug connection issues?", "passed": true, "response_time": 110.**************, "result_count": 2, "details": "2 results found", "timestamp": "2025-05-30T14:24:25.988242"}, {"category": "Complexity Awareness", "name": "Simple Question", "query": "What is authentication?", "passed": true, "response_time": 60.**************, "result_count": 2, "details": "2 results found", "timestamp": "2025-05-30T14:25:26.036408"}, {"category": "Complexity Awareness", "name": "Moderate Question", "query": "How does our authentication system work with external APIs?", "passed": true, "response_time": 82.**************, "result_count": 2, "details": "2 results found", "timestamp": "2025-05-30T14:26:49.030721"}, {"category": "Complexity Awareness", "name": "Complex Question", "query": "What are the security implications of our current authentication architecture and how can we improve it?", "passed": true, "response_time": 113.**************, "result_count": 2, "details": "2 results found", "timestamp": "2025-05-30T14:28:42.402160"}, {"category": "Multi-step Reasoning", "name": "Causal Reasoning", "query": "Why might users experience login failures and how can we prevent them?", "passed": true, "response_time": 103.28368902206421, "result_count": 2, "details": "2 results found", "timestamp": "2025-05-30T14:30:25.685938"}, {"category": "Multi-step Reasoning", "name": "Comparative Analysis", "query": "Compare different authentication methods discussed in our team", "passed": true, "response_time": 81.74361491203308, "result_count": 2, "details": "2 results found", "timestamp": "2025-05-30T14:31:47.429597"}, {"category": "Multi-step Reasoning", "name": "Problem-Solution Mapping", "query": "What solutions have we implemented for common API issues?", "passed": true, "response_time": 80.32827496528625, "result_count": 2, "details": "2 results found", "timestamp": "2025-05-30T14:33:07.757904"}, {"category": "Query Enhancement", "name": "Synonym Expansion", "query": "auth", "passed": true, "response_time": 44.33229899406433, "result_count": 2, "details": "2 results found", "timestamp": "2025-05-30T14:33:52.090284"}, {"category": "Query Enhancement", "name": "Context Enhancement", "query": "login issues", "passed": true, "response_time": 78.72662997245789, "result_count": 2, "details": "2 results found", "timestamp": "2025-05-30T14:35:10.816939"}, {"category": "Query Enhancement", "name": "Technical Term Enhancement", "query": "API problems", "passed": true, "response_time": 79.75798606872559, "result_count": 2, "details": "2 results found", "timestamp": "2025-05-30T14:36:30.574961"}, {"category": "Hybrid Search", "name": "Semantic + Keyword Hybrid", "query": "authentication best practices", "passed": false, "response_time": 95.28204894065857, "result_count": 2, "details": "Expected 3 results, got 2", "timestamp": "2025-05-30T14:38:05.857076"}, {"category": "Hybrid Search", "name": "Temporal + Semantic Hybrid", "query": "recent authentication improvements", "passed": true, "response_time": 101.06799173355103, "result_count": 2, "details": "2 results found", "timestamp": "2025-05-30T14:39:46.925110"}, {"category": "Hybrid Search", "name": "Multi-domain Hybrid", "query": "system architecture discussions", "passed": true, "response_time": 78.51762413978577, "result_count": 2, "details": "2 results found", "timestamp": "2025-05-30T14:41:05.442776"}, {"category": "Performance", "name": "Response Time Test", "query": "authentication system overview", "passed": false, "response_time": 78.60073804855347, "result_count": 2, "details": "Response time 78.60s exceeded 5.0s", "timestamp": "2025-05-30T14:42:24.043604"}, {"category": "Performance", "name": "Large Result Set", "query": "engineering discussions", "passed": false, "response_time": 65.190838098526, "result_count": 2, "details": "Expected 10 results, got 2", "timestamp": "2025-05-30T14:43:29.234484"}, {"category": "Performance", "name": "Complex Query Performance", "query": "What are the architectural patterns and security considerations for implementing scalable authentication systems in microservices?", "passed": false, "response_time": 161.7840850353241, "result_count": 2, "details": "Response time 161.78s exceeded 15.0s", "timestamp": "2025-05-30T14:46:11.018606"}, {"category": "Response Quality", "name": "Citation Quality", "query": "authentication implementation details", "passed": false, "response_time": 77.83165526390076, "result_count": 2, "details": "No citations found in results", "timestamp": "2025-05-30T14:47:28.850346"}, {"category": "Response Quality", "name": "Response Completeness", "query": "How do we handle user authentication?", "passed": false, "response_time": 90.16730213165283, "result_count": 2, "details": "Results too short (avg: 0 chars)", "timestamp": "2025-05-30T14:48:59.017700"}, {"category": "Response Quality", "name": "Technical Accuracy", "query": "API security best practices", "passed": true, "response_time": 94.55718469619751, "result_count": 2, "details": "2 results found", "timestamp": "2025-05-30T14:50:33.574911"}]}