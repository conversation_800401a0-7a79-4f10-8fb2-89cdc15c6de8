# RAGSearch Troubleshooting Guide

## Table of Contents

1. [Common Issues](#common-issues)
2. [Performance Problems](#performance-problems)
3. [Search Issues](#search-issues)
4. [Data Ingestion Problems](#data-ingestion-problems)
5. [Database Issues](#database-issues)
6. [LLM and Embedding Issues](#llm-and-embedding-issues)
7. [Deployment Issues](#deployment-issues)
8. [Debugging Tools](#debugging-tools)

## Common Issues

### Application Won't Start

#### Django Server Errors

**Symptom**: Server fails to start with import errors
```bash
ModuleNotFoundError: No module named 'apps.core'
```

**Solution**:
```bash
# Ensure you're in the correct directory
cd multi_source_rag

# Check Python path
export PYTHONPATH=/path/to/RAGSearch/multi_source_rag:$PYTHONPATH

# Verify virtual environment
poetry shell
poetry install
```

#### Database Connection Errors

**Symptom**: 
```
django.db.utils.OperationalError: could not connect to server
```

**Solution**:
```bash
# Check PostgreSQL status
sudo systemctl status postgresql

# Verify connection settings
psql -h localhost -U ragsearch -d multi_source_rag

# Check .env configuration
cat .env | grep DB_
```

#### Missing Environment Variables

**Symptom**:
```
KeyError: 'SECRET_KEY'
```

**Solution**:
```bash
# Copy environment template
cp .env.example .env

# Generate secret key
python -c "from django.core.management.utils import get_random_secret_key; print(get_random_secret_key())"

# Update .env file with generated key
```

### Vector Database Issues

#### Qdrant Connection Failed

**Symptom**:
```
ConnectionError: Cannot connect to Qdrant at localhost:6333
```

**Solution**:
```bash
# Check Qdrant status
docker ps | grep qdrant

# Start Qdrant if not running
docker run -p 6333:6333 qdrant/qdrant

# Test connection
curl http://localhost:6333/health
```

#### Collection Not Found

**Symptom**:
```
QdrantException: Collection 'tenant_xyz_default' not found
```

**Solution**:
```bash
# List existing collections
curl http://localhost:6333/collections

# Create collection manually
python manage.py shell
>>> from apps.core.utils.collection_manager import create_collection
>>> create_collection("tenant_xyz_default", dimension=768)
```

## Performance Problems

### Slow Search Responses

#### Diagnosis
```bash
# Check search performance
python manage.py shell
>>> from apps.search.services import UnifiedRAGService
>>> import time
>>> service = UnifiedRAGService("your_tenant", user)
>>> start = time.time()
>>> result = service.search("test query")
>>> print(f"Search took: {time.time() - start:.2f}s")
```

#### Solutions

**1. Service Caching Issues**
```bash
# Clear service cache
python manage.py shell
>>> from apps.core.utils.service_cache import clear_all_caches
>>> clear_all_caches()
```

**2. Database Performance**
```sql
-- Check slow queries
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;

-- Analyze table statistics
ANALYZE documents_document;
ANALYZE documents_chunk;
```

**3. Vector Search Optimization**
```python
# Check collection size
from apps.core.utils.collection_manager import get_qdrant_client
client = get_qdrant_client()
info = client.get_collection("your_collection")
print(f"Points: {info.points_count}")
```

### High Memory Usage

#### Diagnosis
```bash
# Monitor memory usage
htop
# or
ps aux | grep python | head -10
```

#### Solutions

**1. Embedding Model Caching**
```python
# Check embedding model memory usage
import psutil
import os
process = psutil.Process(os.getpid())
print(f"Memory usage: {process.memory_info().rss / 1024 / 1024:.2f} MB")
```

**2. Reduce Model Loading**
```bash
# Use consistent embedding model
export EMBEDDING_MODEL_NAME=BAAI/bge-base-en-v1.5
# Restart application
```

## Search Issues

### No Search Results

#### Diagnosis Steps

**1. Check Data Ingestion**
```bash
# Verify documents exist
python manage.py shell
>>> from apps.documents.models import Document
>>> Document.objects.filter(tenant__slug="your_tenant").count()
```

**2. Check Vector Collections**
```bash
# Check Qdrant collections
curl http://localhost:6333/collections
```

**3. Test Embedding Generation**
```python
from apps.core.utils.llama_index_embeddings import get_embedding_model_for_content
model = get_embedding_model_for_content()
embedding = model.get_text_embedding("test query")
print(f"Embedding dimension: {len(embedding)}")
```

#### Solutions

**1. Re-index Documents**
```bash
# Clean and re-ingest data
python manage.py clean_databases --confirm
python manage.py ingest_slack_data --tenant your_tenant --token your_token --channels your_channels
```

**2. Check Collection Names**
```python
from apps.core.utils.collection_manager import get_collection_name
collection = get_collection_name("your_tenant", check_existence=True)
print(f"Using collection: {collection}")
```

### Poor Search Quality

#### Diagnosis

**1. Check Query Processing**
```python
from apps.search.services import UnifiedRAGService
service = UnifiedRAGService("your_tenant", user)
result, docs = service.search("your query", top_k=20)
print(f"Retrieved {len(docs)} documents")
for doc, score in docs[:5]:
    print(f"Score: {score:.3f}, Content: {doc.page_content[:100]}...")
```

**2. Analyze Embedding Quality**
```python
# Test embedding similarity
from apps.core.utils.llama_index_embeddings import get_embedding_model_for_content
model = get_embedding_model_for_content()

query_emb = model.get_text_embedding("your query")
doc_emb = model.get_text_embedding("document content")

# Calculate similarity
import numpy as np
similarity = np.dot(query_emb, doc_emb) / (np.linalg.norm(query_emb) * np.linalg.norm(doc_emb))
print(f"Similarity: {similarity:.3f}")
```

#### Solutions

**1. Improve Query Expansion**
```python
# Enable query expansion
result = service.search(
    "your query",
    use_query_expansion=True,
    use_hybrid_search=True
)
```

**2. Adjust Relevance Threshold**
```python
# Lower relevance threshold for more results
result = service.search(
    "your query",
    min_relevance_score=0.05  # Lower from default 0.10
)
```

## Data Ingestion Problems

### Slack Integration Issues

#### Authentication Errors

**Symptom**:
```
SlackApiError: invalid_auth
```

**Solution**:
```bash
# Verify token permissions
curl -H "Authorization: Bearer xoxb-your-token" \
     https://slack.com/api/auth.test

# Check required scopes
curl -H "Authorization: Bearer xoxb-your-token" \
     https://slack.com/api/auth.test | jq '.response_metadata.scopes'
```

#### Rate Limiting

**Symptom**:
```
SlackApiError: rate_limited
```

**Solution**:
```python
# Implement rate limiting in ingestion
python manage.py ingest_slack_data \
    --tenant your_tenant \
    --token your_token \
    --channels your_channels \
    --rate-limit 1  # 1 request per second
```

### GitHub Integration Issues

#### API Rate Limits

**Symptom**:
```
GithubException: 403 API rate limit exceeded
```

**Solution**:
```bash
# Check rate limit status
curl -H "Authorization: token ghp_your_token" \
     https://api.github.com/rate_limit

# Use authenticated requests
export GITHUB_TOKEN=ghp_your_token
python manage.py ingest_github_data --repo owner/repo --token $GITHUB_TOKEN
```

#### Permission Errors

**Symptom**:
```
GithubException: 404 Not Found
```

**Solution**:
```bash
# Verify repository access
curl -H "Authorization: token ghp_your_token" \
     https://api.github.com/repos/owner/repo

# Check token permissions
curl -H "Authorization: token ghp_your_token" \
     https://api.github.com/user | jq '.permissions'
```

## Database Issues

### PostgreSQL Problems

#### Connection Pool Exhaustion

**Symptom**:
```
OperationalError: connection pool exhausted
```

**Solution**:
```python
# In settings.py
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'OPTIONS': {
            'MAX_CONNS': 20,
            'MIN_CONNS': 5,
        }
    }
}
```

#### pgvector Extension Missing

**Symptom**:
```
ProgrammingError: type "vector" does not exist
```

**Solution**:
```sql
-- Connect to database
\c multi_source_rag

-- Install extension
CREATE EXTENSION IF NOT EXISTS vector;

-- Verify installation
\dx vector
```

### Migration Issues

#### Migration Conflicts

**Symptom**:
```
django.db.migrations.exceptions.InconsistentMigrationHistory
```

**Solution**:
```bash
# Reset migrations (development only)
python manage.py migrate --fake-initial

# Or reset specific app
python manage.py migrate documents zero
python manage.py migrate documents
```

## LLM and Embedding Issues

### Ollama Connection Problems

#### Service Not Running

**Symptom**:
```
ConnectionError: Cannot connect to Ollama at localhost:11434
```

**Solution**:
```bash
# Start Ollama service
ollama serve

# Pull required model
ollama pull llama3

# Test connection
curl http://localhost:11434/api/version
```

#### Model Loading Issues

**Symptom**:
```
OllamaError: model 'llama3' not found
```

**Solution**:
```bash
# List available models
ollama list

# Pull missing model
ollama pull llama3

# Verify model
ollama show llama3
```

### Embedding Dimension Mismatch

#### Diagnosis

**Symptom**:
```
ValueError: Embedding dimension mismatch: expected 768, got 384
```

**Solution**:
```bash
# Check current embedding configuration
python manage.py shell
>>> from apps.core.utils.embedding_consistency import get_embedding_model_info
>>> print(get_embedding_model_info())

# Validate consistency
>>> from apps.core.utils.embedding_consistency import validate_embedding_consistency
>>> validate_embedding_consistency()
```

#### Fix Dimension Mismatch
```bash
# Set consistent embedding model
export EMBEDDING_MODEL_NAME=BAAI/bge-base-en-v1.5

# Clear and re-ingest data
python manage.py clean_databases --confirm
python manage.py ingest_slack_data --tenant your_tenant --token your_token --channels your_channels
```

## Deployment Issues

### Docker Problems

#### Container Won't Start

**Symptom**:
```
docker: Error response from daemon: container failed to start
```

**Solution**:
```bash
# Check container logs
docker logs ragsearch_app

# Check resource usage
docker stats

# Rebuild image
docker-compose build --no-cache
docker-compose up -d
```

#### Volume Mount Issues

**Symptom**:
```
PermissionError: [Errno 13] Permission denied
```

**Solution**:
```bash
# Fix permissions
sudo chown -R 1000:1000 /path/to/volumes

# Or use user mapping in docker-compose.yml
user: "1000:1000"
```

### SSL Certificate Issues

#### Certificate Expired

**Symptom**:
```
SSL: CERTIFICATE_VERIFY_FAILED
```

**Solution**:
```bash
# Renew Let's Encrypt certificate
sudo certbot renew

# Check certificate status
sudo certbot certificates

# Test SSL configuration
openssl s_client -connect your-domain.com:443
```

## Debugging Tools

### Django Debug Commands

#### Database Inspection
```bash
# Check database connectivity
python manage.py dbshell

# Inspect models
python manage.py shell
>>> from apps.documents.models import Document
>>> Document.objects.all().count()
```

#### Cache Debugging
```bash
# Clear all caches
python manage.py shell
>>> from django.core.cache import cache
>>> cache.clear()
```

### Performance Profiling

#### Search Performance
```python
# Profile search performance
import cProfile
import pstats

def profile_search():
    from apps.search.services import UnifiedRAGService
    service = UnifiedRAGService("your_tenant", user)
    return service.search("test query")

cProfile.run('profile_search()', 'search_profile.prof')
stats = pstats.Stats('search_profile.prof')
stats.sort_stats('cumulative').print_stats(10)
```

#### Memory Profiling
```python
# Memory usage profiling
import tracemalloc

tracemalloc.start()
# Run your code here
current, peak = tracemalloc.get_traced_memory()
print(f"Current memory usage: {current / 1024 / 1024:.2f} MB")
print(f"Peak memory usage: {peak / 1024 / 1024:.2f} MB")
tracemalloc.stop()
```

### Log Analysis

#### Search Logs
```bash
# Filter search-related logs
grep "search" /var/log/ragsearch/django.log | tail -50

# Performance logs
grep "duration" /var/log/ragsearch/django.log | tail -20
```

#### Error Logs
```bash
# Recent errors
grep "ERROR" /var/log/ragsearch/django.log | tail -20

# Specific error types
grep "ConnectionError\|TimeoutError" /var/log/ragsearch/django.log
```

For additional support, check the [User Guide](USER_GUIDE.md) and [Developer Guide](DEVELOPER_GUIDE.md).
