# RAGSearch System Architecture

## System Overview

RAGSearch is a production-ready Django-based multi-tenant Retrieval Augmented Generation (RAG) system that intelligently searches across multiple data sources (Slack, GitHub, documents) to provide contextual AI-powered responses. The system follows a modular, scalable architecture with enterprise-grade features.

## Technology Stack

### **Core Framework**
- **Backend**: Django 4.2+ with Django REST Framework
- **Database**: PostgreSQL 14+ with pgvector extension
- **Vector Database**: Qdrant for similarity search
- **Package Management**: Poetry for dependency management

### **AI/ML Stack**
- **RAG Framework**: LlamaIndex (latest) for advanced retrieval
- **Language Models**: Llama 3 (via Ollama) + Gemini Flash
- **Embedding Models**: BAAI/bge-base-en-v1.5 (768d) for consistent embeddings
- **Search**: Hybrid vector + BM25 with query fusion

### **Frontend & Infrastructure**
- **Templates**: Django templates with modern CSS
- **Styling**: Inter font, responsive design, accessibility (WCAG 2.1 AA)
- **Caching**: Redis for service and query caching
- **Monitoring**: Prometheus metrics and structured logging

## Core Components

### 1. Multi-Tenant Architecture

The system is built with multi-tenancy as a core principle:

- Each tenant has isolated data spaces (documents, users, vector collections)
- Tenant-specific configurations for LLMs and embedding models
- Cross-tenant functionality is prevented by design
- Complete data isolation through foreign keys and collection naming

### 2. Document Processing Pipeline

The document ingestion and processing pipeline consists of:

1. **Source Connectors**: Interfaces to external data sources (Slack, GitHub, etc.)
2. **Document Extraction**: Pulling raw content from sources
3. **Document Processing**: Cleaning and normalizing content
4. **Chunking**: Breaking documents into semantic chunks using conversation-aware chunking
5. **Embedding**: Converting chunks to vector embeddings
6. **Storage**: Storing both raw documents in PostgreSQL and embeddings in Qdrant

### 3. Advanced RAG Implementation

The RAG system implements several advanced techniques:

- **Conversation-Aware Chunking**: Preserves message boundaries and conversation context
- **Anti-Fragmentation Processing**: Reduces document fragmentation through intelligent clustering
- **Enhanced Query Engine**: Detail-seeking query detection and structured information extraction
- **Topic Boosting**: Improved relevance through topic-based document boosting
- **Hybrid Search**: Combines vector similarity and keyword matching
- **Intent Classification**: Routes queries to specialized collections
- **Dynamic Relevance Threshold**: Adjusts relevance thresholds based on query characteristics

### 4. Vector Search

The vector search functionality:

- Uses Qdrant for efficient similarity search
- Supports metadata filtering
- Provides relevance scoring
- Handles hybrid search (vector + keyword)
- Implements context-aware retrieval

## System Architecture Diagram

```
┌─────────────────────────────────────────────────────────────────────────┐
│                           Django Application                             │
├─────────────┬─────────────┬─────────────┬─────────────┬─────────────────┤
│  Accounts   │  Documents  │    Search   │    Core     │       API       │
│   Module    │   Module    │   Module    │   Module    │     Module      │
├─────────────┴─────────────┴─────────────┴─────────────┴─────────────────┤
│                          Service Layer                                   │
├─────────────┬─────────────┬─────────────┬─────────────┬─────────────────┤
│  Ingestion  │  Chunking   │  Embedding  │    RAG      │      LLM        │
│  Services   │  Services   │  Services   │  Services   │    Services     │
├─────────────┴─────────────┴─────────────┴─────────────┴─────────────────┤
│                          Storage Layer                                   │
├─────────────────────────────┬─────────────────────────────────────────┬─┘
│      PostgreSQL Database     │        Qdrant Vector Database          │
└─────────────────────────────┴─────────────────────────────────────────┘
```

## Module Structure

### Accounts Module
- Tenant management
- User profiles
- Authentication
- Platform user profiles

### Documents Module
- Document source configuration
- Document ingestion
- Document processing
- Chunking strategies
- Embedding generation

### Search Module
- Query processing
- Vector search
- Response generation
- Citation tracking
- Conversation management

### Core Module
- Shared utilities
- Vector store integration
- LLM integration
- Configuration management

### API Module
- REST API endpoints
- Serializers
- Authentication
- Rate limiting

## Processing Flows

### Document Ingestion Flow

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│   Source    │     │ Ingestion   │     │  Document   │     │  Chunking   │     │  Embedding  │
│  Connector  │────>│  Service    │────>│ Processing  │────>│  Service    │────>│  Service    │
└─────────────┘     └─────────────┘     └─────────────┘     └─────────────┘     └─────────────┘
                           │                                                            │
                           │                                                            ▼
                           │                                                     ┌─────────────┐
                           │                                                     │   Qdrant    │
                           │                                                     │   Vector    │
                           ▼                                                     │  Database   │
                    ┌─────────────┐                                              └─────────────┘
                    │ PostgreSQL  │
                    │  Database   │
                    └─────────────┘
```

### RAG Query Flow

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│    User     │     │   Search    │     │   Vector    │     │     RAG     │     │    LLM      │
│   Query     │────>│  Service    │────>│   Search    │────>│   Service   │────>│  Service    │
└─────────────┘     └─────────────┘     └─────────────┘     └─────────────┘     └─────────────┘
                           │                                        │                   │
                           ▼                                        │                   ▼
                    ┌─────────────┐                                 │            ┌─────────────┐
                    │  Search     │                                 │            │  Generated  │
                    │  Results    │<────────────────────────────────┘            │  Response   │
                    └─────────────┘                                              └─────────────┘
```

## Advanced Features

### Conversation-Aware Chunking

The system implements conversation-aware chunking that:
- Preserves message boundaries
- Maintains conversation flow
- Handles question-answer pairs
- Provides overlap between chunks to maintain context
- Optimizes for thread-based conversations

### Enhanced Message Processing

The message processing pipeline includes:
- Thread-based clustering (highest priority)
- Temporal clustering with conversation flow detection
- Semantic clustering for topic-related messages
- Cluster optimization (splitting/merging)
- Anti-fragmentation optimization

### Improved Query Engine

The query engine implements:
- Detail-seeking query detection
- Structured information extraction
- Enhanced retrieval strategies
- Content boosting for relevant documents
- Confidence scoring and citation validation

## Current Functionality

The system currently supports:

- Ingestion of Slack conversations
- Document chunking and embedding
- Vector search via Qdrant
- RAG with Llama 3
- Multi-tenant isolation
- Intent-aware document processing and search
- Search API with RAG capabilities and citation support

## Future Enhancements

Planned enhancements include:

1. Additional source connectors (GitHub, Notion, etc.)
2. Document refresh/update strategy
3. Enhanced chunking strategies for different content types
4. More advanced RAG techniques (query expansion, reranking)
5. Improved error handling and logging
6. Comprehensive test suite
7. Enhanced UI/UX for document management
8. User feedback mechanisms for RAG responses
