
# Security and Architecture Component Test Report

**Generated:** 2025-05-30T13:09:01.190763

## Summary
- **Total Tests:** 5
- **Passed:** 4
- **Failed:** 1
- **Success Rate:** 80.0%
- **Total Duration:** 0.06s

## Test Results

### ❌ Input Sanitizer
- **Status:** FAILED
- **Duration:** 0.00s
- **Message:** Input sanitization test failed: No module named 'apps.documents.models'

### ✅ Response Schemas
- **Status:** PASSED
- **Duration:** 0.00s
- **Message:** All response schema tests passed

### ✅ Configuration System
- **Status:** PASSED
- **Duration:** 0.05s
- **Message:** All configuration tests passed

### ✅ Dependency Injection
- **Status:** PASSED
- **Duration:** 0.00s
- **Message:** All dependency injection tests passed

### ✅ Observability Components
- **Status:** PASSED
- **Duration:** 0.00s
- **Message:** All observability tests passed

