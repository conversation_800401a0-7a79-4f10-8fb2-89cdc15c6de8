# RAGSearch User Guide

## Table of Contents

1. [Getting Started](#getting-started)
2. [Data Source Configuration](#data-source-configuration)
3. [Search Interface](#search-interface)
4. [Conversation Management](#conversation-management)
5. [Admin Interface](#admin-interface)
6. [Advanced Features](#advanced-features)
7. [Troubleshooting](#troubleshooting)

## Getting Started

### First Login

1. **Access the application** at `http://localhost:8000`
2. **Login** with your credentials or register a new account
3. **Navigate to Search** to start using the RAG system

### Quick Start

1. **Configure Data Sources**: Set up Slack and GitHub integrations
2. **Ingest Data**: Import your team's conversations and repositories
3. **Start Searching**: Ask questions and get AI-powered answers with citations

## Data Source Configuration

### Slack Integration

#### Prerequisites
- Slack workspace admin access
- Bot token with appropriate permissions

#### Setup Steps

1. **Create Slack App**:
   - Go to [Slack API](https://api.slack.com/apps)
   - Create new app for your workspace
   - Add bot token scopes: `channels:history`, `channels:read`, `users:read`

2. **Configure in RAGSearch**:
   ```bash
   # Via management command
   python manage.py ingest_slack_data \
     --tenant your_tenant \
     --token xoxb-your-slack-token \
     --channels C1234567890,C0987654321 \
     --days 30
   ```

3. **Supported Data**:
   - Channel messages and threads
   - User profiles and mentions
   - File attachments and links
   - Message reactions and replies

#### Channel Selection
- **Engineering channels**: Technical discussions, bug reports
- **Product channels**: Feature requests, user feedback
- **General channels**: Company announcements, team updates

### GitHub Integration

#### Prerequisites
- GitHub repository access
- Personal access token or GitHub App

#### Setup Steps

1. **Create GitHub Token**:
   - Go to GitHub Settings > Developer settings > Personal access tokens
   - Create token with `repo` scope for private repos or `public_repo` for public

2. **Configure in RAGSearch**:
   ```bash
   # Via management command
   python manage.py ingest_github_data \
     --repo owner/repository \
     --token ghp_your_github_token \
     --content-types pull_request,issue,wiki,discussions
   ```

3. **Supported Data**:
   - **Pull Requests**: Code reviews, discussions, file changes
   - **Issues**: Bug reports, feature requests, project tracking
   - **Wiki Pages**: Documentation and guides
   - **Discussions**: Community Q&A and announcements

#### Content Types
- **pull_request**: Code reviews and technical discussions
- **issue**: Bug reports and feature requests
- **wiki**: Documentation and knowledge base
- **discussions**: Community Q&A and announcements

### File-Based Documents

#### Supported Formats
- **Text files**: `.txt`, `.md`, `.rst`
- **Documents**: `.pdf`, `.docx`, `.html`
- **Code files**: `.py`, `.js`, `.java`, `.cpp`

#### Upload Process
1. **Admin Interface**: Upload via Django admin
2. **Bulk Import**: Use management commands for large datasets
3. **API Upload**: Programmatic upload via REST API

## Search Interface

### Basic Search

1. **Enter your question** in natural language
2. **Select search options**:
   - **Hybrid Search**: Combines vector and keyword search
   - **Query Expansion**: Uses AI to expand your query
   - **Multi-Step Reasoning**: Breaks down complex questions

3. **Review results** with automatic citations

### Search Features

#### Query Types
- **Factual Questions**: "What is the current API rate limit?"
- **Procedural Questions**: "How do I deploy the application?"
- **Comparative Questions**: "What's the difference between v1 and v2?"
- **Timeline Questions**: "What happened in the last sprint?"

#### Advanced Options
- **Source Filtering**: Search specific data sources
- **Date Filtering**: Limit results to specific time periods
- **Content Type Filtering**: Focus on specific types of content

### Understanding Results

#### Response Format
- **AI Summary**: Contextual answer to your question
- **Source Citations**: Clickable references to original content
- **Confidence Indicators**: High/Medium/Low confidence levels
- **Related Documents**: Additional relevant sources

#### Citation System
- **[1], [2], [3]**: Numbered citations in the response
- **Source Cards**: Detailed information about each source
- **Direct Links**: Click to view original content

## Conversation Management

### Starting Conversations

1. **New Conversation**: Each search starts a new conversation
2. **Follow-up Questions**: Ask related questions in the same conversation
3. **Context Preservation**: System remembers previous questions and answers

### Conversation Features

#### Multi-Turn Dialogue
- **Context Awareness**: System remembers conversation history
- **Follow-up Questions**: "Can you explain that further?"
- **Clarifications**: "What did you mean by X?"

#### Conversation History
- **View Past Conversations**: Access previous discussions
- **Search Conversations**: Find specific topics discussed
- **Export Conversations**: Save important discussions

### Best Practices

#### Effective Questions
- **Be Specific**: "How do I configure Redis caching?" vs "How do I cache?"
- **Provide Context**: "In the user authentication module, how do I..."
- **Ask Follow-ups**: Build on previous answers for deeper understanding

#### Conversation Flow
1. **Start Broad**: "Tell me about the authentication system"
2. **Get Specific**: "How do I implement OAuth2?"
3. **Dive Deep**: "What are the security considerations?"

## Admin Interface

### Accessing Admin

1. **Navigate** to `http://localhost:8000/admin/`
2. **Login** with superuser credentials
3. **Manage** tenants, users, and data sources

### Key Admin Functions

#### Tenant Management
- **Create Tenants**: Set up new organizations
- **User Assignment**: Assign users to tenants
- **Data Isolation**: Ensure proper data separation

#### Data Source Management
- **Configure Sources**: Set up Slack and GitHub integrations
- **Monitor Ingestion**: Track data import progress
- **Manage Collections**: Organize vector collections

#### System Monitoring
- **Search Analytics**: Track query patterns and performance
- **Error Monitoring**: Review system errors and issues
- **Performance Metrics**: Monitor response times and resource usage

## Advanced Features

### Query Expansion (HyDE)
- **Automatic Enhancement**: System expands queries for better results
- **Hypothetical Documents**: Creates ideal document descriptions
- **Improved Recall**: Finds more relevant content

### Multi-Step Reasoning
- **Complex Questions**: Breaks down multi-part questions
- **Sub-Questions**: Generates and answers component questions
- **Comprehensive Answers**: Combines results for complete responses

### Hybrid Search
- **Vector Search**: Semantic similarity matching
- **Keyword Search**: Traditional BM25 text matching
- **Result Fusion**: Combines both approaches for optimal results

### Intent Classification
- **Automatic Routing**: Directs queries to appropriate collections
- **Specialized Processing**: Different handling for different query types
- **Optimized Retrieval**: Tailored search strategies per intent

## Troubleshooting

### Common Issues

#### No Search Results
- **Check Data Sources**: Ensure data has been ingested
- **Verify Permissions**: Confirm access to relevant channels/repos
- **Try Different Queries**: Rephrase your question

#### Slow Performance
- **Check System Resources**: Monitor CPU and memory usage
- **Review Query Complexity**: Simplify complex questions
- **Contact Admin**: Report persistent performance issues

#### Incorrect Results
- **Provide Feedback**: Use feedback mechanisms to improve results
- **Try Alternative Phrasing**: Rephrase your question
- **Check Source Quality**: Verify the quality of ingested data

### Getting Help

#### Support Channels
- **Documentation**: Check this guide and other docs
- **Admin Contact**: Reach out to your system administrator
- **Issue Reporting**: Use the feedback system for bugs

#### Best Practices
- **Clear Questions**: Ask specific, well-formed questions
- **Provide Context**: Include relevant background information
- **Use Feedback**: Rate responses to improve the system

For technical issues and advanced configuration, see the [Troubleshooting Guide](TROUBLESHOOTING.md).
