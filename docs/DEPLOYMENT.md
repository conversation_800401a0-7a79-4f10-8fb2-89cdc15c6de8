# RAGSearch Deployment Guide

## Table of Contents

1. [Production Environment](#production-environment)
2. [Infrastructure Requirements](#infrastructure-requirements)
3. [Database Setup](#database-setup)
4. [Application Deployment](#application-deployment)
5. [Configuration](#configuration)
6. [Monitoring & Logging](#monitoring--logging)
7. [Backup & Recovery](#backup--recovery)
8. [Security](#security)

## Production Environment

### System Requirements

#### Minimum Requirements
- **CPU**: 4 cores
- **RAM**: 16GB
- **Storage**: 100GB SSD
- **Network**: 1Gbps

#### Recommended Requirements
- **CPU**: 8+ cores
- **RAM**: 32GB+
- **Storage**: 500GB+ NVMe SSD
- **Network**: 10Gbps

### Operating System

**Supported Platforms**:
- Ubuntu 20.04+ LTS
- CentOS 8+
- RHEL 8+
- Docker containers

## Infrastructure Requirements

### Core Services

#### PostgreSQL Database
```yaml
# Recommended configuration
version: PostgreSQL 14+
extensions: pgvector
memory: 8GB+
storage: 200GB+ SSD
connections: 100+
```

#### Qdrant Vector Database
```yaml
# Recommended configuration
version: Latest stable
memory: 8GB+
storage: 100GB+ SSD
collections: Multiple tenant collections
```

#### Application Server
```yaml
# Django application
workers: 4-8 (CPU cores)
memory_per_worker: 2GB
max_requests: 1000
timeout: 300s
```

### Load Balancing

#### Nginx Configuration
```nginx
upstream ragsearch_app {
    server 127.0.0.1:8000;
    server 127.0.0.1:8001;
    server 127.0.0.1:8002;
    server 127.0.0.1:8003;
}

server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://ragsearch_app;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 300s;
    }
    
    location /static/ {
        alias /path/to/static/files/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

## Database Setup

### PostgreSQL Configuration

#### Installation
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install postgresql-14 postgresql-contrib-14

# Install pgvector extension
sudo apt install postgresql-14-pgvector

# Or compile from source
git clone https://github.com/pgvector/pgvector.git
cd pgvector
make
sudo make install
```

#### Database Configuration
```sql
-- Create database and user
CREATE DATABASE multi_source_rag;
CREATE USER ragsearch WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE multi_source_rag TO ragsearch;

-- Enable pgvector extension
\c multi_source_rag
CREATE EXTENSION vector;
```

#### Performance Tuning
```postgresql
# postgresql.conf
shared_buffers = 4GB
effective_cache_size = 12GB
maintenance_work_mem = 1GB
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100
random_page_cost = 1.1
effective_io_concurrency = 200
work_mem = 256MB
min_wal_size = 1GB
max_wal_size = 4GB
```

### Qdrant Setup

#### Docker Deployment
```yaml
# docker-compose.yml
version: '3.8'
services:
  qdrant:
    image: qdrant/qdrant:latest
    ports:
      - "6333:6333"
    volumes:
      - qdrant_data:/qdrant/storage
    environment:
      - QDRANT__SERVICE__HTTP_PORT=6333
      - QDRANT__SERVICE__GRPC_PORT=6334
    restart: unless-stopped

volumes:
  qdrant_data:
```

#### Configuration
```yaml
# qdrant_config.yaml
service:
  http_port: 6333
  grpc_port: 6334
  max_request_size_mb: 32

storage:
  storage_path: ./storage
  snapshots_path: ./snapshots
  on_disk_payload: true
  
cluster:
  enabled: false
```

## Application Deployment

### Docker Deployment

#### Dockerfile
```dockerfile
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY pyproject.toml poetry.lock ./
RUN pip install poetry && \
    poetry config virtualenvs.create false && \
    poetry install --no-dev

# Copy application
COPY . .

# Collect static files
RUN python multi_source_rag/manage.py collectstatic --noinput

EXPOSE 8000

CMD ["gunicorn", "--bind", "0.0.0.0:8000", "--workers", "4", "--timeout", "300", "config.wsgi:application"]
```

#### Docker Compose
```yaml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=******************************/ragsearch
      - QDRANT_HOST=qdrant
      - REDIS_URL=redis://redis:6379
    depends_on:
      - db
      - qdrant
      - redis
    volumes:
      - static_files:/app/static
      - media_files:/app/media

  db:
    image: pgvector/pgvector:pg14
    environment:
      - POSTGRES_DB=ragsearch
      - POSTGRES_USER=ragsearch
      - POSTGRES_PASSWORD=secure_password
    volumes:
      - postgres_data:/var/lib/postgresql/data

  qdrant:
    image: qdrant/qdrant:latest
    ports:
      - "6333:6333"
    volumes:
      - qdrant_data:/qdrant/storage

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  qdrant_data:
  redis_data:
  static_files:
  media_files:
```

### Traditional Deployment

#### System Setup
```bash
# Create application user
sudo useradd -m -s /bin/bash ragsearch

# Install Python and dependencies
sudo apt install python3.11 python3.11-venv python3-pip

# Setup application directory
sudo mkdir -p /opt/ragsearch
sudo chown ragsearch:ragsearch /opt/ragsearch
```

#### Application Setup
```bash
# Switch to application user
sudo -u ragsearch -i

# Clone and setup application
cd /opt/ragsearch
git clone <repository-url> .
python3.11 -m venv venv
source venv/bin/activate
pip install poetry
poetry install --no-dev

# Configure environment
cp .env.example .env
# Edit .env with production settings

# Run migrations
cd multi_source_rag
python manage.py migrate
python manage.py collectstatic --noinput
```

#### Systemd Service
```ini
# /etc/systemd/system/ragsearch.service
[Unit]
Description=RAGSearch Application
After=network.target postgresql.service

[Service]
Type=exec
User=ragsearch
Group=ragsearch
WorkingDirectory=/opt/ragsearch/multi_source_rag
Environment=PATH=/opt/ragsearch/venv/bin
EnvironmentFile=/opt/ragsearch/.env
ExecStart=/opt/ragsearch/venv/bin/gunicorn --bind 127.0.0.1:8000 --workers 4 config.wsgi:application
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

## Configuration

### Environment Variables

#### Production Settings
```bash
# Django Configuration
SECRET_KEY=your-very-secure-secret-key
DEBUG=False
ALLOWED_HOSTS=your-domain.com,www.your-domain.com
DJANGO_SETTINGS_MODULE=config.settings.production

# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/ragsearch

# Vector Database
QDRANT_HOST=localhost
QDRANT_PORT=6333

# LLM Configuration
OLLAMA_API_HOST=http://localhost:11434
OLLAMA_MODEL_NAME=llama3
EMBEDDING_MODEL_NAME=BAAI/bge-base-en-v1.5

# Optional: Gemini API
GEMINI_API_KEY=your-gemini-api-key
GEMINI_MODEL=gemini-1.5-flash

# Security
SECURE_SSL_REDIRECT=True
SECURE_HSTS_SECONDS=31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS=True
SECURE_HSTS_PRELOAD=True

# Caching
REDIS_URL=redis://localhost:6379/0

# Email Configuration
EMAIL_HOST=smtp.your-provider.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-password
```

### Django Settings

#### Production Settings
```python
# config/settings/production.py
from .base import *

DEBUG = False
ALLOWED_HOSTS = ['your-domain.com', 'www.your-domain.com']

# Security
SECURE_SSL_REDIRECT = True
SECURE_HSTS_SECONDS = 31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_BROWSER_XSS_FILTER = True
X_FRAME_OPTIONS = 'DENY'

# Database
DATABASES = {
    'default': dj_database_url.parse(os.environ.get('DATABASE_URL'))
}

# Caching
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': os.environ.get('REDIS_URL'),
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}

# Logging
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': '/var/log/ragsearch/django.log',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': True,
        },
        'apps': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}
```

## Monitoring & Logging

### Application Monitoring

#### Health Checks
```python
# apps/core/views.py
from django.http import JsonResponse
from django.views import View

class HealthCheckView(View):
    def get(self, request):
        # Check database connectivity
        # Check Qdrant connectivity
        # Check LLM availability
        return JsonResponse({
            'status': 'healthy',
            'timestamp': timezone.now().isoformat(),
            'services': {
                'database': 'ok',
                'vector_db': 'ok',
                'llm': 'ok'
            }
        })
```

#### Metrics Collection
```python
# Prometheus metrics
from prometheus_client import Counter, Histogram, Gauge

search_requests = Counter('ragsearch_requests_total', 'Total search requests')
search_duration = Histogram('ragsearch_duration_seconds', 'Search request duration')
active_users = Gauge('ragsearch_active_users', 'Number of active users')
```

### Log Management

#### Log Configuration
```python
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': '/var/log/ragsearch/django.log',
            'maxBytes': 1024*1024*100,  # 100MB
            'backupCount': 5,
            'formatter': 'verbose',
        },
    },
    'root': {
        'level': 'INFO',
        'handlers': ['file'],
    },
}
```

## Backup & Recovery

### Database Backup

#### Automated Backup Script
```bash
#!/bin/bash
# backup_db.sh

BACKUP_DIR="/backups/postgresql"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="ragsearch"

# Create backup directory
mkdir -p $BACKUP_DIR

# PostgreSQL backup
pg_dump -h localhost -U ragsearch -d $DB_NAME | gzip > $BACKUP_DIR/ragsearch_$DATE.sql.gz

# Qdrant backup
tar -czf $BACKUP_DIR/qdrant_$DATE.tar.gz /var/lib/qdrant/storage

# Cleanup old backups (keep 30 days)
find $BACKUP_DIR -name "*.gz" -mtime +30 -delete
```

#### Cron Schedule
```bash
# Add to crontab
0 2 * * * /opt/ragsearch/scripts/backup_db.sh
```

### Recovery Procedures

#### Database Recovery
```bash
# PostgreSQL recovery
gunzip -c ragsearch_20240115_020000.sql.gz | psql -h localhost -U ragsearch -d ragsearch

# Qdrant recovery
systemctl stop qdrant
rm -rf /var/lib/qdrant/storage/*
tar -xzf qdrant_20240115_020000.tar.gz -C /
systemctl start qdrant
```

## Security

### SSL/TLS Configuration

#### Let's Encrypt Setup
```bash
# Install certbot
sudo apt install certbot python3-certbot-nginx

# Obtain certificate
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# Auto-renewal
sudo crontab -e
0 12 * * * /usr/bin/certbot renew --quiet
```

### Firewall Configuration

#### UFW Setup
```bash
# Enable firewall
sudo ufw enable

# Allow SSH
sudo ufw allow ssh

# Allow HTTP/HTTPS
sudo ufw allow 80
sudo ufw allow 443

# Allow database (internal only)
sudo ufw allow from 10.0.0.0/8 to any port 5432
sudo ufw allow from 10.0.0.0/8 to any port 6333
```

### Security Headers

#### Nginx Security Headers
```nginx
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-Content-Type-Options "nosniff" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header Referrer-Policy "no-referrer-when-downgrade" always;
add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
```

For troubleshooting deployment issues, see the [Troubleshooting Guide](TROUBLESHOOTING.md).
