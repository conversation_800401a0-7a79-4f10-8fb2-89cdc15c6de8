# Universal Search Service Consolidation

## Overview

The RAG Search Service has been consolidated from multiple confusing search methods into a single, intelligent universal search method that automatically selects the optimal search strategy based on query analysis.

## What Changed

### Before: Multiple Search Methods (Confusing)
The service previously had 5 different search methods:
- `search()` - Standard semantic search
- `agentic_search()` - Agentic search with sophistication levels
- `cross_domain_search()` - Cross-domain search with routing
- `ultimate_search()` - Ultimate search with full optimization
- `slack_search()` - Slack-aware search with metadata filtering

This created confusion about which method to use and required developers to understand the differences between each approach.

### After: Single Universal Search Method (Clean)
Now there is one method:
- `search()` - Universal intelligent search with automatic strategy selection

All other search methods have been removed to eliminate confusion.

## How Universal Search Works

### Automatic Strategy Selection
The universal search method performs intelligent analysis in these steps:

1. **Query Analysis**: Analyzes query text for complexity, intent, and domain indicators
2. **Strategy Selection**: Automatically selects optimal search strategy based on analysis
3. **Parameter Configuration**: Configures search features and parameters automatically
4. **Execution**: Executes search using the selected strategy
5. **Result Formatting**: Returns results with comprehensive metadata about the strategy used

### Intelligence Features

#### Automatic Sophistication Level Detection
- **Expert Level**: Triggered by words like "analyze", "compare", "evaluate", "synthesize"
- **Advanced Level**: Triggered by words like "explain", "summarize", "how to", "curana"
- **Basic Level**: Default for simple queries

#### Automatic Intent Classification
- **Code Intent**: Detected from words like "code", "function", "class", "method", "bug"
- **Conversation Intent**: Detected from words like "conversation", "chat", "message", "curana"
- **Document Intent**: Detected from words like "document", "file", "pdf"
- **General Intent**: Default for other queries

#### Domain-Specific Optimizations
- **Slack Queries**: Automatically detected and optimized with conversation-specific parameters
- **Code Queries**: Automatically optimized with code-specific relevance thresholds
- **Cross-Domain**: Automatically enabled for complex queries requiring multiple sources

#### Smart Parameter Configuration
- **Relevance Scores**: Automatically adjusted based on intent and query type
- **Query Expansion**: Auto-enabled for queries with words like "similar", "related", "alternatives"
- **Multi-Step Reasoning**: Auto-enabled for procedural queries with "how to", "step by step"
- **Hybrid Search**: Enabled by default with intelligent fallbacks

## API Usage

### Simple Usage (Recommended)
```python
# The service automatically optimizes everything
service = RAGSearchService(tenant_slug='default', user=request.user)
results = service.search("What is the latest Curana customer feedback?")
```

### Advanced Usage with Explicit Parameters
```python
# Override automatic detection when needed
results = service.search(
    "Complex technical analysis",
    sophistication=SophisticationLevel.EXPERT,
    use_multi_step_reasoning=True,
    min_relevance_score=0.3
)
```

### Clean Interface
```python
# Single method for all search needs
results = service.search(
    "Query text",
    sophistication=SophisticationLevel.ADVANCED
)
```

## Response Format

The universal search returns comprehensive metadata about the strategy used:

```json
{
    "answer": "Generated answer text",
    "sources": [...],
    "metadata": {
        "intent": "conversation",
        "search_type": "universal",
        "strategy_used": "Slack-Aware Advanced Semantic Search",
        "sophistication_level": "advanced",
        "retrieval_mode": "semantic",
        "domain_config": {
            "is_slack_query": true,
            "is_code_query": false,
            "filters": {...}
        },
        "features_applied": {
            "use_hybrid_search": true,
            "min_relevance_score": 0.08,
            ...
        },
        "source_count": 5,
        "confidence": 0.85,
        "avg_score": 0.72
    }
}
```

## Benefits

### For Developers
- **Simplified API**: One method instead of five
- **Automatic Optimization**: No need to understand complex search strategies
- **Comprehensive Documentation**: Clear examples and parameter explanations
- **Backward Compatibility**: Existing code continues to work

### For Users
- **Better Results**: Automatic optimization provides better search results
- **Consistent Experience**: Same interface regardless of query complexity
- **Intelligent Adaptation**: System adapts to different query types automatically

### For Maintenance
- **Reduced Complexity**: Single method to maintain instead of multiple
- **Clear Logic**: Strategy selection logic is centralized and documented
- **Easier Testing**: One comprehensive method to test instead of multiple paths

## Migration Guide

### Simple Migration Required
Update existing code to use the single universal search method:

```python
# Old way (multiple methods - removed)
results = service.agentic_search(query, sophistication=SophisticationLevel.ADVANCED)
results = service.ultimate_search(query, sophistication=SophisticationLevel.EXPERT)
results = service.slack_search(query, days_back=7)

# New way (single universal method)
results = service.search(query, sophistication=SophisticationLevel.ADVANCED)
results = service.search(query, sophistication=SophisticationLevel.EXPERT)
results = service.search(query, days_back=7)  # Auto-detects Slack intent
```

### Automatic Optimization
The universal search automatically optimizes based on query analysis:
```python
# Simple usage - automatically optimized
results = service.search("What is the latest Curana customer feedback?")
# Automatically detects: Slack query, conversation intent, advanced sophistication
```

## Convenience Functions

Updated convenience functions for quick access:
- `quick_search()` - Universal search returning just the answer
- `quick_advanced_search()` - Advanced search with explicit sophistication level
- `quick_slack_search()` - Slack-optimized search
- `quick_code_search()` - Code-optimized search

## Future Enhancements

The universal search architecture enables easy addition of:
- New domain-specific optimizations
- Advanced query analysis techniques
- Machine learning-based strategy selection
- User preference learning
- Performance-based strategy adaptation

## Testing

The universal search maintains all existing functionality while adding intelligent automation. All existing tests should pass, and new tests can focus on the strategy selection logic and automatic optimizations.

## Changelog

### Version 2.0 - Universal Search Consolidation

**BREAKING CHANGES:**
- Removed `agentic_search()`, `cross_domain_search()`, `ultimate_search()`, and `slack_search()` methods
- All search functionality consolidated into single `search()` method

**NEW FEATURES:**
- Universal search with automatic strategy selection
- Intelligent query analysis and optimization
- Automatic sophistication level detection
- Domain-specific optimization (Slack, code, etc.)
- Comprehensive search metadata in responses

**MIGRATION:**
- Replace all `service.agentic_search()` calls with `service.search()`
- Replace all `service.ultimate_search()` calls with `service.search()`
- Replace all `service.slack_search()` calls with `service.search()`
- Replace all `service.cross_domain_search()` calls with `service.search()`
- All parameters remain the same, automatic optimization is applied

**BENEFITS:**
- Simplified API with single method
- Better search results through automatic optimization
- Reduced confusion about which method to use
- Easier maintenance and testing
