# RAGSearch Developer Guide

## Table of Contents

1. [Development Setup](#development-setup)
2. [Project Structure](#project-structure)
3. [API Documentation](#api-documentation)
4. [Testing Guidelines](#testing-guidelines)
5. [Code Standards](#code-standards)
6. [Contributing](#contributing)

## Development Setup

### Prerequisites

- Python 3.10+
- Poetry for dependency management
- PostgreSQL 14+ with pgvector
- Qdrant vector database
- Git

### Local Development Environment

1. **Clone and Setup**:
   ```bash
   git clone <repository-url>
   cd RAGSearch
   poetry install
   poetry shell
   ```

2. **Database Setup**:
   ```bash
   # PostgreSQL with pgvector
   createdb multi_source_rag
   psql multi_source_rag -c "CREATE EXTENSION vector;"
   
   # Run migrations
   cd multi_source_rag
   python manage.py migrate
   python manage.py createsuperuser
   ```

3. **Vector Database**:
   ```bash
   # Using Docker (recommended)
   docker run -p 6333:6333 qdrant/qdrant
   ```

4. **Environment Configuration**:
   ```bash
   # Copy and configure .env
   cp .env.example .env
   # Edit .env with your settings
   ```

### Development Tools

#### Code Quality
```bash
# Formatting
poetry run black .
poetry run isort .

# Linting
poetry run flake8
poetry run mypy .

# Testing
poetry run pytest
```

#### Database Management
```bash
# Clean databases (development only)
python manage.py clean_databases --confirm

# Create test data
python manage.py create_test_data
```

## Project Structure

### Django Applications

```
multi_source_rag/
├── apps/
│   ├── accounts/          # User and tenant management
│   ├── api/              # REST API endpoints
│   ├── core/             # Shared utilities and services
│   ├── documents/        # Document processing and storage
│   └── search/           # Search and RAG functionality
├── config/               # Django settings
├── static/               # Static files (CSS, JS)
└── templates/            # HTML templates
```

### Key Components

#### Core Services
- **`apps/core/services/`**: RAG services and LLM integration
- **`apps/core/utils/`**: Utilities for embeddings, vector stores, etc.
- **`apps/documents/interfaces/`**: Data source interfaces (Slack, GitHub)

#### Search System
- **`apps/search/services/`**: Search services and query engines
- **`apps/search/views.py`**: Search interface and API views
- **`apps/search/templates/`**: Search UI templates

#### API Layer
- **`apps/api/views.py`**: REST API endpoints
- **`apps/api/serializers.py`**: API serializers
- **`apps/api/urls.py`**: API URL routing

### Configuration

#### Settings Structure
```
config/
├── settings/
│   ├── base.py           # Base settings
│   ├── development.py    # Development overrides
│   ├── production.py     # Production settings
│   └── testing.py        # Test settings
└── urls.py               # Root URL configuration
```

#### Environment Variables
```bash
# Core Django
SECRET_KEY=your-secret-key
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Database
DB_NAME=multi_source_rag
DB_USER=postgres
DB_PASSWORD=password
DB_HOST=localhost
DB_PORT=5432

# Vector Database
QDRANT_HOST=localhost
QDRANT_PORT=6333

# LLM Configuration
OLLAMA_API_HOST=http://localhost:11434
OLLAMA_MODEL_NAME=llama3
EMBEDDING_MODEL_NAME=BAAI/bge-base-en-v1.5

# Optional: Gemini API
GEMINI_API_KEY=your-api-key
GEMINI_MODEL=gemini-1.5-flash
```

## API Documentation

### Authentication

All API endpoints require authentication:

```python
# Token-based authentication
headers = {
    'Authorization': 'Token your-api-token',
    'Content-Type': 'application/json'
}
```

### Core Endpoints

#### Search API
```http
POST /api/search/
Content-Type: application/json

{
    "query": "How do I configure authentication?",
    "top_k": 10,
    "use_hybrid_search": true,
    "use_query_expansion": false,
    "metadata_filter": {
        "source": "slack",
        "channel": "engineering"
    }
}
```

**Response**:
```json
{
    "response": "To configure authentication...",
    "sources": [
        {
            "id": "doc_123",
            "title": "Authentication Setup Guide",
            "content": "...",
            "metadata": {
                "source": "slack",
                "channel": "engineering",
                "timestamp": "2024-01-15T10:30:00Z"
            },
            "score": 0.95
        }
    ],
    "conversation_id": 456,
    "query_time": 1.23
}
```

#### Document Ingestion API
```http
POST /api/documents/ingest/
Content-Type: application/json

{
    "source_type": "slack",
    "config": {
        "token": "xoxb-your-token",
        "channels": ["C1234567890"],
        "days": 30
    }
}
```

#### Conversation API
```http
GET /api/conversations/
GET /api/conversations/{id}/
POST /api/conversations/{id}/messages/
```

### Error Handling

```json
{
    "error": "ValidationError",
    "message": "Invalid query parameters",
    "details": {
        "query": ["This field is required."]
    }
}
```

### Rate Limiting

- **Search API**: 100 requests per minute per user
- **Ingestion API**: 10 requests per minute per user
- **General API**: 1000 requests per hour per user

## Testing Guidelines

### Test Structure

```
tests/
├── unit/                 # Unit tests
├── integration/          # Integration tests
├── api/                  # API tests
└── fixtures/             # Test data
```

### Running Tests

```bash
# All tests
poetry run pytest

# Specific test categories
poetry run pytest tests/unit/
poetry run pytest tests/api/
poetry run pytest tests/integration/

# With coverage
poetry run pytest --cov=apps/
```

### Test Categories

#### Unit Tests
- **Service Logic**: Test RAG services and utilities
- **Model Methods**: Test Django model methods
- **Utility Functions**: Test helper functions

#### Integration Tests
- **End-to-End Search**: Test complete search workflows
- **Data Ingestion**: Test document processing pipelines
- **API Integration**: Test API endpoints with real data

#### Performance Tests
- **Search Performance**: Measure query response times
- **Ingestion Performance**: Test data processing speed
- **Concurrent Usage**: Test system under load

### Test Data

#### Fixtures
```python
# tests/fixtures/slack_data.json
{
    "messages": [
        {
            "text": "How do we handle authentication?",
            "user": "U1234567890",
            "ts": "1642678800.000100",
            "channel": "C1234567890"
        }
    ]
}
```

#### Test Utilities
```python
# tests/utils.py
def create_test_tenant():
    """Create a test tenant with sample data."""
    
def create_test_documents():
    """Create test documents for search testing."""
    
def mock_llm_response():
    """Mock LLM responses for testing."""
```

### Testing Best Practices

1. **Isolated Tests**: Each test should be independent
2. **Mock External Services**: Mock LLM and API calls
3. **Test Data Cleanup**: Clean up test data after tests
4. **Performance Assertions**: Include performance checks
5. **Error Scenarios**: Test error handling and edge cases

## Code Standards

### Python Style

- **PEP 8**: Follow Python style guidelines
- **Black**: Use Black for code formatting
- **isort**: Sort imports consistently
- **Type Hints**: Use type hints for function signatures

```python
from typing import List, Optional, Dict, Any

def search_documents(
    query: str,
    top_k: int = 10,
    metadata_filter: Optional[Dict[str, Any]] = None
) -> List[Dict[str, Any]]:
    """Search documents with the given query."""
    pass
```

### Django Conventions

- **Model Design**: Follow Django model best practices
- **View Structure**: Use class-based views where appropriate
- **URL Patterns**: Use descriptive URL names
- **Template Organization**: Organize templates by app

### Documentation

- **Docstrings**: Use Google-style docstrings
- **Comments**: Explain complex logic and business rules
- **README Updates**: Keep documentation current
- **API Documentation**: Document all API endpoints

```python
def process_document(document: Dict[str, Any]) -> ProcessedDocument:
    """
    Process a raw document for ingestion.
    
    Args:
        document: Raw document data from source
        
    Returns:
        ProcessedDocument: Processed document ready for embedding
        
    Raises:
        ValidationError: If document format is invalid
    """
    pass
```

### Error Handling

- **Specific Exceptions**: Use specific exception types
- **Logging**: Log errors with context
- **User-Friendly Messages**: Provide clear error messages
- **Graceful Degradation**: Handle failures gracefully

```python
import logging

logger = logging.getLogger(__name__)

try:
    result = process_document(doc)
except ValidationError as e:
    logger.error(f"Document validation failed: {e}")
    raise
except Exception as e:
    logger.error(f"Unexpected error processing document: {e}")
    raise ProcessingError("Failed to process document")
```

## Contributing

### Development Workflow

1. **Create Feature Branch**: `git checkout -b feature/new-feature`
2. **Make Changes**: Implement your feature
3. **Write Tests**: Add comprehensive tests
4. **Run Tests**: Ensure all tests pass
5. **Code Review**: Submit pull request for review
6. **Merge**: Merge after approval

### Pull Request Guidelines

- **Clear Description**: Explain what the PR does
- **Test Coverage**: Include tests for new functionality
- **Documentation**: Update docs if needed
- **Small Changes**: Keep PRs focused and small
- **Code Quality**: Ensure code meets standards

### Commit Messages

```
feat: add GitHub integration for pull requests
fix: resolve embedding dimension mismatch
docs: update API documentation
test: add integration tests for search
refactor: simplify RAG service architecture
```

### Code Review Checklist

- [ ] Code follows style guidelines
- [ ] Tests are included and passing
- [ ] Documentation is updated
- [ ] No security vulnerabilities
- [ ] Performance impact considered
- [ ] Error handling is appropriate

For deployment and production considerations, see the [Deployment Guide](DEPLOYMENT.md).
