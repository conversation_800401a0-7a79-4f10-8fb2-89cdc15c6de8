#!/usr/bin/env python3
"""
Debug script to identify the search issue causing low quality scores.
"""

import os
import sys
import django

# Add project root to Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.join(project_root, 'multi_source_rag'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from apps.search.services.rag_search_service import RAGSearchService
from apps.core.llama_index_manager import LlamaIndexManager


def debug_search_step_by_step():
    """Debug search functionality step by step."""
    
    print("🔍 DEBUGGING SEARCH ISSUE")
    print("=" * 50)
    
    query = "What is authentication?"
    tenant_slug = "stride"
    
    print(f"Query: {query}")
    print(f"Tenant: {tenant_slug}")
    print()
    
    # Step 1: Test RAGSearchService
    print("📋 Step 1: Testing RAGSearchService")
    try:
        service = RAGSearchService(tenant_slug=tenant_slug)
        result = service.search(query=query, limit=5, intent='slack')
        
        print(f"✅ Service created successfully")
        print(f"Response type: {type(result)}")
        print(f"Response keys: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")
        
        if isinstance(result, dict):
            response_text = result.get('response', 'No response key')
            source_nodes = result.get('source_nodes', [])
            
            print(f"Response text: '{response_text}'")
            print(f"Response length: {len(str(response_text))}")
            print(f"Source nodes count: {len(source_nodes)}")
            
            if source_nodes:
                print(f"First source keys: {list(source_nodes[0].keys()) if isinstance(source_nodes[0], dict) else 'Not a dict'}")
        
    except Exception as e:
        print(f"❌ RAGSearchService failed: {e}")
        import traceback
        traceback.print_exc()
    
    print()
    
    # Step 2: Test LlamaIndexManager directly
    print("📋 Step 2: Testing LlamaIndexManager directly")
    try:
        manager = LlamaIndexManager(tenant_slug)
        result = manager.search(query=query, intent='slack')
        
        print(f"✅ Manager created successfully")
        print(f"Response type: {type(result)}")
        print(f"Response keys: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")
        
        if isinstance(result, dict):
            answer = result.get('answer', 'No answer key')
            response = result.get('response', 'No response key')
            source_nodes = result.get('source_nodes', [])
            
            print(f"Answer: '{answer}'")
            print(f"Response: '{response}'")
            print(f"Answer length: {len(str(answer))}")
            print(f"Response length: {len(str(response))}")
            print(f"Source nodes count: {len(source_nodes)}")
            
    except Exception as e:
        print(f"❌ LlamaIndexManager failed: {e}")
        import traceback
        traceback.print_exc()
    
    print()
    
    # Step 3: Test vector search directly
    print("📋 Step 3: Testing vector search directly")
    try:
        import requests
        from apps.core.utils.embedding_config import get_embedding_model
        
        # Get embedding for query
        embedding_model = get_embedding_model()
        query_embedding = embedding_model.get_text_embedding(query)
        
        print(f"✅ Query embedding generated: {len(query_embedding)} dimensions")
        
        # Search vector database
        search_payload = {
            'vector': query_embedding,
            'limit': 5,
            'with_payload': True
        }
        
        response = requests.post('http://localhost:6333/collections/tenant_stride_default/points/search', json=search_payload)
        
        if response.status_code == 200:
            result = response.json()
            results = result.get('result', [])
            print(f"✅ Vector search successful: {len(results)} results")
            
            if results:
                best_result = results[0]
                print(f"Best match score: {best_result.get('score', 'No score')}")
                print(f"Best match payload keys: {list(best_result.get('payload', {}).keys())}")
                
                # Check if we have the actual text content
                payload = best_result.get('payload', {})
                node_content = payload.get('_node_content', 'No content')
                print(f"Node content preview: '{str(node_content)[:100]}...'")
        else:
            print(f"❌ Vector search failed: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"❌ Vector search test failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    debug_search_step_by_step()
