#!/usr/bin/env python3
"""
Optimized RAG System Testing Framework

This script tests all critical RAG features with optimized performance
and realistic expectations for production readiness.
"""

import os
import sys
import json
import time
import logging
from datetime import datetime
from typing import Dict, List, Any

# Setup Django
sys.path.insert(0, 'multi_source_rag')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
import django
django.setup()

from apps.accounts.models import Tenant
from apps.search.services.rag_search_service import RAGSearchService

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class OptimizedRAGTester:
    """Optimized RAG testing framework for production readiness."""
    
    def __init__(self, tenant_slug: str):
        self.tenant_slug = tenant_slug
        self.tenant = Tenant.objects.get(slug=tenant_slug)
        self.search_service = RAGSearchService(tenant_slug)
        self.test_results = []
        
    def run_optimized_tests(self):
        """Run optimized tests focusing on critical functionality."""
        
        print("🚀 OPTIMIZED RAG SYSTEM TESTING")
        print("=" * 60)
        print(f"🏢 Tenant: {self.tenant.name}")
        print(f"⏰ Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # Critical test categories
        test_categories = [
            ("🔍 CORE FUNCTIONALITY", self._test_core_functionality),
            ("📊 RESULT QUALITY", self._test_result_quality),
            ("⚡ PERFORMANCE", self._test_performance),
            ("🎯 ADVANCED FEATURES", self._test_advanced_features),
        ]
        
        total_tests = 0
        passed_tests = 0
        
        for category_name, test_function in test_categories:
            print(f"\n{category_name}")
            print("-" * 50)
            
            try:
                category_results = test_function()
                category_passed = sum(1 for r in category_results if r['passed'])
                category_total = len(category_results)
                
                total_tests += category_total
                passed_tests += category_passed
                
                print(f"📊 Category Results: {category_passed}/{category_total} passed")
                self.test_results.extend(category_results)
                
            except Exception as e:
                print(f"❌ Category failed: {e}")
                logger.error(f"Category {category_name} failed", exc_info=True)
        
        # Final Summary
        self._print_final_summary(passed_tests, total_tests)
        return passed_tests / total_tests if total_tests > 0 else 0
    
    def _test_core_functionality(self) -> List[Dict]:
        """Test core RAG functionality."""
        tests = [
            {
                "name": "Basic Search",
                "query": "authentication",
                "min_results": 1,
                "check_content": True
            },
            {
                "name": "Technical Query",
                "query": "API endpoint configuration",
                "min_results": 1,
                "check_content": True
            },
            {
                "name": "Complex Query",
                "query": "How do we handle user authentication in our system?",
                "min_results": 1,
                "check_content": True
            },
            {
                "name": "Multiple Results",
                "query": "engineering discussions",
                "min_results": 2,
                "check_content": True
            }
        ]
        
        return self._run_test_batch(tests, "Core Functionality")
    
    def _test_result_quality(self) -> List[Dict]:
        """Test result quality and citations."""
        tests = [
            {
                "name": "Content Quality",
                "query": "authentication implementation details",
                "min_results": 1,
                "check_citations": True,
                "check_completeness": True
            },
            {
                "name": "Metadata Quality",
                "query": "API security best practices",
                "min_results": 1,
                "check_metadata": True
            },
            {
                "name": "Relevance Quality",
                "query": "login issues",
                "min_results": 1,
                "check_relevance": True
            }
        ]
        
        return self._run_test_batch(tests, "Result Quality")
    
    def _test_performance(self) -> List[Dict]:
        """Test performance with realistic expectations."""
        tests = [
            {
                "name": "Standard Performance",
                "query": "authentication system overview",
                "max_response_time": 30.0,
                "min_results": 1
            },
            {
                "name": "Complex Query Performance",
                "query": "What are the architectural patterns for implementing scalable authentication?",
                "max_response_time": 60.0,
                "min_results": 1
            }
        ]
        
        return self._run_test_batch(tests, "Performance")
    
    def _test_advanced_features(self) -> List[Dict]:
        """Test advanced RAG features."""
        tests = [
            {
                "name": "Intent Classification",
                "query": "How to debug connection issues?",
                "min_results": 1,
                "expected_intent": "code"
            },
            {
                "name": "Query Enhancement",
                "query": "auth",
                "min_results": 1,
                "check_enhancement": True
            },
            {
                "name": "Hybrid Search",
                "query": "authentication best practices",
                "min_results": 2,
                "check_hybrid": True
            }
        ]
        
        return self._run_test_batch(tests, "Advanced Features")
    
    def _run_test_batch(self, tests: List[Dict], category: str) -> List[Dict]:
        """Run a batch of tests and return results."""
        results = []
        
        for i, test in enumerate(tests, 1):
            print(f"  {i}. {test['name']}")
            
            try:
                start_time = time.time()
                
                # Execute search with optimized parameters
                search_result = self.search_service.search(
                    query=test['query'],
                    user_id=None,
                    limit=test.get('min_results', 5) * 2,  # Get more results for better testing
                    include_metadata=True
                )
                
                end_time = time.time()
                response_time = end_time - start_time
                
                # Validate results
                passed, details = self._validate_test_result(test, search_result, response_time)
                
                result = {
                    'category': category,
                    'name': test['name'],
                    'query': test['query'],
                    'passed': passed,
                    'response_time': response_time,
                    'result_count': len(search_result.get('results', [])),
                    'details': details,
                    'timestamp': datetime.now().isoformat()
                }
                
                results.append(result)
                
                # Print result
                status = "✅" if passed else "❌"
                print(f"     {status} {details} ({response_time:.2f}s)")
                
            except Exception as e:
                print(f"     ❌ Error: {str(e)}")
                results.append({
                    'category': category,
                    'name': test['name'],
                    'query': test['query'],
                    'passed': False,
                    'error': str(e),
                    'timestamp': datetime.now().isoformat()
                })
        
        return results
    
    def _validate_test_result(self, test: Dict, result: Dict, response_time: float) -> tuple:
        """Validate a test result against expectations."""
        
        # Check if search was successful
        if result.get('status') != 'success':
            return False, f"Search failed: {result.get('message', 'Unknown error')}"
        
        results = result.get('results', [])
        
        # Check minimum results
        min_results = test.get('min_results', 1)
        if len(results) < min_results:
            return False, f"Expected {min_results} results, got {len(results)}"
        
        # Check response time
        max_time = test.get('max_response_time')
        if max_time and response_time > max_time:
            return False, f"Response time {response_time:.2f}s exceeded {max_time}s"
        
        # Check content quality
        if test.get('check_content'):
            if not any(len(r.get('text', '')) > 100 for r in results):
                return False, "Results have insufficient content"
        
        # Check citations
        if test.get('check_citations'):
            has_citations = any(
                'metadata' in r and r['metadata'] and (
                    'source_type' in r['metadata'] or 
                    'document_id' in r['metadata'] or
                    'channel_name' in r['metadata']
                ) for r in results
            )
            if not has_citations:
                return False, "No citations found in results"
        
        # Check completeness
        if test.get('check_completeness'):
            avg_length = sum(len(r.get('text', '')) for r in results) / len(results)
            if avg_length < 100:
                return False, f"Results too short (avg: {avg_length:.0f} chars)"
        
        # Check metadata
        if test.get('check_metadata'):
            has_metadata = any('metadata' in r and r['metadata'] for r in results)
            if not has_metadata:
                return False, "No metadata found in results"
        
        # Check relevance
        if test.get('check_relevance'):
            has_relevance = any(r.get('relevance', 0) > 0 for r in results)
            if not has_relevance:
                return False, "No relevance scores found"
        
        return True, f"{len(results)} results found"
    
    def _print_final_summary(self, passed: int, total: int):
        """Print final test summary."""
        
        print(f"\n🎯 OPTIMIZED RAG TESTING SUMMARY")
        print("=" * 60)
        
        success_rate = (passed / total * 100) if total > 0 else 0
        
        print(f"📊 Tests Passed: {passed}/{total}")
        print(f"📊 Success Rate: {success_rate:.1f}%")
        
        # Category breakdown
        categories = {}
        for result in self.test_results:
            cat = result['category']
            if cat not in categories:
                categories[cat] = {'passed': 0, 'total': 0}
            categories[cat]['total'] += 1
            if result['passed']:
                categories[cat]['passed'] += 1
        
        print(f"\n📋 Category Breakdown:")
        for cat, stats in categories.items():
            rate = (stats['passed'] / stats['total'] * 100) if stats['total'] > 0 else 0
            print(f"  • {cat}: {stats['passed']}/{stats['total']} ({rate:.1f}%)")
        
        # Performance stats
        response_times = [r.get('response_time', 0) for r in self.test_results if 'response_time' in r]
        if response_times:
            avg_time = sum(response_times) / len(response_times)
            max_time = max(response_times)
            print(f"\n⚡ Performance:")
            print(f"  • Average response time: {avg_time:.2f}s")
            print(f"  • Maximum response time: {max_time:.2f}s")
        
        # Final verdict
        print(f"\n🏆 FINAL VERDICT:")
        if success_rate >= 95:
            print("🎉 EXCELLENT - RAG system is production-ready with outstanding performance!")
        elif success_rate >= 90:
            print("✅ VERY GOOD - RAG system is production-ready with strong performance!")
        elif success_rate >= 85:
            print("👍 GOOD - RAG system is production-ready with minor optimizations needed!")
        elif success_rate >= 80:
            print("⚠️  FAIR - RAG system needs improvements before production!")
        else:
            print("❌ POOR - RAG system requires significant fixes!")
        
        # Save detailed results
        self._save_test_results()
    
    def _save_test_results(self):
        """Save detailed test results to file."""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"optimized_rag_test_results_{timestamp}.json"
        
        with open(filename, 'w') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'tenant': self.tenant_slug,
                'summary': {
                    'total_tests': len(self.test_results),
                    'passed_tests': sum(1 for r in self.test_results if r['passed']),
                    'success_rate': sum(1 for r in self.test_results if r['passed']) / len(self.test_results) * 100
                },
                'detailed_results': self.test_results
            }, f, indent=2)
        
        print(f"📄 Detailed results saved to: {filename}")

def main():
    """Main testing function."""
    try:
        tester = OptimizedRAGTester('stride')
        success_rate = tester.run_optimized_tests()
        
        # Exit with appropriate code
        sys.exit(0 if success_rate >= 0.85 else 1)
        
    except Exception as e:
        print(f"❌ Testing failed: {e}")
        logger.error("Testing failed", exc_info=True)
        sys.exit(1)

if __name__ == "__main__":
    main()
