#!/usr/bin/env python3
"""
Real User UAT Testing Script for RAG System

This script performs comprehensive end-to-end testing as a real user would,
testing all RAG features from basic to advanced with real Slack data.
"""

import os
import sys
import django
import json
import time
import logging
import requests
from datetime import datetime
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "multi_source_rag"))

# Set up Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.development")
django.setup()

from django.contrib.auth.models import User
from django.test import Client
from django.urls import reverse
from apps.accounts.models import Tenant
from apps.documents.models import DocumentSource, RawDocument, DocumentChunk
from apps.search.models import SearchQuery, SearchResult, Conversation, Message
from apps.search.services.rag_search_service import RAGSearchService

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('real_user_uat_test.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class RealUserUATTester:
    """Real user UAT testing for the RAG system."""
    
    def __init__(self):
        self.client = Client()
        self.test_results = {
            'timestamp': datetime.now().isoformat(),
            'tests': {},
            'summary': {}
        }
        self.user = None
        self.tenant = None
        self.search_service = None
        
    def setup_test_environment(self):
        """Set up the test environment."""
        logger.info("🔧 Setting up test environment...")
        
        # Get test user
        try:
            self.user = User.objects.get(username='mahesh')
            logger.info(f"✅ Using user: {self.user.username}")
        except User.DoesNotExist:
            logger.error("❌ Test user 'mahesh' not found")
            return False
            
        # Get tenant
        try:
            self.tenant = Tenant.objects.get(slug='stride')
            logger.info(f"✅ Using tenant: {self.tenant.name}")
        except Tenant.DoesNotExist:
            logger.error("❌ Tenant 'stride' not found")
            return False
            
        # Login the user
        login_success = self.client.force_login(self.user)
        if login_success is not False:
            logger.info("✅ User logged in successfully")
        else:
            logger.error("❌ Failed to login user")
            return False
            
        # Initialize search service
        try:
            self.search_service = RAGSearchService(
                tenant_slug=self.tenant.slug,
                user=self.user
            )
            logger.info("✅ RAG Search Service initialized")
        except Exception as e:
            logger.error(f"❌ Failed to initialize search service: {e}")
            return False
            
        return True
        
    def test_database_integrity(self):
        """Test database integrity and data availability."""
        logger.info("🔍 Testing database integrity...")
        
        test_name = "database_integrity"
        results = {}
        
        try:
            # Check data counts
            tenant_count = Tenant.objects.count()
            source_count = DocumentSource.objects.count()
            doc_count = RawDocument.objects.count()
            chunk_count = DocumentChunk.objects.count()
            
            results['tenant_count'] = tenant_count
            results['source_count'] = source_count
            results['document_count'] = doc_count
            results['chunk_count'] = chunk_count
            
            # Check Slack data specifically
            slack_sources = DocumentSource.objects.filter(source_type__in=['slack', 'local_slack'])
            slack_docs = RawDocument.objects.filter(source__in=slack_sources)
            slack_chunks = DocumentChunk.objects.filter(document__in=slack_docs)
            
            results['slack_sources'] = slack_sources.count()
            results['slack_documents'] = slack_docs.count()
            results['slack_chunks'] = slack_chunks.count()
            
            # Validate minimum data requirements
            if slack_docs.count() < 100:
                results['status'] = 'warning'
                results['warning'] = f"Low Slack document count: {slack_docs.count()}"
            else:
                results['status'] = 'passed'
            
            logger.info(f"✅ Database integrity check completed")
            logger.info(f"   - Tenants: {tenant_count}")
            logger.info(f"   - Sources: {source_count}")
            logger.info(f"   - Documents: {doc_count}")
            logger.info(f"   - Chunks: {chunk_count}")
            logger.info(f"   - Slack Documents: {slack_docs.count()}")
                
        except Exception as e:
            logger.error(f"❌ Database integrity test failed: {e}")
            results['status'] = 'failed'
            results['error'] = str(e)
            
        self.test_results['tests'][test_name] = results
        return results.get('status') in ['passed', 'warning']
        
    def test_basic_search(self):
        """Test basic search functionality."""
        logger.info("🔍 Testing basic search functionality...")
        
        test_name = "basic_search"
        results = {}
        
        # Test queries covering different complexity levels
        test_queries = [
            "What are the main engineering challenges?",
            "Tell me about recent discussions",
            "How is the team handling technical debt?",
            "What feedback have we received from customers?",
            "List the key issues mentioned in engineering meetings"
        ]
        
        try:
            query_results = []
            
            for query in test_queries:
                logger.info(f"Testing query: '{query}'")
                start_time = time.time()
                
                # Test through search service
                search_result = self.search_service.search(
                    query=query,
                    user_id=self.user.id,
                    limit=10
                )
                
                duration = time.time() - start_time
                
                # Validate response
                has_answer = bool(search_result.get('answer', '').strip())
                has_sources = len(search_result.get('sources', [])) > 0
                
                query_result = {
                    'query': query,
                    'duration': duration,
                    'has_answer': has_answer,
                    'has_sources': has_sources,
                    'source_count': len(search_result.get('sources', [])),
                    'answer_length': len(search_result.get('answer', '')),
                    'status': 'passed' if has_answer and has_sources else 'failed'
                }
                
                query_results.append(query_result)
                
                logger.info(f"   ✅ Query completed in {duration:.2f}s")
                logger.info(f"   - Answer: {'Yes' if has_answer else 'No'}")
                logger.info(f"   - Sources: {len(search_result.get('sources', []))}")
                
            # Calculate overall results
            passed_queries = sum(1 for r in query_results if r['status'] == 'passed')
            avg_duration = sum(r['duration'] for r in query_results) / len(query_results)
            
            results['query_results'] = query_results
            results['passed_queries'] = passed_queries
            results['total_queries'] = len(test_queries)
            results['success_rate'] = passed_queries / len(test_queries)
            results['avg_duration'] = avg_duration
            results['status'] = 'passed' if passed_queries >= len(test_queries) * 0.8 else 'failed'
            
            logger.info(f"✅ Basic search test completed")
            logger.info(f"   - Success rate: {results['success_rate']:.1%}")
            logger.info(f"   - Average duration: {avg_duration:.2f}s")
                
        except Exception as e:
            logger.error(f"❌ Basic search test failed: {e}")
            results['status'] = 'failed'
            results['error'] = str(e)
            
        self.test_results['tests'][test_name] = results
        return results.get('status') == 'passed'
        
    def test_advanced_search_features(self):
        """Test advanced search features."""
        logger.info("🔍 Testing advanced search features...")
        
        test_name = "advanced_search"
        results = {}
        
        try:
            # Test agentic search
            logger.info("Testing agentic search...")
            agentic_result = self.search_service.agentic_search(
                query="What are the most critical engineering issues we need to address?",
                user_id=self.user.id
            )
            
            # Test cross-domain search
            logger.info("Testing cross-domain search...")
            cross_domain_result = self.search_service.cross_domain_search(
                query="How are we handling customer feedback and technical improvements?",
                user_id=self.user.id
            )
            
            # Test ultimate search
            logger.info("Testing ultimate search...")
            ultimate_result = self.search_service.ultimate_search(
                query="Provide a comprehensive analysis of our engineering team's current challenges and solutions",
                user_id=self.user.id
            )
            
            # Validate results
            agentic_valid = bool(agentic_result.get('answer', '').strip())
            cross_domain_valid = bool(cross_domain_result.get('answer', '').strip())
            ultimate_valid = bool(ultimate_result.get('answer', '').strip())
            
            results['agentic_search'] = {
                'valid': agentic_valid,
                'answer_length': len(agentic_result.get('answer', '')),
                'source_count': len(agentic_result.get('sources', []))
            }
            
            results['cross_domain_search'] = {
                'valid': cross_domain_valid,
                'answer_length': len(cross_domain_result.get('answer', '')),
                'source_count': len(cross_domain_result.get('sources', []))
            }
            
            results['ultimate_search'] = {
                'valid': ultimate_valid,
                'answer_length': len(ultimate_result.get('answer', '')),
                'source_count': len(ultimate_result.get('sources', []))
            }
            
            # Overall status
            all_valid = agentic_valid and cross_domain_valid and ultimate_valid
            results['status'] = 'passed' if all_valid else 'failed'
            
            logger.info(f"✅ Advanced search test completed")
            logger.info(f"   - Agentic search: {'✅' if agentic_valid else '❌'}")
            logger.info(f"   - Cross-domain search: {'✅' if cross_domain_valid else '❌'}")
            logger.info(f"   - Ultimate search: {'✅' if ultimate_valid else '❌'}")
                
        except Exception as e:
            logger.error(f"❌ Advanced search test failed: {e}")
            results['status'] = 'failed'
            results['error'] = str(e)
            
        self.test_results['tests'][test_name] = results
        return results.get('status') == 'passed'

    def test_web_interface(self):
        """Test web interface functionality."""
        logger.info("🌐 Testing web interface...")

        test_name = "web_interface"
        results = {}

        try:
            # Test search page access
            response = self.client.get(reverse('search:search'))
            search_page_ok = response.status_code == 200

            # Test search query submission
            search_response = self.client.post(reverse('search:query'), {
                'query': 'What are the main engineering challenges discussed?',
                'use_hybrid_search': 'true',
                'use_context_aware': 'true'
            })
            search_query_ok = search_response.status_code == 200

            # Test conversations page
            conv_response = self.client.get(reverse('search:conversations'))
            conversations_ok = conv_response.status_code == 200

            results['search_page'] = search_page_ok
            results['search_query'] = search_query_ok
            results['conversations_page'] = conversations_ok
            results['status'] = 'passed' if all([search_page_ok, search_query_ok, conversations_ok]) else 'failed'

            logger.info(f"✅ Web interface test completed")
            logger.info(f"   - Search page: {'✅' if search_page_ok else '❌'}")
            logger.info(f"   - Search query: {'✅' if search_query_ok else '❌'}")
            logger.info(f"   - Conversations: {'✅' if conversations_ok else '❌'}")

        except Exception as e:
            logger.error(f"❌ Web interface test failed: {e}")
            results['status'] = 'failed'
            results['error'] = str(e)

        self.test_results['tests'][test_name] = results
        return results.get('status') == 'passed'

    def test_search_quality(self):
        """Test search quality and relevance."""
        logger.info("🎯 Testing search quality and relevance...")

        test_name = "search_quality"
        results = {}

        # Quality test queries with expected characteristics
        quality_tests = [
            {
                'query': 'What customer feedback have we received?',
                'expected_keywords': ['customer', 'feedback', 'user'],
                'min_sources': 2
            },
            {
                'query': 'How is the engineering team handling technical debt?',
                'expected_keywords': ['technical', 'debt', 'engineering'],
                'min_sources': 1
            },
            {
                'query': 'What are the latest discussions about product improvements?',
                'expected_keywords': ['product', 'improvement', 'discussion'],
                'min_sources': 1
            }
        ]

        try:
            quality_results = []

            for test in quality_tests:
                logger.info(f"Testing quality for: '{test['query']}'")

                search_result = self.search_service.search(
                    query=test['query'],
                    user_id=self.user.id,
                    limit=10
                )

                answer = search_result.get('answer', '').lower()
                sources = search_result.get('sources', [])

                # Check for expected keywords
                keyword_matches = sum(1 for keyword in test['expected_keywords']
                                    if keyword.lower() in answer)
                keyword_score = keyword_matches / len(test['expected_keywords'])

                # Check source count
                source_count_ok = len(sources) >= test['min_sources']

                # Check answer quality (length and coherence)
                answer_quality_ok = len(answer) > 100 and len(answer.split()) > 20

                quality_result = {
                    'query': test['query'],
                    'keyword_score': keyword_score,
                    'source_count': len(sources),
                    'source_count_ok': source_count_ok,
                    'answer_length': len(answer),
                    'answer_quality_ok': answer_quality_ok,
                    'overall_quality': keyword_score >= 0.5 and source_count_ok and answer_quality_ok
                }

                quality_results.append(quality_result)

                logger.info(f"   - Keyword relevance: {keyword_score:.1%}")
                logger.info(f"   - Sources: {len(sources)} (min: {test['min_sources']})")
                logger.info(f"   - Answer quality: {'✅' if answer_quality_ok else '❌'}")

            # Calculate overall quality score
            quality_scores = [r['overall_quality'] for r in quality_results]
            overall_quality = sum(quality_scores) / len(quality_scores)

            results['quality_tests'] = quality_results
            results['overall_quality_score'] = overall_quality
            results['status'] = 'passed' if overall_quality >= 0.7 else 'failed'

            logger.info(f"✅ Search quality test completed")
            logger.info(f"   - Overall quality score: {overall_quality:.1%}")

        except Exception as e:
            logger.error(f"❌ Search quality test failed: {e}")
            results['status'] = 'failed'
            results['error'] = str(e)

        self.test_results['tests'][test_name] = results
        return results.get('status') == 'passed'

    def run_comprehensive_uat(self):
        """Run comprehensive UAT testing."""
        logger.info("🚀 Starting Comprehensive UAT Testing...")
        start_time = time.time()

        # Setup test environment
        if not self.setup_test_environment():
            logger.error("❌ Failed to setup test environment")
            return False

        # Run all tests
        tests = [
            ('Database Integrity', self.test_database_integrity),
            ('Basic Search', self.test_basic_search),
            ('Advanced Search Features', self.test_advanced_search_features),
            ('Web Interface', self.test_web_interface),
            ('Search Quality', self.test_search_quality)
        ]

        passed_tests = 0
        total_tests = len(tests)

        for test_name, test_func in tests:
            logger.info(f"\n{'='*60}")
            logger.info(f"Running: {test_name}")
            logger.info(f"{'='*60}")

            try:
                if test_func():
                    passed_tests += 1
                    logger.info(f"✅ {test_name} PASSED")
                else:
                    logger.error(f"❌ {test_name} FAILED")
            except Exception as e:
                logger.error(f"❌ {test_name} FAILED with exception: {e}")

        # Calculate summary
        total_duration = time.time() - start_time
        success_rate = passed_tests / total_tests

        self.test_results['summary'] = {
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'failed_tests': total_tests - passed_tests,
            'success_rate': success_rate,
            'total_duration': total_duration,
            'system_ready': success_rate >= 0.8
        }

        # Print final summary
        logger.info(f"\n{'='*60}")
        logger.info(f"UAT TESTING SUMMARY")
        logger.info(f"{'='*60}")
        logger.info(f"Total Tests: {total_tests}")
        logger.info(f"Passed: {passed_tests}")
        logger.info(f"Failed: {total_tests - passed_tests}")
        logger.info(f"Success Rate: {success_rate:.1%}")
        logger.info(f"Total Duration: {total_duration:.2f}s")
        logger.info(f"System Ready: {'✅ YES' if self.test_results['summary']['system_ready'] else '❌ NO'}")

        # Save results to file
        results_file = f"uat_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(results_file, 'w') as f:
            json.dump(self.test_results, f, indent=2, default=str)
        logger.info(f"📄 Results saved to: {results_file}")

        return self.test_results['summary']['system_ready']


def main():
    """Main execution function."""
    logger.info("🎯 Starting Real User UAT Testing for RAG System")

    tester = RealUserUATTester()
    success = tester.run_comprehensive_uat()

    if success:
        logger.info("🎉 UAT Testing completed successfully! System is ready for production.")
        sys.exit(0)
    else:
        logger.error("💥 UAT Testing failed! System needs fixes before production.")
        sys.exit(1)


if __name__ == "__main__":
    main()
