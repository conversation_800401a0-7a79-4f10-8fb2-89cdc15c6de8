#!/usr/bin/env python3
"""
Core Slack Ingestion Pipeline Test

This script tests the core Slack data ingestion pipeline functionality
without the advanced RAG features that have circular import issues.
"""

import os
import sys
import logging
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def setup_django():
    """Setup Django environment for testing."""
    try:
        # Add the multi_source_rag directory to Python path
        multi_source_rag_path = project_root / "multi_source_rag"
        sys.path.insert(0, str(multi_source_rag_path))

        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
        import django
        django.setup()
        logger.info("✅ Django setup complete")
        return True
    except Exception as e:
        logger.error(f"❌ Django setup failed: {e}")
        return False

def test_slack_interface():
    """Test basic Slack interface functionality."""
    logger.info("🔍 Testing Slack Interface...")

    try:
        from apps.documents.interfaces.slack.local_slack import LocalSlackSourceInterface

        config = {
            "data_dir": "/Users/<USER>/Desktop/RAGSearch/data",
            "channel_id": "C065QSSNH8A",
            "use_local_files": True
        }

        interface = LocalSlackSourceInterface(config)
        logger.info("✅ Local Slack interface created")

        # Test document fetching
        documents = interface.fetch_documents(limit=5)
        logger.info(f"✅ Fetched {len(documents)} documents")

        if documents:
            sample_doc = documents[0]
            required_fields = ["content", "metadata", "id"]
            for field in required_fields:
                assert field in sample_doc, f"Document should have {field} field"
            logger.info("✅ Document structure validated")

        return True, {"documents": len(documents)}

    except Exception as e:
        logger.error(f"❌ Slack interface test failed: {e}")
        return False, {}

def test_basic_ingestion():
    """Test basic ingestion without advanced features."""
    logger.info("🔍 Testing Basic Ingestion...")

    try:
        from apps.documents.services.ingestion import IngestionService
        from apps.documents.models import DocumentSource
        from apps.accounts.models import Tenant
        from django.contrib.auth.models import User

        # Get or create test tenant and user
        user, _ = User.objects.get_or_create(username='test_user', defaults={'email': '<EMAIL>'})
        tenant, _ = Tenant.objects.get_or_create(slug='test-tenant', defaults={'name': 'Test Tenant', 'owner': user})

        # Create test document source
        source, _ = DocumentSource.objects.get_or_create(
            name='test_slack_basic',
            source_type='slack',
            tenant=tenant,
            defaults={'config': {}}
        )

        # Create basic ingestion service
        service = IngestionService(tenant.slug)
        logger.info("✅ Basic ingestion service created")

        # Test document data
        test_document = {
            "id": "slack_basic_test_001",
            "title": "Test Slack Message",
            "content": "This is a test Slack message for basic ingestion testing.",
            "metadata": {
                "source": "slack",
                "channel_id": "C065QSSNH8A",
                "user_name": "test_user",
                "timestamp": "2024-01-15T10:30:00Z"
            }
        }

        # Process document
        raw_doc, chunks = service.process_document(test_document, source)
        logger.info(f"✅ Document processed: {len(chunks)} chunks created")

        # Cleanup
        raw_doc.delete()

        return True, {"chunks": len(chunks)}

    except Exception as e:
        logger.error(f"❌ Basic ingestion test failed: {e}")
        return False, {}

def test_vector_storage():
    """Test vector storage connectivity."""
    logger.info("🔍 Testing Vector Storage...")

    try:
        from apps.core.utils.vectorstore import get_qdrant_client
        from apps.core.utils.embedding_config import get_embedding_info

        # Test Qdrant connection
        client = get_qdrant_client()
        logger.info("✅ Qdrant client connected")

        # Test embedding info
        embedding_info = get_embedding_info()
        logger.info(f"✅ Embedding model: {embedding_info.get('model_name')}")

        # Test collections
        collections = client.get_collections()
        collection_names = [col.name for col in collections.collections]
        logger.info(f"✅ Available collections: {len(collection_names)}")

        return True, {"collections": len(collection_names)}

    except Exception as e:
        logger.error(f"❌ Vector storage test failed: {e}")
        return False, {}

def test_local_data_processing():
    """Test processing of local Slack data."""
    logger.info("🔍 Testing Local Data Processing...")

    try:
        data_dir = Path("/Users/<USER>/Desktop/RAGSearch/data")
        if not data_dir.exists():
            logger.warning("⚠️ No local data directory found")
            return True, {}

        # Find channel directories
        channel_dirs = [d for d in data_dir.iterdir() if d.is_dir() and d.name.startswith("channel_")]

        if not channel_dirs:
            logger.warning("⚠️ No channel directories found")
            return True, {}

        # Test with first available channel
        channel_dir = channel_dirs[0]
        channel_id = channel_dir.name.replace("channel_", "")
        logger.info(f"Testing with channel: {channel_id}")

        from apps.documents.interfaces.slack.local_slack import LocalSlackSourceInterface

        config = {
            "data_dir": str(data_dir),
            "channel_id": channel_id,
            "use_local_files": True
        }

        interface = LocalSlackSourceInterface(config)
        documents = interface.fetch_documents(limit=10)

        logger.info(f"✅ Processed {len(documents)} documents from local data")
        return True, {"documents": len(documents)}

    except Exception as e:
        logger.error(f"❌ Local data processing test failed: {e}")
        return False, {}

def run_core_test():
    """Run core tests without advanced RAG features."""
    logger.info("🚀 Starting Core Slack Ingestion Pipeline Test")
    logger.info("=" * 60)

    # Setup Django
    if not setup_django():
        logger.error("❌ Cannot proceed without Django setup")
        return False

    tests = [
        ("Slack Interface", test_slack_interface),
        ("Basic Ingestion", test_basic_ingestion),
        ("Vector Storage", test_vector_storage),
        ("Local Data Processing", test_local_data_processing),
    ]

    passed = 0
    total = len(tests)
    results = {}

    for test_name, test_func in tests:
        logger.info(f"\n📋 {test_name}")
        logger.info("-" * 40)
        try:
            result = test_func()
            if isinstance(result, tuple):
                success, data = result
                results[test_name] = data
            else:
                success = result

            if success:
                passed += 1
                logger.info(f"✅ {test_name}: PASSED")
            else:
                logger.error(f"❌ {test_name}: FAILED")
        except Exception as e:
            logger.error(f"❌ {test_name}: ERROR - {e}")

    logger.info("\n" + "=" * 60)
    logger.info(f"🎯 CORE TEST SUMMARY: {passed}/{total} tests passed")

    if passed == total:
        logger.info("🎉 ALL CORE TESTS PASSED - BASIC SLACK INGESTION VALIDATED!")
        return True
    else:
        logger.error("❌ SOME CORE TESTS FAILED - Review issues above")
        return False

if __name__ == "__main__":
    success = run_core_test()
    exit(0 if success else 1)
