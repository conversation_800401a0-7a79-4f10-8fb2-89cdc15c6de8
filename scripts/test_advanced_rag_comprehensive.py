#!/usr/bin/env python3
"""
Comprehensive Advanced RAG Features Test

This script thoroughly validates all advanced RAG features with quality checks:
1. Domain Classification - accuracy and confidence validation
2. Complexity Analysis - multi-dimensional analysis validation
3. File Content Processing - file-level retrieval validation
4. Enhanced Ingestion Pipeline - end-to-end integration validation
5. LlamaIndex Manager - agentic search validation
6. Cross-Domain Intelligence - routing validation
7. Ultimate Agentic Search - sophistication level validation

Each test validates not just that responses are returned, but that the quality
and functionality meet production standards.
"""

import os
import sys
import logging
import traceback
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Setup comprehensive logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(project_root / 'advanced_rag_test.log')
    ]
)
logger = logging.getLogger(__name__)

def setup_django():
    """Setup Django environment for testing."""
    try:
        # Add the multi_source_rag directory to Python path
        multi_source_rag_path = project_root / "multi_source_rag"
        sys.path.insert(0, str(multi_source_rag_path))

        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
        import django
        django.setup()
        logger.info("✅ Django setup complete")
        return True
    except Exception as e:
        logger.error(f"❌ Django setup failed: {e}")
        logger.error(f"Error details: {traceback.format_exc()}")
        return False

def create_test_document(content_type="engineering", complexity="moderate"):
    """Create test documents with different characteristics."""
    documents = {
        "engineering": {
            "simple": {
                "title": "Simple Bug Fix",
                "content": "Fixed a small CSS issue in the header. Changed color from blue to green.",
                "metadata": {
                    "channel_name": "engineering",
                    "user_name": "developer",
                    "has_code": True,
                    "technical_terms": ["CSS", "header"],
                    "participant_count": 1,
                    "thread_count": 0
                }
            },
            "moderate": {
                "title": "API Implementation Discussion",
                "content": """
                We need to implement a new REST API for user authentication.
                The API should support OAuth 2.0, JWT tokens, and rate limiting.
                We should also consider security best practices and CORS handling.
                The implementation will involve database changes and caching strategies.
                """,
                "metadata": {
                    "channel_name": "engineering",
                    "user_name": "tech_lead",
                    "has_code": True,
                    "technical_terms": ["REST API", "OAuth", "JWT", "rate limiting", "CORS", "database", "caching"],
                    "participant_count": 3,
                    "thread_count": 2
                }
            },
            "complex": {
                "title": "Distributed System Architecture",
                "content": """
                We're designing a distributed microservices architecture with the following components:
                1. API Gateway with load balancing and service discovery
                2. Event-driven architecture using Apache Kafka for message streaming
                3. Database sharding across multiple PostgreSQL instances
                4. Redis cluster for distributed caching and session management
                5. Elasticsearch for full-text search and analytics
                6. Docker containers orchestrated with Kubernetes
                7. CI/CD pipeline with automated testing and deployment
                8. Monitoring with Prometheus and Grafana
                9. Distributed tracing with Jaeger
                10. Security implementation with OAuth 2.0, JWT, and API rate limiting

                The system needs to handle 100k+ concurrent users with sub-100ms response times.
                We need to implement circuit breakers, bulkhead patterns, and graceful degradation.
                """,
                "metadata": {
                    "channel_name": "architecture",
                    "user_name": "architect",
                    "has_code": True,
                    "technical_terms": [
                        "microservices", "API Gateway", "load balancing", "service discovery",
                        "Apache Kafka", "PostgreSQL", "Redis", "Elasticsearch", "Docker",
                        "Kubernetes", "CI/CD", "Prometheus", "Grafana", "Jaeger", "OAuth",
                        "circuit breakers", "bulkhead patterns"
                    ],
                    "participant_count": 8,
                    "thread_count": 15
                }
            }
        },
        "general": {
            "simple": {
                "title": "Team Lunch",
                "content": "Hey everyone, let's have lunch together at 12:30 PM today. The new Italian place?",
                "metadata": {
                    "channel_name": "general",
                    "user_name": "team_member",
                    "has_code": False,
                    "technical_terms": [],
                    "participant_count": 1,
                    "thread_count": 0
                }
            }
        },
        "product": {
            "moderate": {
                "title": "Feature Requirements Discussion",
                "content": """
                Product Requirements for User Dashboard v2.0:

                1. User Analytics: Display user engagement metrics, session duration, and feature usage
                2. Customizable Widgets: Allow users to add/remove dashboard components
                3. Real-time Updates: Live data refresh every 30 seconds
                4. Export Functionality: PDF and CSV export options
                5. Mobile Responsiveness: Optimized for tablets and smartphones
                6. Accessibility: WCAG 2.1 AA compliance
                7. Performance: Page load time under 2 seconds

                Success Metrics:
                - 25% increase in user engagement
                - 40% reduction in support tickets
                - 90% user satisfaction score
                """,
                "metadata": {
                    "channel_name": "product",
                    "user_name": "product_manager",
                    "has_code": False,
                    "technical_terms": ["analytics", "widgets", "real-time", "PDF", "CSV", "mobile", "WCAG", "performance"],
                    "participant_count": 4,
                    "thread_count": 3
                }
            }
        }
    }

    return documents[content_type][complexity]

def validate_domain_classification_quality(classification, expected_domain=None, min_confidence=0.6):
    """Validate domain classification quality and accuracy."""
    quality_checks = {
        "has_primary_domain": hasattr(classification, 'primary_domain') and classification.primary_domain is not None,
        "confidence_in_range": hasattr(classification, 'confidence') and 0.0 <= classification.confidence <= 1.0,
        "confidence_above_threshold": hasattr(classification, 'confidence') and classification.confidence >= min_confidence,
        "has_secondary_domains": hasattr(classification, 'secondary_domains') and isinstance(classification.secondary_domains, list),
        "has_routing_metadata": hasattr(classification, 'routing_metadata') and isinstance(classification.routing_metadata, dict),
        "routing_metadata_not_empty": hasattr(classification, 'routing_metadata') and len(classification.routing_metadata) > 0
    }

    if expected_domain:
        quality_checks["correct_domain"] = str(classification.primary_domain).lower().find(expected_domain.lower()) >= 0

    passed_checks = sum(quality_checks.values())
    total_checks = len(quality_checks)
    quality_score = passed_checks / total_checks

    return {
        "quality_score": quality_score,
        "passed_checks": passed_checks,
        "total_checks": total_checks,
        "checks": quality_checks,
        "classification": {
            "primary_domain": str(classification.primary_domain) if hasattr(classification, 'primary_domain') else None,
            "confidence": classification.confidence if hasattr(classification, 'confidence') else None,
            "secondary_domains": [str(d) for d in classification.secondary_domains] if hasattr(classification, 'secondary_domains') else [],
            "routing_metadata": classification.routing_metadata if hasattr(classification, 'routing_metadata') else {}
        }
    }

def validate_complexity_analysis_quality(analysis, expected_level=None):
    """Validate complexity analysis quality and accuracy."""
    quality_checks = {
        "has_semantic_complexity": hasattr(analysis, 'semantic_complexity') and 0.0 <= analysis.semantic_complexity <= 1.0,
        "has_domain_complexity": hasattr(analysis, 'domain_complexity') and 0.0 <= analysis.domain_complexity <= 1.0,
        "has_temporal_complexity": hasattr(analysis, 'temporal_complexity') and 0.0 <= analysis.temporal_complexity <= 1.0,
        "has_structural_complexity": hasattr(analysis, 'structural_complexity') and 0.0 <= analysis.structural_complexity <= 1.0,
        "has_contextual_complexity": hasattr(analysis, 'contextual_complexity') and 0.0 <= analysis.contextual_complexity <= 1.0,
        "has_overall_level": hasattr(analysis, 'overall_level') and analysis.overall_level in ["SIMPLE", "MODERATE", "COMPLEX", "ADAPTIVE"],
        "has_strategy_recommendations": hasattr(analysis, 'strategy_recommendations') and isinstance(analysis.strategy_recommendations, dict),
        "has_performance_hints": hasattr(analysis, 'performance_hints') and isinstance(analysis.performance_hints, dict),
        "strategy_recommendations_not_empty": hasattr(analysis, 'strategy_recommendations') and len(analysis.strategy_recommendations) > 0,
        "performance_hints_not_empty": hasattr(analysis, 'performance_hints') and len(analysis.performance_hints) > 0
    }

    if expected_level:
        quality_checks["correct_level"] = hasattr(analysis, 'overall_level') and analysis.overall_level.upper() == expected_level.upper()

    # Validate complexity score consistency
    if all(hasattr(analysis, attr) for attr in ['semantic_complexity', 'domain_complexity', 'temporal_complexity', 'structural_complexity', 'contextual_complexity']):
        avg_complexity = (analysis.semantic_complexity + analysis.domain_complexity + analysis.temporal_complexity +
                         analysis.structural_complexity + analysis.contextual_complexity) / 5

        level_mapping = {"SIMPLE": 0.3, "MODERATE": 0.6, "COMPLEX": 0.8, "ADAPTIVE": 1.0}
        expected_range = level_mapping.get(analysis.overall_level, 0.5)

        quality_checks["complexity_level_consistent"] = abs(avg_complexity - expected_range) < 0.3

    passed_checks = sum(quality_checks.values())
    total_checks = len(quality_checks)
    quality_score = passed_checks / total_checks

    return {
        "quality_score": quality_score,
        "passed_checks": passed_checks,
        "total_checks": total_checks,
        "checks": quality_checks,
        "analysis": {
            "semantic_complexity": analysis.semantic_complexity if hasattr(analysis, 'semantic_complexity') else None,
            "domain_complexity": analysis.domain_complexity if hasattr(analysis, 'domain_complexity') else None,
            "temporal_complexity": analysis.temporal_complexity if hasattr(analysis, 'temporal_complexity') else None,
            "structural_complexity": analysis.structural_complexity if hasattr(analysis, 'structural_complexity') else None,
            "contextual_complexity": analysis.contextual_complexity if hasattr(analysis, 'contextual_complexity') else None,
            "overall_level": analysis.overall_level if hasattr(analysis, 'overall_level') else None,
            "strategy_recommendations": analysis.strategy_recommendations if hasattr(analysis, 'strategy_recommendations') else {},
            "performance_hints": analysis.performance_hints if hasattr(analysis, 'performance_hints') else {}
        }
    }

def test_domain_classification_comprehensive():
    """Comprehensive test of domain classification with quality validation."""
    logger.info("🔍 Testing Domain Classification Comprehensively...")

    try:
        from apps.core.services.domain_classification import DomainClassificationService, DataDomain
        from apps.documents.models import RawDocument, DocumentSource
        from apps.accounts.models import Tenant
        from django.contrib.auth.models import User

        # Get or create test tenant and user
        user, _ = User.objects.get_or_create(username='test_user', defaults={'email': '<EMAIL>'})
        tenant, _ = Tenant.objects.get_or_create(slug='test-tenant', defaults={'name': 'Test Tenant', 'owner': user})

        # Create test document source
        source, _ = DocumentSource.objects.get_or_create(
            name='test_domain_classification',
            source_type='slack',
            tenant=tenant,
            defaults={'config': {}}
        )

        classifier = DomainClassificationService()
        test_results = {}

        # Test different document types
        test_cases = [
            ("engineering_simple", "engineering", "simple"),
            ("engineering_moderate", "engineering", "moderate"),
            ("engineering_complex", "engineering", "complex"),
            ("general_simple", "general", "simple"),
            ("product_moderate", "product", "moderate")
        ]

        for test_name, content_type, complexity in test_cases:
            logger.info(f"Testing {test_name}...")

            # Create test document
            doc_data = create_test_document(content_type, complexity)
            test_doc = RawDocument.objects.create(
                title=doc_data["title"],
                content_type="slack_message",
                source=source,
                metadata=doc_data["metadata"]
            )

            # Create document content separately
            from apps.documents.models import DocumentContent
            DocumentContent.objects.create(
                document=test_doc,
                content=doc_data["content"],
                content_format="slack"
            )

            # Classify document
            classification = classifier.classify_document(test_doc)

            # Validate quality
            expected_domain = content_type if content_type != "general" else "slack_conversations"
            quality_result = validate_domain_classification_quality(
                classification,
                expected_domain=expected_domain,
                min_confidence=0.5
            )

            test_results[test_name] = quality_result

            logger.info(f"✅ {test_name}: Quality Score {quality_result['quality_score']:.2f} "
                       f"({quality_result['passed_checks']}/{quality_result['total_checks']} checks passed)")
            logger.info(f"   Domain: {quality_result['classification']['primary_domain']} "
                       f"(confidence: {quality_result['classification']['confidence']:.2f})")

            # Cleanup
            test_doc.delete()

        # Calculate overall quality
        overall_quality = sum(result['quality_score'] for result in test_results.values()) / len(test_results)

        logger.info(f"✅ Domain Classification Overall Quality: {overall_quality:.2f}")

        # Quality threshold check
        quality_threshold = 0.8
        if overall_quality >= quality_threshold:
            logger.info(f"✅ Domain classification meets quality threshold ({quality_threshold})")
            return True, {"overall_quality": overall_quality, "test_results": test_results}
        else:
            logger.error(f"❌ Domain classification below quality threshold ({quality_threshold})")
            return False, {"overall_quality": overall_quality, "test_results": test_results}

    except Exception as e:
        logger.error(f"❌ Domain classification comprehensive test failed: {e}")
        logger.error(f"Error details: {traceback.format_exc()}")
        return False, {}


def test_complexity_analysis_comprehensive():
    """Comprehensive test of complexity analysis with quality validation."""
    logger.info("🔍 Testing Complexity Analysis Comprehensively...")

    try:
        from apps.core.services.complexity_analysis import ComplexityAnalysisEngine
        from apps.documents.models import RawDocument, DocumentSource
        from apps.accounts.models import Tenant
        from django.contrib.auth.models import User

        # Get or create test tenant and user
        user, _ = User.objects.get_or_create(username='test_user', defaults={'email': '<EMAIL>'})
        tenant, _ = Tenant.objects.get_or_create(slug='test-tenant', defaults={'name': 'Test Tenant', 'owner': user})

        # Create test document source
        source, _ = DocumentSource.objects.get_or_create(
            name='test_complexity_analysis',
            source_type='slack',
            tenant=tenant,
            defaults={'config': {}}
        )

        analyzer = ComplexityAnalysisEngine()
        test_results = {}

        # Test different complexity levels
        test_cases = [
            ("simple_engineering", "engineering", "simple", "SIMPLE"),
            ("moderate_engineering", "engineering", "moderate", "MODERATE"),
            ("complex_engineering", "engineering", "complex", "COMPLEX"),
            ("simple_general", "general", "simple", "SIMPLE"),
            ("moderate_product", "product", "moderate", "MODERATE")
        ]

        for test_name, content_type, complexity, expected_level in test_cases:
            logger.info(f"Testing {test_name}...")

            # Create test document
            doc_data = create_test_document(content_type, complexity)
            test_doc = RawDocument.objects.create(
                title=doc_data["title"],
                content_type="slack_message",
                source=source,
                metadata=doc_data["metadata"]
            )

            # Create document content separately
            from apps.documents.models import DocumentContent
            DocumentContent.objects.create(
                document=test_doc,
                content=doc_data["content"],
                content_format="slack"
            )

            # Analyze complexity
            analysis = analyzer.analyze_document_complexity(test_doc)

            # Validate quality
            quality_result = validate_complexity_analysis_quality(analysis, expected_level=expected_level)

            test_results[test_name] = quality_result

            logger.info(f"✅ {test_name}: Quality Score {quality_result['quality_score']:.2f} "
                       f"({quality_result['passed_checks']}/{quality_result['total_checks']} checks passed)")
            logger.info(f"   Level: {quality_result['analysis']['overall_level']} "
                       f"(expected: {expected_level})")
            logger.info(f"   Complexities: S:{quality_result['analysis']['semantic_complexity']:.2f} "
                       f"D:{quality_result['analysis']['domain_complexity']:.2f} "
                       f"T:{quality_result['analysis']['temporal_complexity']:.2f}")

            # Cleanup
            test_doc.delete()

        # Calculate overall quality
        overall_quality = sum(result['quality_score'] for result in test_results.values()) / len(test_results)

        logger.info(f"✅ Complexity Analysis Overall Quality: {overall_quality:.2f}")

        # Quality threshold check
        quality_threshold = 0.8
        if overall_quality >= quality_threshold:
            logger.info(f"✅ Complexity analysis meets quality threshold ({quality_threshold})")
            return True, {"overall_quality": overall_quality, "test_results": test_results}
        else:
            logger.error(f"❌ Complexity analysis below quality threshold ({quality_threshold})")
            return False, {"overall_quality": overall_quality, "test_results": test_results}

    except Exception as e:
        logger.error(f"❌ Complexity analysis comprehensive test failed: {e}")
        logger.error(f"Error details: {traceback.format_exc()}")
        return False, {}


if __name__ == "__main__":
    logger.info("🚀 Starting Comprehensive Advanced RAG Features Test")
    logger.info("=" * 80)

    if not setup_django():
        logger.error("❌ Cannot proceed without Django setup")
        exit(1)

    logger.info("✅ Setup complete - proceeding with advanced RAG feature tests")

    # Run comprehensive tests
    tests = [
        ("Domain Classification Comprehensive", test_domain_classification_comprehensive),
        ("Complexity Analysis Comprehensive", test_complexity_analysis_comprehensive),
    ]

    passed = 0
    total = len(tests)
    results = {}

    for test_name, test_func in tests:
        logger.info(f"\n📋 {test_name}")
        logger.info("-" * 60)
        try:
            result = test_func()
            if isinstance(result, tuple):
                success, data = result
                results[test_name] = data
            else:
                success = result

            if success:
                passed += 1
                logger.info(f"✅ {test_name}: PASSED")
            else:
                logger.error(f"❌ {test_name}: FAILED")
        except Exception as e:
            logger.error(f"❌ {test_name}: ERROR - {e}")

    logger.info("\n" + "=" * 80)
    logger.info(f"🎯 COMPREHENSIVE TEST SUMMARY: {passed}/{total} tests passed")

    if passed == total:
        logger.info("🎉 ALL COMPREHENSIVE TESTS PASSED!")
        exit(0)
    else:
        logger.error("❌ SOME COMPREHENSIVE TESTS FAILED")
        exit(1)
