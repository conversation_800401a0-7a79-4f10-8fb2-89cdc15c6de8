#!/usr/bin/env python3
"""
Manual Search Validation Script

Performs direct testing of the search functionality to validate the RAG system.
"""

import os
import sys
import time
import json
from pathlib import Path
from datetime import datetime

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Setup Django
multi_source_rag_path = project_root / "multi_source_rag"
sys.path.insert(0, str(multi_source_rag_path))

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
import django
django.setup()

def test_search_functionality():
    """Test search functionality directly."""
    print("🔍 Manual Search Validation")
    print("=" * 60)
    
    try:
        from apps.search.services.rag_search_service import RAGSearchService
        
        # Initialize search service
        search_service = RAGSearchService(tenant_slug='stride')
        print("✅ RAG Search Service initialized")
        
        # Test queries of different complexity
        test_queries = [
            "What is authentication?",
            "How does OAuth work?",
            "Who worked on the API?",
            "What are the main challenges with our system?",
            "Explain our rate limiting strategy"
        ]
        
        results = []
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n📋 Test {i}: {query}")
            print("-" * 40)
            
            start_time = time.time()
            
            try:
                # Perform search
                response = search_service.search(
                    query=query,
                    limit=5,
                    intent="slack"
                )
                
                duration = time.time() - start_time
                
                # Analyze response
                has_response = bool(response.get('response'))
                response_length = len(response.get('response', ''))
                source_count = len(response.get('source_nodes', []))
                
                result = {
                    "query": query,
                    "duration": duration,
                    "has_response": has_response,
                    "response_length": response_length,
                    "source_count": source_count,
                    "response_preview": response.get('response', '')[:200] + "..." if response.get('response') else None
                }
                
                results.append(result)
                
                print(f"✅ Duration: {duration:.2f}s")
                print(f"✅ Response length: {response_length} characters")
                print(f"✅ Sources found: {source_count}")
                
                if response.get('response'):
                    print(f"📄 Response preview: {response.get('response')[:150]}...")
                else:
                    print("⚠️ No response generated")
                
            except Exception as e:
                print(f"❌ Query failed: {e}")
                results.append({
                    "query": query,
                    "error": str(e),
                    "duration": time.time() - start_time
                })
        
        # Summary
        print("\n" + "=" * 60)
        print("🎯 MANUAL SEARCH VALIDATION SUMMARY")
        print("=" * 60)
        
        successful_queries = [r for r in results if 'error' not in r and r.get('has_response')]
        avg_duration = sum(r['duration'] for r in results) / len(results)
        avg_response_length = sum(r.get('response_length', 0) for r in successful_queries) / len(successful_queries) if successful_queries else 0
        avg_sources = sum(r.get('source_count', 0) for r in successful_queries) / len(successful_queries) if successful_queries else 0
        
        print(f"Total queries tested: {len(test_queries)}")
        print(f"Successful queries: {len(successful_queries)}")
        print(f"Success rate: {len(successful_queries)/len(test_queries)*100:.1f}%")
        print(f"Average duration: {avg_duration:.2f} seconds")
        print(f"Average response length: {avg_response_length:.0f} characters")
        print(f"Average sources per query: {avg_sources:.1f}")
        
        # Save results
        results_file = project_root / f"manual_search_validation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(results_file, 'w') as f:
            json.dump({
                "timestamp": datetime.now().isoformat(),
                "summary": {
                    "total_queries": len(test_queries),
                    "successful_queries": len(successful_queries),
                    "success_rate": len(successful_queries)/len(test_queries),
                    "avg_duration": avg_duration,
                    "avg_response_length": avg_response_length,
                    "avg_sources": avg_sources
                },
                "detailed_results": results
            }, indent=2)
        
        print(f"\n📄 Results saved to: {results_file}")
        
        if len(successful_queries) >= len(test_queries) * 0.8:
            print("\n🎉 SEARCH FUNCTIONALITY VALIDATION: PASSED")
            return True
        else:
            print("\n❌ SEARCH FUNCTIONALITY VALIDATION: FAILED")
            return False
            
    except Exception as e:
        print(f"❌ Search validation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_state():
    """Test current database state."""
    print("\n🔍 Database State Validation")
    print("=" * 60)
    
    try:
        from apps.documents.models import RawDocument, DocumentChunk, EmbeddingMetadata
        from apps.accounts.models import Tenant
        
        tenant = Tenant.objects.get(slug='stride')
        
        total_docs = RawDocument.objects.filter(source__tenant=tenant).count()
        total_chunks = DocumentChunk.objects.filter(document__source__tenant=tenant).count()
        total_embeddings = EmbeddingMetadata.objects.filter(chunk__document__source__tenant=tenant).count()
        
        print(f"✅ Tenant: {tenant.name}")
        print(f"✅ Total documents: {total_docs}")
        print(f"✅ Total chunks: {total_chunks}")
        print(f"✅ Total embeddings: {total_embeddings}")
        
        if total_docs > 0:
            print(f"✅ Average chunks per document: {total_chunks/total_docs:.1f}")
            if total_chunks > 0:
                print(f"✅ Embedding coverage: {total_embeddings/total_chunks*100:.1f}%")
        
        # Check recent documents
        recent_docs = RawDocument.objects.filter(source__tenant=tenant).order_by('-created_at')[:3]
        print(f"\n📄 Recent documents:")
        for doc in recent_docs:
            print(f"  - {doc.external_id}: {doc.title[:50]}...")
        
        return total_docs > 0 and total_embeddings > 0
        
    except Exception as e:
        print(f"❌ Database state check failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Manual Search and Database Validation")
    print("=" * 80)
    
    # Test database state
    db_healthy = test_database_state()
    
    if db_healthy:
        # Test search functionality
        search_working = test_search_functionality()
        
        if search_working:
            print("\n🎉 OVERALL VALIDATION: SYSTEM IS FUNCTIONAL")
            sys.exit(0)
        else:
            print("\n⚠️ OVERALL VALIDATION: SEARCH ISSUES DETECTED")
            sys.exit(1)
    else:
        print("\n❌ OVERALL VALIDATION: DATABASE ISSUES DETECTED")
        sys.exit(1)
