#!/usr/bin/env python3
"""
Test script to verify hybrid search fix with docstore population.
"""

import os
import sys
import django

# Add the project root to Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'multi_source_rag'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'multi_source_rag.settings')
django.setup()

from apps.search.services.rag_search_service import RAGSearchService
from apps.core.llama_index_manager import LlamaIndexManager
from llama_index.core import Document
from apps.core.utils.collection_manager import get_collection_name
from apps.core.utils.llama_index_vectorstore import get_vector_store
from apps.core.utils.llama_index_embeddings import get_embedding_model_for_content
from llama_index.core.ingestion import IngestionPipeline
from llama_index.core.storage.docstore import SimpleDocumentStore
from llama_index.core.storage import StorageContext
from llama_index.core.node_parser import SentenceSplitter

def test_docstore_population():
    """Test that documents are properly stored in docstore for BM25."""
    print("🧪 Testing docstore population for hybrid search...")
    
    tenant_slug = 'stride'
    
    # Create a test document
    test_doc = Document(
        text="This is a test document about Curana customer feedback. The customer mentioned that the number of employees (356) feels light compared to what they are expecting. This is important feedback for our product development.",
        metadata={
            "document_id": "test_hybrid_doc",
            "source_type": "test",
            "content_type": "document",
            "tenant_id": tenant_slug
        }
    )
    
    # Create pipeline with docstore
    collection_name = get_collection_name(tenant_slug, intent="general")
    vector_store = get_vector_store(collection_name=collection_name)
    embed_model = get_embedding_model_for_content(content_type="document")
    
    # Create docstore for BM25 hybrid search support
    docstore = SimpleDocumentStore()
    storage_context = StorageContext.from_defaults(
        vector_store=vector_store,
        docstore=docstore
    )
    
    pipeline = IngestionPipeline(
        transformations=[
            SentenceSplitter(
                chunk_size=500,
                chunk_overlap=50
            ),
            embed_model,
        ],
        storage_context=storage_context
    )
    
    # Process the document
    print("📄 Processing test document...")
    nodes = pipeline.run(documents=[test_doc])
    print(f"✅ Created {len(nodes)} nodes")
    
    # Check if docstore has documents
    print(f"📚 Docstore has {len(docstore.docs)} documents")
    
    # Now test hybrid retriever creation
    print("🔍 Testing hybrid retriever creation...")
    manager = LlamaIndexManager(tenant_slug=tenant_slug)
    
    try:
        # Get index and check docstore
        index = manager.get_index(intent="general")
        print(f"📊 Index docstore has {len(index.docstore.docs)} documents")
        
        # Try to create hybrid retriever
        hybrid_retriever = manager._create_hybrid_retriever(index, intent="general", top_k=5)
        print(f"✅ Successfully created hybrid retriever: {type(hybrid_retriever).__name__}")
        
        # Check what retrievers are in the fusion retriever
        if hasattr(hybrid_retriever, '_retrievers'):
            retrievers = hybrid_retriever._retrievers
            print(f"🔧 Fusion retriever has {len(retrievers)} retrievers:")
            for i, retriever in enumerate(retrievers):
                print(f"   {i+1}. {type(retriever).__name__}")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to create hybrid retriever: {e}")
        return False

def test_search_with_hybrid():
    """Test search with the new hybrid setup."""
    print("\n🔍 Testing search with hybrid retriever...")
    
    service = RAGSearchService(tenant_slug='stride')
    results = service.search("What is the latest Curana customer feedback?")
    
    print(f"📝 Answer: {results['answer']}")
    print(f"📊 Strategy: {results.get('metadata', {}).get('strategy', 'Unknown')}")
    print(f"📚 Sources: {len(results.get('source_nodes', []))}")
    
    return results

if __name__ == "__main__":
    print("🚀 Testing Hybrid Search Fix")
    print("=" * 50)
    
    # Test 1: Docstore population
    success = test_docstore_population()
    
    if success:
        print("\n" + "=" * 50)
        # Test 2: Search with hybrid
        test_search_with_hybrid()
    
    print("\n✅ Test completed!")
