#!/usr/bin/env python3
"""
Real Slack Data Ingestion Test

This script tests the complete Slack ingestion pipeline with real data
from the local Slack export files.
"""

import os
import sys
import logging
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def setup_django():
    """Setup Django environment for testing."""
    try:
        # Add the multi_source_rag directory to Python path
        multi_source_rag_path = project_root / "multi_source_rag"
        sys.path.insert(0, str(multi_source_rag_path))

        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
        import django
        django.setup()
        logger.info("✅ Django setup complete")
        return True
    except Exception as e:
        logger.error(f"❌ Django setup failed: {e}")
        return False

def test_real_slack_ingestion():
    """Test ingestion with real Slack data."""
    logger.info("🔍 Testing Real Slack Data Ingestion...")

    try:
        from apps.documents.interfaces.slack.local_slack import LocalSlackSourceInterface
        from apps.documents.services.ingestion import IngestionService
        from apps.documents.models import DocumentSource, RawDocument, DocumentChunk
        from apps.accounts.models import Tenant
        from django.contrib.auth.models import User

        # Get or create test tenant and user
        user, _ = User.objects.get_or_create(username='test_user', defaults={'email': '<EMAIL>'})
        tenant, _ = Tenant.objects.get_or_create(slug='stride', defaults={'name': 'Stride Tenant', 'owner': user})

        # Create test document source
        source, _ = DocumentSource.objects.get_or_create(
            name='slack_real_test',
            source_type='slack',
            tenant=tenant,
            defaults={'config': {}}
        )

        # Create Slack interface
        config = {
            "data_dir": "/Users/<USER>/Desktop/RAGSearch/data",
            "channel_id": "C065QSSNH8A",
            "use_local_files": True
        }

        interface = LocalSlackSourceInterface(config)
        logger.info("✅ Slack interface created")

        # Fetch documents
        documents = interface.fetch_documents(limit=5)
        logger.info(f"✅ Fetched {len(documents)} documents from Slack data")

        if not documents:
            logger.warning("⚠️ No documents found in Slack data")
            return True, {"documents": 0, "processed": 0}

        # Create ingestion service
        ingestion_service = IngestionService(tenant.slug)
        logger.info("✅ Ingestion service created")

        # Process documents
        processed_count = 0
        total_chunks = 0

        for i, document in enumerate(documents):
            try:
                logger.info(f"Processing document {i+1}/{len(documents)}: {document.get('id', 'unknown')}")

                # Process document
                raw_doc = ingestion_service.process_document(document, source)

                # Count chunks
                chunk_count = DocumentChunk.objects.filter(document=raw_doc).count()
                total_chunks += chunk_count
                processed_count += 1

                logger.info(f"✅ Document processed: {chunk_count} chunks created")

            except Exception as e:
                logger.error(f"❌ Failed to process document {i+1}: {e}")
                continue

        logger.info(f"✅ Processed {processed_count}/{len(documents)} documents")
        logger.info(f"✅ Total chunks created: {total_chunks}")

        # Verify data in database
        total_docs = RawDocument.objects.filter(source=source).count()
        total_db_chunks = DocumentChunk.objects.filter(document__source=source).count()

        logger.info(f"✅ Database verification:")
        logger.info(f"   - Documents in DB: {total_docs}")
        logger.info(f"   - Chunks in DB: {total_db_chunks}")

        return True, {
            "documents": len(documents),
            "processed": processed_count,
            "chunks": total_chunks,
            "db_docs": total_docs,
            "db_chunks": total_db_chunks
        }

    except Exception as e:
        logger.error(f"❌ Real Slack ingestion test failed: {e}")
        import traceback
        logger.error(f"Error details: {traceback.format_exc()}")
        return False, {}

def test_search_functionality():
    """Test search functionality with ingested data."""
    logger.info("🔍 Testing Search Functionality...")

    try:
        from apps.core.utils.vectorstore import get_qdrant_client
        from apps.core.utils.collection_manager import get_collection_name

        # Test Qdrant connection
        client = get_qdrant_client()

        # Check collections
        collections = client.get_collections()
        collection_names = [col.name for col in collections.collections]
        logger.info(f"✅ Available collections: {collection_names}")

        # Test search in stride collection
        stride_collection = get_collection_name("stride", intent="default")
        if stride_collection in collection_names:
            # Get collection info
            collection_info = client.get_collection(stride_collection)
            vector_count = collection_info.vectors_count
            logger.info(f"✅ Collection {stride_collection} has {vector_count} vectors")

            if vector_count > 0:
                # Test a simple search
                search_results = client.search(
                    collection_name=stride_collection,
                    query_vector=[0.1] * 768,  # Dummy vector for testing
                    limit=3
                )
                logger.info(f"✅ Search test returned {len(search_results)} results")
            else:
                logger.warning("⚠️ No vectors in collection for search test")
        else:
            logger.warning(f"⚠️ Collection {stride_collection} not found")

        return True, {
            "collections": len(collection_names),
            "stride_collection_exists": stride_collection in collection_names
        }

    except Exception as e:
        logger.error(f"❌ Search functionality test failed: {e}")
        return False, {}

def run_real_data_test():
    """Run real data ingestion test."""
    logger.info("🚀 Starting Real Slack Data Ingestion Test")
    logger.info("=" * 60)

    # Setup Django
    if not setup_django():
        logger.error("❌ Cannot proceed without Django setup")
        return False

    tests = [
        ("Real Slack Ingestion", test_real_slack_ingestion),
        ("Search Functionality", test_search_functionality),
    ]

    passed = 0
    total = len(tests)
    results = {}

    for test_name, test_func in tests:
        logger.info(f"\n📋 {test_name}")
        logger.info("-" * 40)
        try:
            result = test_func()
            if isinstance(result, tuple):
                success, data = result
                results[test_name] = data
            else:
                success = result

            if success:
                passed += 1
                logger.info(f"✅ {test_name}: PASSED")
            else:
                logger.error(f"❌ {test_name}: FAILED")
        except Exception as e:
            logger.error(f"❌ {test_name}: ERROR - {e}")

    logger.info("\n" + "=" * 60)
    logger.info(f"🎯 REAL DATA TEST SUMMARY: {passed}/{total} tests passed")

    if passed == total:
        logger.info("🎉 ALL REAL DATA TESTS PASSED - SLACK INGESTION WITH REAL DATA VALIDATED!")
        logger.info("✅ System successfully processes real Slack data")
        return True
    else:
        logger.error("❌ SOME REAL DATA TESTS FAILED - Review issues above")
        return False

if __name__ == "__main__":
    success = run_real_data_test()
    exit(0 if success else 1)
