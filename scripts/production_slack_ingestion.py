#!/usr/bin/env python3
"""
Production Slack Data Ingestion Script

This script ingests Slack data using the production IngestionService exactly as it operates
in the production environment. No hacks, fallbacks, or workarounds - only production-ready code.

Features:
- Uses production IngestionService with all advanced RAG features
- Processes real Slack data from local files
- Full compatibility with advanced RAG pipeline
- Production-grade error handling and monitoring
- Comprehensive logging and metrics
"""

import os
import sys
import logging
import traceback
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Setup production logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(project_root / 'production_slack_ingestion.log')
    ]
)
logger = logging.getLogger(__name__)

def setup_django():
    """Setup Django environment for production ingestion."""
    try:
        # Add the multi_source_rag directory to Python path
        multi_source_rag_path = project_root / "multi_source_rag"
        sys.path.insert(0, str(multi_source_rag_path))

        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
        import django
        django.setup()
        logger.info("✅ Django setup complete")
        return True
    except Exception as e:
        logger.error(f"❌ Django setup failed: {e}")
        logger.error(f"Error details: {traceback.format_exc()}")
        return False

def create_production_tenant_and_source():
    """Create production tenant and document source."""
    try:
        from apps.accounts.models import Tenant
        from apps.documents.models import DocumentSource
        from django.contrib.auth.models import User

        # Get or create production user
        user, created = User.objects.get_or_create(
            username='<EMAIL>',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Mahesh',
                'last_name': 'Admin'
            }
        )
        if created:
            logger.info("✅ Created production user")

        # Get or create production tenant
        tenant, created = Tenant.objects.get_or_create(
            slug='stride',
            defaults={
                'name': 'Stride HR',
                'owner': user
            }
        )
        if created:
            logger.info("✅ Created production tenant")

        # Create production document source for Slack
        source, created = DocumentSource.objects.get_or_create(
            name='slack_production_engineering',
            source_type='local_slack',
            tenant=tenant,
            defaults={
                'config': {
                    "data_dir": "/Users/<USER>/Desktop/RAGSearch/data",
                    "channel_id": "C065QSSNH8A",
                    "channel_name": "1-productengineering",
                    "use_local_files": True,
                    "enable_enhanced_processing": True
                },
                'is_active': True
            }
        )
        if created:
            logger.info("✅ Created production Slack document source")

        return tenant, source

    except Exception as e:
        logger.error(f"❌ Failed to create production tenant/source: {e}")
        raise

def run_production_slack_ingestion():
    """Run production Slack ingestion using the actual IngestionService."""
    logger.info("🚀 Starting Production Slack Data Ingestion")
    logger.info("=" * 80)

    if not setup_django():
        logger.error("❌ Cannot proceed without Django setup")
        return False

    try:
        # Import production services
        from apps.documents.services.ingestion import IngestionService
        from apps.documents.interfaces.slack.local_slack import LocalSlackSourceInterface

        # Create production tenant and source
        tenant, source = create_production_tenant_and_source()
        logger.info(f"✅ Using tenant: {tenant.name} (slug: {tenant.slug})")
        logger.info(f"✅ Using source: {source.name} (type: {source.source_type})")

        # Initialize production ingestion service
        ingestion_service = IngestionService(tenant.slug)
        logger.info("✅ Production IngestionService initialized")
        logger.info(f"   - Available pipelines: {list(ingestion_service.pipelines.keys())}")

        # Test Slack interface first
        logger.info("\n📋 Testing Slack Data Interface...")
        interface = LocalSlackSourceInterface(source.config)

        # Fetch documents using production interface
        documents = interface.fetch_documents(limit=50)  # Process more documents
        logger.info(f"✅ Fetched {len(documents)} documents from Slack interface")

        if not documents:
            logger.error("❌ No documents found - check data directory and files")
            return False

        # Log sample document structure
        sample_doc = documents[0]
        logger.info(f"📄 Sample document structure:")
        logger.info(f"   - ID: {sample_doc.get('id', 'N/A')}")
        logger.info(f"   - Content length: {len(sample_doc.get('content', ''))}")
        logger.info(f"   - Metadata keys: {list(sample_doc.get('metadata', {}).keys())}")

        # Run production ingestion
        logger.info("\n🔄 Starting Production Ingestion Process...")
        start_time = datetime.now()

        try:
            processed_count, failed_count = ingestion_service.process_source(
                source=source,
                batch_size=10,  # Process in smaller batches for better monitoring
                limit=50  # Limit for initial testing
            )

            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            logger.info("\n" + "=" * 80)
            logger.info("🎯 PRODUCTION INGESTION RESULTS:")
            logger.info(f"   ✅ Documents processed: {processed_count}")
            logger.info(f"   ❌ Documents failed: {failed_count}")
            logger.info(f"   ⏱️  Processing time: {duration:.2f} seconds")
            logger.info(f"   📊 Processing rate: {processed_count/duration:.2f} docs/sec")

            # Verify data in database
            logger.info("\n📊 Verifying Ingested Data...")
            from apps.documents.models import RawDocument, DocumentChunk

            total_docs = RawDocument.objects.filter(source=source).count()
            total_chunks = DocumentChunk.objects.filter(document__source=source).count()

            logger.info(f"   📄 Total documents in DB: {total_docs}")
            logger.info(f"   🧩 Total chunks in DB: {total_chunks}")

            if total_chunks > 0:
                avg_chunks_per_doc = total_chunks / total_docs if total_docs > 0 else 0
                logger.info(f"   📈 Average chunks per document: {avg_chunks_per_doc:.2f}")

            # Test vector store integration
            logger.info("\n🔍 Verifying Vector Store Integration...")
            from apps.core.utils.collection_manager import get_collection_name, is_collection_usable

            collection_name = get_collection_name(tenant.slug, intent="slack")
            is_usable = is_collection_usable(collection_name)

            logger.info(f"   📦 Collection name: {collection_name}")
            logger.info(f"   ✅ Collection usable: {is_usable}")

            if processed_count > 0:
                logger.info("\n🎉 PRODUCTION INGESTION COMPLETED SUCCESSFULLY!")
                logger.info("✅ System ready for advanced RAG search testing")
                return True
            else:
                logger.error("\n❌ PRODUCTION INGESTION FAILED!")
                logger.error("No documents were successfully processed")
                return False

        except Exception as e:
            logger.error(f"\n❌ Production ingestion failed: {e}")
            logger.error(f"Error details: {traceback.format_exc()}")
            return False

    except Exception as e:
        logger.error(f"❌ Production ingestion setup failed: {e}")
        logger.error(f"Error details: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = run_production_slack_ingestion()

    if success:
        logger.info("\n🚀 Ready for production RAG search testing!")
        logger.info("You can now run comprehensive search tests with real data.")
    else:
        logger.error("\n💥 Production ingestion failed!")
        logger.error("Please check logs and fix issues before proceeding.")

    exit(0 if success else 1)
