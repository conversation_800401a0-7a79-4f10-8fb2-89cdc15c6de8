#!/usr/bin/env python3
"""
Django Shell Script: Ingest All Slack Data (Past 2 Years)

Run this in Django shell:
python manage.py shell < scripts/ingest_all_slack_data.py

Or copy-paste the content into Django shell.
"""

import os
import logging
from datetime import datetime, timedelta
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def ingest_all_slack_data():
    """Ingest all available Slack data from the past 2 years."""
    
    print("🚀 Starting Complete Slack Data Ingestion (Past 2 Years)")
    print("=" * 60)
    
    try:
        # Import required models and services
        from apps.accounts.models import Tenant
        from apps.documents.models import DocumentSource
        from apps.documents.services.ingestion import IngestionService
        from django.contrib.auth.models import User
        
        # Get production tenant
        tenant = Tenant.objects.get(slug='stride')
        print(f"✅ Using tenant: {tenant.name}")
        
        # Get or create Slack source
        source, created = DocumentSource.objects.get_or_create(
            name='slack_production_engineering',
            source_type='local_slack',
            tenant=tenant,
            defaults={
                'config': {
                    'data_dir': '/Users/<USER>/Desktop/RAGSearch/data',
                    'use_local_files': True,
                    'channels': ['C065QSSNH8A', 'C07M2CAS79S']  # engineering channels
                }
            }
        )
        print(f"✅ Using source: {source.name}")
        
        # Initialize ingestion service
        ingestion_service = IngestionService(tenant.slug)
        print("✅ Ingestion service initialized")
        
        # Check available data
        data_dir = Path('/Users/<USER>/Desktop/RAGSearch/data')
        if not data_dir.exists():
            print("❌ Data directory not found!")
            return
            
        # Find all channel directories
        channel_dirs = [d for d in data_dir.iterdir() if d.is_dir() and d.name.startswith("channel_")]
        print(f"📁 Found {len(channel_dirs)} channel directories")
        
        total_processed = 0
        total_failed = 0
        
        # Process each channel
        for channel_dir in channel_dirs:
            channel_id = channel_dir.name.replace("channel_", "")
            print(f"\n📋 Processing channel: {channel_id}")
            
            try:
                # Create interface for this channel
                from apps.documents.interfaces.slack.local_slack import LocalSlackSourceInterface
                
                config = {
                    'data_dir': str(data_dir),
                    'channel_id': channel_id,
                    'use_local_files': True
                }
                
                interface = LocalSlackSourceInterface(config)
                
                # Fetch all documents (no limit for complete ingestion)
                print(f"   📥 Fetching documents...")
                documents = interface.fetch_documents()  # No limit = all data
                
                if not documents:
                    print(f"   ⚠️  No documents found for channel {channel_id}")
                    continue
                    
                print(f"   📄 Found {len(documents)} documents")
                
                # Process documents in batches
                batch_size = 50
                channel_processed = 0
                channel_failed = 0
                
                for i in range(0, len(documents), batch_size):
                    batch = documents[i:i + batch_size]
                    print(f"   🔄 Processing batch {i//batch_size + 1} ({len(batch)} docs)")
                    
                    try:
                        results = ingestion_service.process_documents(batch, source)
                        batch_processed = results.get('processed', 0)
                        batch_failed = results.get('failed', 0)
                        
                        channel_processed += batch_processed
                        channel_failed += batch_failed
                        
                        print(f"   ✅ Batch complete: {batch_processed} processed, {batch_failed} failed")
                        
                    except Exception as e:
                        print(f"   ❌ Batch failed: {e}")
                        channel_failed += len(batch)
                
                total_processed += channel_processed
                total_failed += channel_failed
                
                print(f"   📊 Channel {channel_id} complete: {channel_processed} processed, {channel_failed} failed")
                
            except Exception as e:
                print(f"   ❌ Channel {channel_id} failed: {e}")
                continue
        
        # Final summary
        print(f"\n🎯 COMPLETE INGESTION RESULTS:")
        print(f"   ✅ Total documents processed: {total_processed}")
        print(f"   ❌ Total documents failed: {total_failed}")
        print(f"   📊 Success rate: {(total_processed/(total_processed+total_failed)*100):.1f}%" if (total_processed+total_failed) > 0 else "N/A")
        
        # Validate final state
        from apps.documents.models import RawDocument, DocumentChunk, EmbeddingMetadata
        
        total_docs = RawDocument.objects.filter(source__tenant=tenant).count()
        total_chunks = DocumentChunk.objects.filter(document__source__tenant=tenant).count()
        total_embeddings = EmbeddingMetadata.objects.filter(chunk__document__source__tenant=tenant).count()
        
        print(f"\n📈 FINAL DATABASE STATE:")
        print(f"   📄 Total documents: {total_docs}")
        print(f"   🧩 Total chunks: {total_chunks}")
        print(f"   🔗 Total embeddings: {total_embeddings}")
        
        print(f"\n🎉 COMPLETE SLACK INGESTION FINISHED!")
        print("✅ System ready for comprehensive RAG search testing")
        
    except Exception as e:
        print(f"❌ Ingestion failed: {e}")
        import traceback
        traceback.print_exc()

# Run the ingestion
if __name__ == "__main__":
    ingest_all_slack_data()
else:
    # When run in Django shell, execute automatically
    ingest_all_slack_data()
