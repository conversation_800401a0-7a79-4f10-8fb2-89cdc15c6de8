#!/usr/bin/env python3
"""
Debug Search Response Script

Diagnoses why search responses are empty by testing each component in the pipeline.
"""

import os
import sys
import time
import json
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Setup Django
multi_source_rag_path = project_root / "multi_source_rag"
sys.path.insert(0, str(multi_source_rag_path))

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
import django
django.setup()

def test_llm_service_directly():
    """Test LLM service directly."""
    print("🔍 Testing LLM Service Directly")
    print("-" * 40)
    
    try:
        import requests
        
        # Test Ollama directly
        response = requests.post(
            "http://localhost:11434/api/generate",
            json={
                "model": "llama3",
                "prompt": "What is authentication? Please provide a brief explanation.",
                "stream": False
            },
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            llm_response = result.get('response', '')
            print(f"✅ LLM Response Length: {len(llm_response)}")
            print(f"✅ LLM Response Preview: {llm_response[:200]}...")
            return len(llm_response) > 0
        else:
            print(f"❌ LLM Service Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ LLM Service Test Failed: {e}")
        return False

def test_vector_search():
    """Test vector search functionality."""
    print("\n🔍 Testing Vector Search")
    print("-" * 40)
    
    try:
        from apps.core.llama_index_manager import LlamaIndexManager
        
        manager = LlamaIndexManager("stride")
        
        # Get index directly
        index = manager._get_or_create_index("slack")
        print(f"✅ Index created: {type(index)}")
        
        # Test retriever
        retriever = index.as_retriever(similarity_top_k=5)
        nodes = retriever.retrieve("What is authentication?")
        
        print(f"✅ Retrieved Nodes: {len(nodes)}")
        for i, node in enumerate(nodes[:3]):
            print(f"  Node {i+1}: {node.text[:100]}...")
            print(f"  Score: {getattr(node, 'score', 'N/A')}")
        
        return len(nodes) > 0
        
    except Exception as e:
        print(f"❌ Vector Search Test Failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_query_engine():
    """Test query engine functionality."""
    print("\n🔍 Testing Query Engine")
    print("-" * 40)
    
    try:
        from apps.core.llama_index_manager import LlamaIndexManager
        
        manager = LlamaIndexManager("stride")
        
        # Get query engine
        engine = manager.get_query_engine(intent="slack")
        print(f"✅ Query Engine Created: {type(engine)}")
        
        # Test query
        test_query = "What is authentication?"
        print(f"📋 Testing Query: {test_query}")
        
        response = engine.query(test_query)
        print(f"✅ Response Type: {type(response)}")
        print(f"✅ Response String: '{str(response)}'")
        print(f"✅ Response Length: {len(str(response))}")
        
        if hasattr(response, 'source_nodes'):
            print(f"✅ Source Nodes: {len(response.source_nodes)}")
        
        if hasattr(response, 'metadata'):
            print(f"✅ Metadata: {response.metadata}")
        
        return len(str(response)) > 0
        
    except Exception as e:
        print(f"❌ Query Engine Test Failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_llama_index_manager_search():
    """Test LlamaIndex manager search method."""
    print("\n🔍 Testing LlamaIndex Manager Search")
    print("-" * 40)
    
    try:
        from apps.core.llama_index_manager import LlamaIndexManager
        
        manager = LlamaIndexManager("stride")
        
        test_query = "What is authentication?"
        print(f"📋 Testing Query: {test_query}")
        
        result = manager.search(query=test_query, intent="slack")
        
        print(f"✅ Result Type: {type(result)}")
        print(f"✅ Result Keys: {list(result.keys())}")
        
        answer = result.get('answer', '')
        print(f"✅ Answer: '{answer}'")
        print(f"✅ Answer Length: {len(answer)}")
        
        source_nodes = result.get('source_nodes', [])
        print(f"✅ Source Nodes: {len(source_nodes)}")
        
        return len(answer) > 0
        
    except Exception as e:
        print(f"❌ LlamaIndex Manager Test Failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_rag_search_service():
    """Test RAG search service."""
    print("\n🔍 Testing RAG Search Service")
    print("-" * 40)
    
    try:
        from apps.search.services.rag_search_service import RAGSearchService
        
        service = RAGSearchService("stride")
        
        test_query = "What is authentication?"
        print(f"📋 Testing Query: {test_query}")
        
        result = service.search(query=test_query, intent="slack")
        
        print(f"✅ Result Type: {type(result)}")
        print(f"✅ Result Keys: {list(result.keys())}")
        
        answer = result.get('answer', '')
        response = result.get('response', '')  # Check both keys
        
        print(f"✅ Answer: '{answer}'")
        print(f"✅ Response: '{response}'")
        print(f"✅ Answer Length: {len(answer)}")
        print(f"✅ Response Length: {len(response)}")
        
        sources = result.get('sources', [])
        print(f"✅ Sources: {len(sources)}")
        
        return len(answer) > 0 or len(response) > 0
        
    except Exception as e:
        print(f"❌ RAG Search Service Test Failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_comprehensive_debug():
    """Run comprehensive debugging."""
    print("🚀 Comprehensive Search Response Debugging")
    print("=" * 60)
    
    tests = [
        ("LLM Service Direct", test_llm_service_directly),
        ("Vector Search", test_vector_search),
        ("Query Engine", test_query_engine),
        ("LlamaIndex Manager", test_llama_index_manager_search),
        ("RAG Search Service", test_rag_search_service),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results[test_name] = result
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"\n{test_name}: {status}")
        except Exception as e:
            results[test_name] = False
            print(f"\n{test_name}: ❌ ERROR - {e}")
    
    # Summary
    print("\n" + "=" * 60)
    print("🎯 DEBUG SUMMARY")
    print("=" * 60)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    passed = sum(results.values())
    total = len(results)
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All components working - issue might be in integration")
    else:
        print(f"\n🔧 {total - passed} components need fixing")
    
    return results

if __name__ == "__main__":
    results = run_comprehensive_debug()
    
    # Save debug results
    debug_file = project_root / f"debug_search_response_{int(time.time())}.json"
    with open(debug_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n📄 Debug results saved to: {debug_file}")
