#!/usr/bin/env python3
"""
Production-Grade Comprehensive RAG System Test

This script tests the complete RAG system exactly as it operates in production:
1. Real Slack data ingestion using production services
2. Complete advanced RAG pipeline validation
3. Wide range of query complexities from simple to highly complex
4. End-to-end search functionality testing
5. Quality validation of all responses
6. Performance and accuracy metrics

Tests the actual production services without mocks or shortcuts.
"""

import os
import sys
import logging
import traceback
import json
import time
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Setup comprehensive logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(project_root / 'production_rag_test.log')
    ]
)
logger = logging.getLogger(__name__)

def setup_django():
    """Setup Django environment for production testing."""
    try:
        # Add the multi_source_rag directory to Python path
        multi_source_rag_path = project_root / "multi_source_rag"
        sys.path.insert(0, str(multi_source_rag_path))

        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
        import django
        django.setup()
        logger.info("✅ Django setup complete")
        return True
    except Exception as e:
        logger.error(f"❌ Django setup failed: {e}")
        logger.error(f"Error details: {traceback.format_exc()}")
        return False

def get_test_queries():
    """Get comprehensive test queries covering all complexity levels."""
    return {
        "simple_factual": [
            "What is authentication?",
            "Who worked on the API?",
            "When was the last deployment?",
            "What is OAuth?",
            "How do we handle errors?"
        ],
        "moderate_analytical": [
            "How does our authentication system work?",
            "What are the main challenges with the current API implementation?",
            "Explain the rate limiting strategy we use",
            "What security measures are implemented in our system?",
            "How do we handle database connections?"
        ],
        "complex_technical": [
            "Compare our OAuth 2.0 implementation with industry best practices and identify potential improvements",
            "Analyze the performance bottlenecks in our current microservices architecture and suggest optimizations",
            "What are the trade-offs between our current caching strategy and alternative approaches?",
            "How would you implement distributed tracing across our entire system architecture?",
            "Evaluate our current CI/CD pipeline and recommend enhancements for better reliability"
        ],
        "highly_complex_strategic": [
            "Design a comprehensive disaster recovery plan for our distributed system considering all failure modes",
            "Propose a migration strategy from our current monolithic components to a fully microservices architecture",
            "How would you implement end-to-end encryption while maintaining system performance and debugging capabilities?",
            "Create a scalability roadmap for handling 10x current traffic with minimal infrastructure changes",
            "Design a multi-tenant architecture that ensures data isolation while optimizing resource utilization"
        ],
        "cross_domain": [
            "How do our Slack discussions about API changes relate to actual GitHub code implementations?",
            "What engineering decisions discussed in Slack have been implemented in our codebase?",
            "Trace the evolution of our authentication system from initial Slack discussions to current implementation",
            "How do product requirements discussed in Slack translate to technical implementation decisions?",
            "What patterns emerge when comparing engineering discussions with actual code changes?"
        ]
    }

def validate_search_response_quality(query: str, response: Dict, complexity_level: str) -> Dict:
    """Validate the quality of search responses based on complexity level."""
    quality_checks = {
        "has_response": bool(response.get('response')),
        "has_sources": bool(response.get('source_nodes', [])),
        "response_not_empty": len(response.get('response', '').strip()) > 0,
        "sources_not_empty": len(response.get('source_nodes', [])) > 0,
        "response_length_appropriate": False,
        "sources_relevant": False,
        "technical_accuracy": False,
        "completeness": False,
        "citations_present": False
    }

    response_text = response.get('response', '')
    source_nodes = response.get('source_nodes', [])

    # Length appropriateness based on complexity
    min_lengths = {
        "simple_factual": 50,
        "moderate_analytical": 150,
        "complex_technical": 300,
        "highly_complex_strategic": 500,
        "cross_domain": 400
    }

    min_length = min_lengths.get(complexity_level, 100)
    quality_checks["response_length_appropriate"] = len(response_text) >= min_length

    # Check for citations
    quality_checks["citations_present"] = any(
        marker in response_text.lower()
        for marker in ['source:', 'according to', 'mentioned in', 'discussed in', 'from the']
    )

    # Source relevance (basic check)
    if source_nodes:
        quality_checks["sources_relevant"] = len(source_nodes) >= 2

        # Check if sources contain relevant content
        source_texts = [node.get('text', '') for node in source_nodes]
        query_words = set(query.lower().split())

        relevant_sources = 0
        for source_text in source_texts:
            source_words = set(source_text.lower().split())
            overlap = len(query_words.intersection(source_words))
            if overlap >= 2:  # At least 2 words overlap
                relevant_sources += 1

        quality_checks["sources_relevant"] = relevant_sources >= 1

    # Technical accuracy (basic heuristics)
    technical_indicators = [
        'api', 'authentication', 'oauth', 'jwt', 'database', 'microservices',
        'architecture', 'security', 'performance', 'scalability', 'implementation'
    ]

    if any(term in query.lower() for term in technical_indicators):
        quality_checks["technical_accuracy"] = any(
            term in response_text.lower() for term in technical_indicators
        )
    else:
        quality_checks["technical_accuracy"] = True  # Not a technical query

    # Completeness (addresses the query)
    query_keywords = [word for word in query.lower().split() if len(word) > 3]
    addressed_keywords = sum(
        1 for keyword in query_keywords
        if keyword in response_text.lower()
    )

    if query_keywords:
        quality_checks["completeness"] = (addressed_keywords / len(query_keywords)) >= 0.5
    else:
        quality_checks["completeness"] = True

    # Calculate overall quality score
    passed_checks = sum(quality_checks.values())
    total_checks = len(quality_checks)
    quality_score = passed_checks / total_checks

    return {
        "quality_score": quality_score,
        "passed_checks": passed_checks,
        "total_checks": total_checks,
        "checks": quality_checks,
        "response_length": len(response_text),
        "source_count": len(source_nodes),
        "query": query,
        "complexity_level": complexity_level
    }

def test_production_slack_ingestion():
    """Test production Slack ingestion pipeline."""
    logger.info("🔍 Testing Production Slack Ingestion...")

    try:
        from apps.documents.interfaces.slack.local_slack import LocalSlackSourceInterface
        from apps.documents.services.ingestion import IngestionService
        from apps.documents.models import DocumentSource, RawDocument, DocumentChunk
        from apps.accounts.models import Tenant
        from django.contrib.auth.models import User

        # Get or create production tenant
        user, _ = User.objects.get_or_create(
            username='<EMAIL>',
            defaults={'email': '<EMAIL>'}
        )
        tenant, _ = Tenant.objects.get_or_create(
            slug='stride',
            defaults={'name': 'Stride HR', 'owner': user}
        )

        # Create production document source
        source, _ = DocumentSource.objects.get_or_create(
            name='slack_production_test',
            source_type='slack',
            tenant=tenant,
            defaults={'config': {
                "channel_id": "C065QSSNH8A",
                "channel_name": "1-productengineering"
            }}
        )

        # Test with real Slack data
        config = {
            "data_dir": "/Users/<USER>/Desktop/RAGSearch/data",
            "channel_id": "C065QSSNH8A",
            "use_local_files": True
        }

        interface = LocalSlackSourceInterface(config)
        documents = interface.fetch_documents(limit=10)

        if not documents:
            logger.warning("⚠️ No Slack documents found - skipping ingestion test")
            return True, {"documents": 0, "processed": 0, "chunks": 0}

        # Use production ingestion service
        ingestion_service = IngestionService(tenant.slug)

        processed_count = 0
        total_chunks = 0

        for i, document in enumerate(documents[:5]):  # Test with first 5 documents
            try:
                logger.info(f"Processing document {i+1}/5: {document.get('id', 'unknown')}")

                # Process using production service
                raw_doc, chunks = ingestion_service.process_document(document, source)

                chunk_count = len(chunks)
                total_chunks += chunk_count
                processed_count += 1

                logger.info(f"✅ Document processed: {chunk_count} chunks created")

            except Exception as e:
                logger.error(f"❌ Failed to process document {i+1}: {e}")
                continue

        # Verify in database
        total_docs = RawDocument.objects.filter(source=source).count()
        total_db_chunks = DocumentChunk.objects.filter(document__source=source).count()

        logger.info(f"✅ Production ingestion completed:")
        logger.info(f"   - Documents processed: {processed_count}")
        logger.info(f"   - Chunks created: {total_chunks}")
        logger.info(f"   - Total docs in DB: {total_docs}")
        logger.info(f"   - Total chunks in DB: {total_db_chunks}")

        return True, {
            "documents": len(documents),
            "processed": processed_count,
            "chunks": total_chunks,
            "db_docs": total_docs,
            "db_chunks": total_db_chunks
        }

    except Exception as e:
        logger.error(f"❌ Production Slack ingestion test failed: {e}")
        logger.error(f"Error details: {traceback.format_exc()}")
        return False, {}

def test_production_search_comprehensive():
    """Test production search with comprehensive query complexity."""
    logger.info("🔍 Testing Production Search Comprehensively...")

    try:
        from apps.search.services.rag_search_service import RAGSearchService

        # Initialize production search service
        search_service = RAGSearchService(tenant_slug='stride')

        test_queries = get_test_queries()
        all_results = {}
        overall_quality_scores = {}

        for complexity_level, queries in test_queries.items():
            logger.info(f"\n📋 Testing {complexity_level.upper()} queries...")

            level_results = []
            level_quality_scores = []

            for i, query in enumerate(queries):
                logger.info(f"Query {i+1}/{len(queries)}: {query[:60]}...")

                start_time = time.time()

                try:
                    # Use production search service
                    response = search_service.search(
                        query=query,
                        limit=10,
                        intent="slack"
                    )

                    search_time = time.time() - start_time

                    # Validate response quality
                    quality_result = validate_search_response_quality(
                        query, response, complexity_level
                    )

                    level_results.append({
                        "query": query,
                        "response": response,
                        "quality": quality_result,
                        "search_time": search_time
                    })

                    level_quality_scores.append(quality_result["quality_score"])

                    logger.info(f"✅ Quality Score: {quality_result['quality_score']:.2f} "
                               f"({quality_result['passed_checks']}/{quality_result['total_checks']} checks) "
                               f"Time: {search_time:.2f}s")

                except Exception as e:
                    logger.error(f"❌ Search failed for query: {e}")
                    level_results.append({
                        "query": query,
                        "response": None,
                        "quality": {"quality_score": 0.0, "error": str(e)},
                        "search_time": None
                    })
                    level_quality_scores.append(0.0)

            # Calculate level statistics
            avg_quality = sum(level_quality_scores) / len(level_quality_scores) if level_quality_scores else 0.0
            overall_quality_scores[complexity_level] = avg_quality
            all_results[complexity_level] = level_results

            logger.info(f"✅ {complexity_level.upper()} Average Quality: {avg_quality:.2f}")

        # Calculate overall system quality
        overall_avg_quality = sum(overall_quality_scores.values()) / len(overall_quality_scores)

        logger.info(f"\n🎯 OVERALL SEARCH QUALITY: {overall_avg_quality:.2f}")

        # Quality thresholds by complexity
        quality_thresholds = {
            "simple_factual": 0.8,
            "moderate_analytical": 0.7,
            "complex_technical": 0.6,
            "highly_complex_strategic": 0.5,
            "cross_domain": 0.6
        }

        passed_levels = 0
        total_levels = len(quality_thresholds)

        for level, threshold in quality_thresholds.items():
            actual_quality = overall_quality_scores.get(level, 0.0)
            if actual_quality >= threshold:
                passed_levels += 1
                logger.info(f"✅ {level}: {actual_quality:.2f} >= {threshold} (PASSED)")
            else:
                logger.error(f"❌ {level}: {actual_quality:.2f} < {threshold} (FAILED)")

        success = passed_levels == total_levels

        return success, {
            "overall_quality": overall_avg_quality,
            "level_qualities": overall_quality_scores,
            "passed_levels": passed_levels,
            "total_levels": total_levels,
            "detailed_results": all_results
        }

    except Exception as e:
        logger.error(f"❌ Production search comprehensive test failed: {e}")
        logger.error(f"Error details: {traceback.format_exc()}")
        return False, {}


def test_advanced_rag_features():
    """Test advanced RAG features in production environment."""
    logger.info("🔍 Testing Advanced RAG Features...")

    try:
        from apps.core.llama_index_manager import LlamaIndexManager
        from apps.core.retrieval import SophisticationLevel

        # Initialize production LlamaIndex manager
        manager = LlamaIndexManager("stride")

        # Test different sophistication levels
        test_query = "How does our authentication system handle OAuth 2.0 and JWT tokens?"

        sophistication_tests = [
            (SophisticationLevel.BASIC, "Basic"),
            (SophisticationLevel.MODERATE, "Moderate"),
            (SophisticationLevel.ADVANCED, "Advanced"),
            (SophisticationLevel.ULTIMATE, "Ultimate")
        ]

        results = {}

        for level, level_name in sophistication_tests:
            logger.info(f"Testing {level_name} sophistication...")

            try:
                start_time = time.time()

                # Test agentic search with different sophistication levels
                response = manager.agentic_search(
                    test_query,
                    intent="slack",
                    sophistication=level
                )

                search_time = time.time() - start_time

                # Validate response
                quality = validate_search_response_quality(
                    test_query, response, "complex_technical"
                )

                results[level_name] = {
                    "response": response,
                    "quality": quality,
                    "search_time": search_time
                }

                logger.info(f"✅ {level_name}: Quality {quality['quality_score']:.2f}, "
                           f"Time: {search_time:.2f}s")

            except Exception as e:
                logger.error(f"❌ {level_name} sophistication failed: {e}")
                results[level_name] = {"error": str(e)}

        # Test cross-domain search
        try:
            logger.info("Testing cross-domain search...")

            cross_domain_response = manager.cross_domain_search(
                "How do Slack discussions about API changes relate to GitHub implementations?",
                intent="slack",
                sophistication=SophisticationLevel.ADVANCED
            )

            cross_domain_quality = validate_search_response_quality(
                "cross-domain query", cross_domain_response, "cross_domain"
            )

            results["cross_domain"] = {
                "response": cross_domain_response,
                "quality": cross_domain_quality
            }

            logger.info(f"✅ Cross-domain: Quality {cross_domain_quality['quality_score']:.2f}")

        except Exception as e:
            logger.error(f"❌ Cross-domain search failed: {e}")
            results["cross_domain"] = {"error": str(e)}

        # Calculate success rate
        successful_tests = sum(1 for result in results.values() if "error" not in result)
        total_tests = len(results)
        success_rate = successful_tests / total_tests

        logger.info(f"✅ Advanced RAG Features Success Rate: {success_rate:.2f} ({successful_tests}/{total_tests})")

        return success_rate >= 0.75, {
            "success_rate": success_rate,
            "successful_tests": successful_tests,
            "total_tests": total_tests,
            "results": results
        }

    except Exception as e:
        logger.error(f"❌ Advanced RAG features test failed: {e}")
        logger.error(f"Error details: {traceback.format_exc()}")
        return False, {}


def run_production_comprehensive_test():
    """Run complete production-grade comprehensive test."""
    logger.info("🚀 Starting Production-Grade Comprehensive RAG System Test")
    logger.info("=" * 80)

    if not setup_django():
        logger.error("❌ Cannot proceed without Django setup")
        return False

    tests = [
        ("Production Slack Ingestion", test_production_slack_ingestion),
        ("Production Search Comprehensive", test_production_search_comprehensive),
        ("Advanced RAG Features", test_advanced_rag_features),
    ]

    passed = 0
    total = len(tests)
    results = {}
    test_summary = {}

    for test_name, test_func in tests:
        logger.info(f"\n📋 {test_name}")
        logger.info("-" * 60)

        try:
            result = test_func()
            if isinstance(result, tuple):
                success, data = result
                results[test_name] = data
                test_summary[test_name] = "PASSED" if success else "FAILED"
            else:
                success = result
                test_summary[test_name] = "PASSED" if success else "FAILED"

            if success:
                passed += 1
                logger.info(f"✅ {test_name}: PASSED")
            else:
                logger.error(f"❌ {test_name}: FAILED")

        except Exception as e:
            logger.error(f"❌ {test_name}: ERROR - {e}")
            test_summary[test_name] = "ERROR"

    logger.info("\n" + "=" * 80)
    logger.info(f"🎯 PRODUCTION TEST SUMMARY: {passed}/{total} tests passed")
    logger.info("\n📊 Detailed Results:")

    for test_name, status in test_summary.items():
        status_icon = "✅" if status == "PASSED" else "❌" if status == "FAILED" else "⚠️"
        logger.info(f"  {status_icon} {test_name}: {status}")

    if passed == total:
        logger.info("\n🎉 ALL PRODUCTION TESTS PASSED!")
        logger.info("✅ RAG System is production-ready with comprehensive functionality")
        return True
    else:
        logger.error(f"\n❌ {total - passed} PRODUCTION TESTS FAILED")
        logger.info("🔧 System requires fixes before full production deployment")
        return False


if __name__ == "__main__":
    success = run_production_comprehensive_test()
    exit(0 if success else 1)
