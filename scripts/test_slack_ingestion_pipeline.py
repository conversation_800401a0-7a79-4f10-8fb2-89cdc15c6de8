#!/usr/bin/env python3
"""
Comprehensive Slack Ingestion Pipeline Test with Advanced RAG Features

This script tests the complete Slack data ingestion pipeline to ensure:
1. Full compatibility with advanced RAG features (domain classification, complexity analysis, file processing)
2. Data integrity and quality throughout the process
3. Proper integration with embedding systems and vector storage
4. Memory management and caching functionality
5. Token-based chunking with conversation awareness
6. Thread processing and metadata enhancement
7. Enhanced ingestion service with domain metadata
8. Cross-domain intelligence and routing capabilities
9. File-level retrieval support
10. Ultimate agentic search compatibility

Run this script to validate the entire Slack ingestion system with advanced RAG features.
"""

import os
import sys
import json
import logging
import traceback
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Setup comprehensive logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(project_root / 'slack_ingestion_test.log')
    ]
)
logger = logging.getLogger(__name__)

def setup_django():
    """Setup Django environment for testing."""
    try:
        # Add the multi_source_rag directory to Python path
        multi_source_rag_path = project_root / "multi_source_rag"
        sys.path.insert(0, str(multi_source_rag_path))

        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
        import django
        django.setup()
        logger.info("✅ Django setup complete")
        return True
    except Exception as e:
        logger.error(f"❌ Django setup failed: {e}")
        return False

def test_advanced_rag_services():
    """Test advanced RAG services initialization."""
    logger.info("🔍 Testing Advanced RAG Services...")

    try:
        # Test Domain Classification Service
        from apps.core.services.domain_classification import DomainClassificationService, DataDomain
        domain_classifier = DomainClassificationService()
        logger.info("✅ Domain Classification Service initialized")

        # Test Complexity Analysis Engine
        from apps.core.services.complexity_analysis import ComplexityAnalysisEngine
        complexity_analyzer = ComplexityAnalysisEngine()
        logger.info("✅ Complexity Analysis Engine initialized")

        # Test File Content Processor
        from apps.core.services.file_content_processor import FileContentProcessor
        file_processor = FileContentProcessor()
        logger.info("✅ File Content Processor initialized")

        # Test Enhanced Ingestion Service (now consolidated into main service)
        from apps.documents.services.ingestion import IngestionService
        enhanced_service = IngestionService("test-tenant")
        logger.info("✅ Enhanced Ingestion Service initialized")

        # Test data domains
        available_domains = list(DataDomain)
        logger.info(f"✅ Available data domains: {[d.value for d in available_domains]}")

        return True, {
            "domain_classifier": domain_classifier,
            "complexity_analyzer": complexity_analyzer,
            "file_processor": file_processor,
            "enhanced_service": enhanced_service,
            "available_domains": available_domains
        }

    except Exception as e:
        logger.error(f"❌ Advanced RAG services initialization failed: {e}")
        logger.error(f"Error details: {traceback.format_exc()}")
        return False, {}


def test_slack_interface_creation():
    """Test creation of Slack interfaces."""
    logger.info("🔍 Testing Slack Interface Creation...")

    try:
        # Test local file-based Slack interface
        from apps.documents.interfaces.slack.local_slack import LocalSlackSourceInterface

        local_config = {
            "data_dir": "/Users/<USER>/Desktop/RAGSearch/data",
            "channel_id": "C065QSSNH8A",
            "use_local_files": True
        }

        local_interface = LocalSlackSourceInterface(local_config)
        logger.info("✅ Local file-based Slack interface created successfully")

        # Test interface validation
        if hasattr(local_interface, 'validate_configuration'):
            is_valid = local_interface.validate_configuration()
            logger.info(f"✅ Interface configuration validation: {'PASSED' if is_valid else 'FAILED'}")

        return True, {"local_interface": local_interface}

    except Exception as e:
        logger.error(f"❌ Interface creation failed: {e}")
        logger.error(f"Error details: {traceback.format_exc()}")
        return False, {}

def test_token_estimation():
    """Test token estimation functionality."""
    logger.info("🔍 Testing Token Estimation...")

    try:
        from apps.documents.interfaces.slack.slack import TokenEstimator

        estimator = TokenEstimator()

        # Test cases
        test_cases = [
            ("Simple message", "Hello world"),
            ("Message with URL", "Check this out: https://example.com"),
            ("Message with mention", "Hey <@U1234567890> how are you?"),
            ("Message with emoji", "Great work! :thumbsup: :rocket:"),
            ("Code block", "Here's the code:\n```python\nprint('hello')\n```"),
            ("Complex message", "Hey <@U123> check https://example.com :rocket: ```code``` #channel"),
        ]

        for description, text in test_cases:
            tokens = estimator.estimate_tokens(text)
            logger.info(f"  {description}: '{text}' -> {tokens} tokens")

        logger.info("✅ Token estimation working correctly")
        return True

    except Exception as e:
        logger.error(f"❌ Token estimation failed: {e}")
        return False

def test_slack_message_validation():
    """Test SlackMessage data class validation."""
    logger.info("🔍 Testing SlackMessage Validation...")

    try:
        from apps.documents.interfaces.slack.slack import SlackMessage

        # Valid message
        valid_msg = SlackMessage(
            text="Test message",
            user="U1234567890",
            timestamp="1640995200.123456",
            channel="C1234567890",
            user_name="Test User"
        )

        assert valid_msg.validate(), "Valid message should pass validation"
        assert valid_msg.readable_timestamp, "Should have readable timestamp"
        assert not valid_msg.is_thread_parent, "Should not be thread parent"
        assert not valid_msg.is_thread_reply, "Should not be thread reply"

        # Thread parent message
        thread_parent = SlackMessage(
            text="Thread starter",
            user="U1234567890",
            timestamp="1640995200.123456",
            channel="C1234567890",
            user_name="Test User",
            reply_count=3
        )

        assert thread_parent.is_thread_parent, "Should be thread parent"
        assert not thread_parent.is_thread_reply, "Should not be thread reply"

        # Thread reply message
        thread_reply = SlackMessage(
            text="Thread reply",
            user="U1234567890",
            timestamp="1640995260.123456",
            thread_ts="1640995200.123456",
            channel="C1234567890",
            user_name="Test User"
        )

        assert not thread_reply.is_thread_parent, "Should not be thread parent"
        assert thread_reply.is_thread_reply, "Should be thread reply"

        logger.info("✅ SlackMessage validation working correctly")
        return True

    except Exception as e:
        logger.error(f"❌ SlackMessage validation failed: {e}")
        return False

def test_memory_management():
    """Test memory management and caching."""
    logger.info("🔍 Testing Memory Management...")

    try:
        from apps.core.utils.memory_manager import memory_manager, CacheConfig

        # Create test cache
        cache_config = CacheConfig(max_size=100, ttl_seconds=60)
        test_cache = memory_manager.create_cache("test_slack_cache", cache_config)

        # Test cache operations
        test_cache.set("user_123", "Test User")
        assert test_cache.get("user_123") == "Test User", "Cache should return stored value"

        # Test cache stats
        stats = test_cache.get_stats()
        assert "hits" in stats, "Cache should provide statistics"
        assert "misses" in stats, "Cache should track misses"

        # Cleanup
        test_cache.clear()

        logger.info("✅ Memory management working correctly")
        return True

    except Exception as e:
        logger.error(f"❌ Memory management failed: {e}")
        return False

def test_local_slack_data_processing():
    """Test local Slack data processing if data exists."""
    logger.info("🔍 Testing Local Slack Data Processing...")

    try:
        data_dir = Path("/Users/<USER>/Desktop/RAGSearch/data")
        if not data_dir.exists():
            logger.warning("⚠️ No local data directory found, skipping local data test")
            return True

        # Find channel directories
        channel_dirs = [d for d in data_dir.iterdir() if d.is_dir() and d.name.startswith("channel_")]

        if not channel_dirs:
            logger.warning("⚠️ No channel directories found, skipping local data test")
            return True

        # Test with first available channel
        channel_dir = channel_dirs[0]
        channel_id = channel_dir.name.replace("channel_", "")

        logger.info(f"Testing with channel: {channel_id}")

        from apps.documents.interfaces.factory import DocumentSourceFactory

        config = {
            "data_dir": str(data_dir),
            "channel_id": channel_id,
            "use_local_files": True
        }

        interface = DocumentSourceFactory.create_interface("local_slack", config)

        # Test document fetching
        documents = interface.fetch_documents(limit=10)

        if documents:
            logger.info(f"✅ Successfully processed {len(documents)} documents from local data")

            # Validate document structure
            sample_doc = documents[0]
            required_fields = ["content", "metadata", "id"]
            for field in required_fields:
                assert field in sample_doc, f"Document should have {field} field"

            logger.info("✅ Document structure validation passed")
        else:
            logger.warning("⚠️ No documents found in local data")

        return True

    except Exception as e:
        logger.error(f"❌ Local data processing failed: {e}")
        return False

def test_embedding_integration():
    """Test integration with embedding system."""
    logger.info("🔍 Testing Embedding Integration...")

    try:
        from apps.core.utils.embedding_config import get_embedding_info, validate_system_embedding_consistency

        # Check embedding configuration
        embedding_info = get_embedding_info()
        logger.info(f"Embedding model: {embedding_info.get('model_name')}")
        logger.info(f"Dimensions: {embedding_info.get('dimensions')}")

        # Validate consistency
        is_consistent = validate_system_embedding_consistency()
        assert is_consistent, "Embedding system should be consistent"

        logger.info("✅ Embedding integration working correctly")
        return True

    except Exception as e:
        logger.error(f"❌ Embedding integration failed: {e}")
        return False

def test_domain_classification():
    """Test domain classification functionality."""
    logger.info("🔍 Testing Domain Classification...")

    try:
        from apps.core.services.domain_classification import DomainClassificationService, DataDomain
        from apps.documents.models import RawDocument, DocumentSource
        from apps.accounts.models import Tenant
        from django.contrib.auth.models import User

        # Get or create test tenant and user
        user, _ = User.objects.get_or_create(username='test_user', defaults={'email': '<EMAIL>'})
        tenant, _ = Tenant.objects.get_or_create(slug='test-tenant', defaults={'name': 'Test Tenant', 'owner': user})

        # Create test document source
        source, _ = DocumentSource.objects.get_or_create(
            name='test_slack_source',
            source_type='slack',
            tenant=tenant,
            defaults={'config': {}}
        )

        # Create test document
        test_doc = RawDocument.objects.create(
            title="Engineering Discussion",
            content="Let's discuss the API implementation. We need to handle authentication and rate limiting.",
            content_type="text/slack",
            source=source,
            metadata={
                "channel_name": "engineering-team",
                "user_name": "developer",
                "has_code": True,
                "technical_terms": ["API", "authentication", "rate limiting"]
            }
        )

        # Test domain classification
        classifier = DomainClassificationService()
        classification = classifier.classify_document(test_doc)

        # Validate classification
        assert classification.primary_domain in [DataDomain.SLACK_ENGINEERING, DataDomain.SLACK_CONVERSATIONS]
        assert 0.0 <= classification.confidence <= 1.0
        assert isinstance(classification.secondary_domains, list)
        assert isinstance(classification.routing_metadata, dict)

        logger.info(f"✅ Domain classification: {classification.primary_domain.value} (confidence: {classification.confidence:.2f})")
        logger.info(f"✅ Secondary domains: {[d.value for d in classification.secondary_domains]}")

        # Cleanup
        test_doc.delete()

        return True, {"classification": classification}

    except Exception as e:
        logger.error(f"❌ Domain classification test failed: {e}")
        logger.error(f"Error details: {traceback.format_exc()}")
        return False, {}


def test_complexity_analysis():
    """Test complexity analysis functionality."""
    logger.info("🔍 Testing Complexity Analysis...")

    try:
        from apps.core.services.complexity_analysis import ComplexityAnalysisEngine
        from apps.documents.models import RawDocument, DocumentSource
        from apps.accounts.models import Tenant
        from django.contrib.auth.models import User

        # Get or create test tenant and user
        user, _ = User.objects.get_or_create(username='test_user', defaults={'email': '<EMAIL>'})
        tenant, _ = Tenant.objects.get_or_create(slug='test-tenant', defaults={'name': 'Test Tenant', 'owner': user})

        # Create test document source
        source, _ = DocumentSource.objects.get_or_create(
            name='test_slack_source',
            source_type='slack',
            tenant=tenant,
            defaults={'config': {}}
        )

        # Create test document with complex content
        complex_content = """
        This is a complex technical discussion about implementing a distributed system.
        We need to consider microservices architecture, database sharding, caching strategies,
        load balancing, and fault tolerance. The system should handle high throughput
        and provide real-time analytics with machine learning capabilities.
        """

        test_doc = RawDocument.objects.create(
            title="Complex Technical Discussion",
            content=complex_content,
            content_type="text/slack",
            source=source,
            metadata={
                "channel_name": "architecture",
                "participant_count": 5,
                "thread_count": 3,
                "technical_terms": ["microservices", "database", "machine learning"]
            }
        )

        # Test complexity analysis
        analyzer = ComplexityAnalysisEngine()
        analysis = analyzer.analyze_document_complexity(test_doc)

        # Validate analysis
        assert 0.0 <= analysis.semantic_complexity <= 1.0
        assert 0.0 <= analysis.domain_complexity <= 1.0
        assert 0.0 <= analysis.temporal_complexity <= 1.0
        assert 0.0 <= analysis.structural_complexity <= 1.0
        assert 0.0 <= analysis.contextual_complexity <= 1.0
        assert analysis.overall_level in ["SIMPLE", "MODERATE", "COMPLEX", "ADAPTIVE"]
        assert isinstance(analysis.strategy_recommendations, dict)
        assert isinstance(analysis.performance_hints, dict)

        logger.info(f"✅ Complexity analysis: {analysis.overall_level}")
        logger.info(f"✅ Semantic: {analysis.semantic_complexity:.2f}, Domain: {analysis.domain_complexity:.2f}")
        logger.info(f"✅ Strategy recommendations: {list(analysis.strategy_recommendations.keys())}")

        # Cleanup
        test_doc.delete()

        return True, {"analysis": analysis}

    except Exception as e:
        logger.error(f"❌ Complexity analysis test failed: {e}")
        logger.error(f"Error details: {traceback.format_exc()}")
        return False, {}


def test_file_content_processing():
    """Test file content processing functionality."""
    logger.info("🔍 Testing File Content Processing...")

    try:
        from apps.core.services.file_content_processor import FileContentProcessor
        from apps.documents.models import RawDocument, DocumentSource
        from apps.accounts.models import Tenant
        from django.contrib.auth.models import User

        # Get or create test tenant and user
        user, _ = User.objects.get_or_create(username='test_user', defaults={'email': '<EMAIL>'})
        tenant, _ = Tenant.objects.get_or_create(slug='test-tenant', defaults={'name': 'Test Tenant', 'owner': user})

        # Create test document source
        source, _ = DocumentSource.objects.get_or_create(
            name='test_slack_source',
            source_type='slack',
            tenant=tenant,
            defaults={'config': {}}
        )

        # Create test document with file-like content
        file_content = """
        # Slack Engineering Discussion

        This conversation covers the implementation of a new authentication system.
        Key topics discussed:
        - OAuth 2.0 implementation
        - JWT token management
        - Rate limiting strategies
        - Security best practices

        Participants: @john.doe, @jane.smith, @tech.lead
        """

        test_doc = RawDocument.objects.create(
            title="Authentication System Discussion",
            content=file_content,
            content_type="text/slack",
            source=source,
            metadata={
                "channel_name": "engineering",
                "file_path": "slack/engineering/auth_discussion.md",
                "participants": ["john.doe", "jane.smith", "tech.lead"]
            }
        )

        # Test file processing
        processor = FileContentProcessor()
        result = processor.process_for_file_retrieval(test_doc)

        # Validate processing result
        assert isinstance(result.file_summary, str)
        assert isinstance(result.file_keywords, list)
        assert 0.0 <= result.file_complexity_score <= 1.0
        assert isinstance(result.file_metadata, dict)
        assert isinstance(result.is_complete_file, bool)

        logger.info(f"✅ File processing: Complete file: {result.is_complete_file}")
        logger.info(f"✅ Keywords: {result.file_keywords[:5]}")  # Show first 5 keywords
        logger.info(f"✅ Complexity score: {result.file_complexity_score:.2f}")
        logger.info(f"✅ File metadata keys: {list(result.file_metadata.keys())}")

        # Cleanup
        test_doc.delete()

        return True, {"result": result}

    except Exception as e:
        logger.error(f"❌ File content processing test failed: {e}")
        logger.error(f"Error details: {traceback.format_exc()}")
        return False, {}


def test_rag_compatibility():
    """Test compatibility with RAG search system."""
    logger.info("🔍 Testing RAG System Compatibility...")

    try:
        # Test document format compatibility
        sample_document = {
            "id": "slack_msg_1640995200.123456",
            "content": "[2022-01-01 12:00:00] Test User: This is a test message with some content for RAG processing.",
            "metadata": {
                "source": "slack",
                "channel_id": "C1234567890",
                "channel_name": "general",
                "user_id": "U1234567890",
                "user_name": "Test User",
                "timestamp": "1640995200.123456",
                "message_type": "message",
                "thread_ts": None,
                "reply_count": 0,
                "reactions": [],
                "files": [],
                # Advanced RAG metadata
                "data_domain": "slack_conversations",
                "domain_confidence": 0.85,
                "complexity_level": "MODERATE",
                "retrieval_strategy_hints": {
                    "optimal_for_chunks": True,
                    "optimal_for_file_content": False,
                    "optimal_for_metadata": False
                }
            }
        }

        # Validate required fields for RAG
        required_fields = ["id", "content", "metadata"]
        for field in required_fields:
            assert field in sample_document, f"Document must have {field} for RAG compatibility"

        # Validate metadata structure
        metadata = sample_document["metadata"]
        required_metadata = ["source", "timestamp", "user_name"]
        for field in required_metadata:
            assert field in metadata, f"Metadata must have {field} for RAG compatibility"

        # Validate advanced RAG metadata
        advanced_metadata = ["data_domain", "domain_confidence", "complexity_level", "retrieval_strategy_hints"]
        for field in advanced_metadata:
            assert field in metadata, f"Advanced RAG metadata must have {field}"

        logger.info("✅ RAG system compatibility validated")
        logger.info("✅ Advanced RAG metadata structure validated")
        return True

    except Exception as e:
        logger.error(f"❌ RAG compatibility test failed: {e}")
        return False

def test_enhanced_ingestion_pipeline():
    """Test the enhanced ingestion pipeline with real Slack data."""
    logger.info("🔍 Testing Enhanced Ingestion Pipeline...")

    try:
        from apps.core.services.enhanced_ingestion_service import EnhancedUnifiedIngestionService
        from apps.documents.models import DocumentSource
        from apps.accounts.models import Tenant
        from django.contrib.auth.models import User

        # Get or create test tenant and user
        user, _ = User.objects.get_or_create(username='test_user', defaults={'email': '<EMAIL>'})
        tenant, _ = Tenant.objects.get_or_create(slug='test-tenant', defaults={'name': 'Test Tenant', 'owner': user})

        # Create test document source
        source, _ = DocumentSource.objects.get_or_create(
            name='test_slack_enhanced',
            source_type='slack',
            tenant=tenant,
            defaults={'config': {}}
        )

        # Create enhanced ingestion service
        enhanced_service = EnhancedUnifiedIngestionService(tenant.slug)

        # Test document data with Slack-like structure
        test_document = {
            "id": "slack_enhanced_test_001",
            "title": "Engineering Team Discussion",
            "content": """
            [2024-01-15 10:30:00] john.doe: We need to implement the new authentication system
            [2024-01-15 10:31:15] jane.smith: I suggest using OAuth 2.0 with JWT tokens
            [2024-01-15 10:32:30] tech.lead: Good idea. We should also consider rate limiting
            [2024-01-15 10:33:45] john.doe: What about security best practices?
            [2024-01-15 10:35:00] jane.smith: Let's follow OWASP guidelines
            """,
            "metadata": {
                "source": "slack",
                "channel_id": "C065QSSNH8A",
                "channel_name": "engineering-team",
                "participants": ["john.doe", "jane.smith", "tech.lead"],
                "message_count": 5,
                "thread_count": 1,
                "has_code": False,
                "technical_terms": ["OAuth", "JWT", "authentication", "rate limiting", "OWASP"],
                "timestamp": "2024-01-15T10:30:00Z"
            }
        }

        # Process document through enhanced pipeline
        result = enhanced_service.process_document_with_enhancements(test_document, source)

        # Validate enhanced processing result
        assert result.document is not None
        assert len(result.chunks) > 0
        assert result.domain_metadata is not None
        assert result.complexity_profile is not None
        assert result.file_processing_result is not None
        assert isinstance(result.processing_stats, dict)

        logger.info(f"✅ Enhanced ingestion: Document processed with {len(result.chunks)} chunks")
        logger.info(f"✅ Domain: {result.domain_metadata.primary_domain} (confidence: {result.domain_metadata.confidence:.2f})")
        logger.info(f"✅ Complexity: {result.complexity_profile.overall_level}")
        logger.info(f"✅ Processing stats: {result.processing_stats}")

        # Cleanup
        result.document.delete()

        return True, {"result": result}

    except Exception as e:
        logger.error(f"❌ Enhanced ingestion pipeline test failed: {e}")
        logger.error(f"Error details: {traceback.format_exc()}")
        return False, {}


def test_vector_storage_integration():
    """Test integration with vector storage and embeddings."""
    logger.info("🔍 Testing Vector Storage Integration...")

    try:
        from apps.core.utils.vectorstore import get_qdrant_client
        from apps.core.utils.embedding_config import get_embedding_info, validate_system_embedding_consistency
        from apps.core.utils.collection_manager import get_collection_name

        # Test Qdrant connection
        client = get_qdrant_client()
        logger.info("✅ Qdrant client connection established")

        # Test embedding configuration
        embedding_info = get_embedding_info()
        logger.info(f"✅ Embedding model: {embedding_info.get('model_name')}")
        logger.info(f"✅ Dimensions: {embedding_info.get('dimensions')}")

        # Validate system consistency
        is_consistent = validate_system_embedding_consistency()
        assert is_consistent, "Embedding system should be consistent"
        logger.info("✅ Embedding system consistency validated")

        # Test collection naming
        test_collection = get_collection_name("test-tenant", intent="slack")
        logger.info(f"✅ Collection name generated: {test_collection}")

        # Test collection existence (should exist if data has been ingested)
        collections = client.get_collections()
        collection_names = [col.name for col in collections.collections]
        logger.info(f"✅ Available collections: {collection_names}")

        return True, {
            "client": client,
            "embedding_info": embedding_info,
            "collections": collection_names
        }

    except Exception as e:
        logger.error(f"❌ Vector storage integration test failed: {e}")
        logger.error(f"Error details: {traceback.format_exc()}")
        return False, {}


def test_llama_index_manager_integration():
    """Test integration with LlamaIndex manager and advanced search."""
    logger.info("🔍 Testing LlamaIndex Manager Integration...")

    try:
        from apps.core.llama_index_manager import LlamaIndexManager
        from apps.core.retrieval import SophisticationLevel, RetrievalMode

        # Create LlamaIndex manager
        manager = LlamaIndexManager("test-tenant")
        logger.info("✅ LlamaIndex manager created")

        # Test basic search functionality
        test_query = "authentication system implementation"

        try:
            # Test basic search
            basic_results = manager.search(test_query, intent="slack")
            logger.info(f"✅ Basic search completed: {len(basic_results.get('source_nodes', []))} results")
        except Exception as e:
            logger.warning(f"⚠️ Basic search failed (expected if no data): {e}")

        try:
            # Test agentic search
            agentic_results = manager.agentic_search(
                test_query,
                intent="slack",
                sophistication=SophisticationLevel.MODERATE
            )
            logger.info(f"✅ Agentic search completed: {len(agentic_results.get('source_nodes', []))} results")
        except Exception as e:
            logger.warning(f"⚠️ Agentic search failed (expected if no data): {e}")

        try:
            # Test cross-domain search
            cross_domain_results = manager.cross_domain_search(
                test_query,
                intent="slack",
                sophistication=SophisticationLevel.MODERATE
            )
            logger.info(f"✅ Cross-domain search completed: {len(cross_domain_results.get('source_nodes', []))} results")
        except Exception as e:
            logger.warning(f"⚠️ Cross-domain search failed (expected if no data): {e}")

        # Test configuration methods
        config = manager.get_retrieval_config("slack", SophisticationLevel.MODERATE)
        logger.info(f"✅ Retrieval configuration: {list(config.keys())}")

        sophistication_levels = manager.get_available_sophistication_levels()
        logger.info(f"✅ Available sophistication levels: {len(sophistication_levels)}")

        # Cleanup
        manager.cleanup_resources()
        logger.info("✅ Manager resources cleaned up")

        return True, {
            "manager": manager,
            "config": config,
            "sophistication_levels": sophistication_levels
        }

    except Exception as e:
        logger.error(f"❌ LlamaIndex manager integration test failed: {e}")
        logger.error(f"Error details: {traceback.format_exc()}")
        return False, {}


def run_comprehensive_test():
    """Run all tests and provide summary."""
    logger.info("🚀 Starting Comprehensive Slack Ingestion Pipeline Test with Advanced RAG Features")
    logger.info("=" * 80)

    # Setup Django
    if not setup_django():
        logger.error("❌ Cannot proceed without Django setup")
        return False

    tests = [
        ("Advanced RAG Services", test_advanced_rag_services),
        ("Slack Interface Creation", test_slack_interface_creation),
        ("Domain Classification", test_domain_classification),
        ("Complexity Analysis", test_complexity_analysis),
        ("File Content Processing", test_file_content_processing),
        ("Enhanced Ingestion Pipeline", test_enhanced_ingestion_pipeline),
        ("Vector Storage Integration", test_vector_storage_integration),
        ("LlamaIndex Manager Integration", test_llama_index_manager_integration),
        ("Token Estimation", test_token_estimation),
        ("Message Validation", test_slack_message_validation),
        ("Memory Management", test_memory_management),
        ("Local Data Processing", test_local_slack_data_processing),
        ("Embedding Integration", test_embedding_integration),
        ("RAG Compatibility", test_rag_compatibility),
    ]

    passed = 0
    total = len(tests)
    results = {}
    test_summary = {}

    for test_name, test_func in tests:
        logger.info(f"\n📋 {test_name}")
        logger.info("-" * 50)
        try:
            result = test_func()
            if isinstance(result, tuple):
                success, data = result
                results[test_name] = data
                test_summary[test_name] = "PASSED" if success else "FAILED"
            else:
                success = result
                test_summary[test_name] = "PASSED" if success else "FAILED"

            if success:
                passed += 1
                logger.info(f"✅ {test_name}: PASSED")
            else:
                logger.error(f"❌ {test_name}: FAILED")
        except Exception as e:
            logger.error(f"❌ {test_name}: ERROR - {e}")
            test_summary[test_name] = "ERROR"

    logger.info("\n" + "=" * 80)
    logger.info(f"🎯 TEST SUMMARY: {passed}/{total} tests passed")
    logger.info("\n📊 Detailed Results:")

    for test_name, status in test_summary.items():
        status_icon = "✅" if status == "PASSED" else "❌" if status == "FAILED" else "⚠️"
        logger.info(f"  {status_icon} {test_name}: {status}")

    if passed == total:
        logger.info("\n🎉 ALL TESTS PASSED - SLACK INGESTION PIPELINE WITH ADVANCED RAG FEATURES VALIDATED!")
        logger.info("✅ System is ready for production Slack data ingestion with:")
        logger.info("   • Domain classification and routing")
        logger.info("   • Complexity analysis and strategy selection")
        logger.info("   • File-level retrieval capabilities")
        logger.info("   • Cross-domain intelligence")
        logger.info("   • Ultimate agentic search")
        logger.info("   • Enhanced metadata and performance optimization")
        return True
    else:
        logger.error(f"\n❌ {total - passed} TESTS FAILED - Review issues above")
        logger.info("🔧 System may have partial functionality but requires fixes for full production readiness")
        return False

if __name__ == "__main__":
    success = run_comprehensive_test()
    exit(0 if success else 1)
