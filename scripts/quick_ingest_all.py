#!/usr/bin/env python3
"""
Quick Django Shell One-Liner for Complete Slack Ingestion

Copy and paste this into Django shell:
python manage.py shell

Then paste this code:
"""

# === COPY FROM HERE ===
from apps.accounts.models import Tenant; from apps.documents.models import DocumentSource; from apps.documents.services.ingestion import IngestionService; from apps.documents.interfaces.slack.local_slack import LocalSlackSourceInterface; from pathlib import Path; import logging; logging.basicConfig(level=logging.INFO); tenant = Tenant.objects.get(slug='stride'); source, _ = DocumentSource.objects.get_or_create(name='slack_production_engineering', source_type='local_slack', tenant=tenant, defaults={'config': {'data_dir': '/Users/<USER>/Desktop/RAGSearch/data', 'use_local_files': True}}); ingestion_service = IngestionService(tenant.slug); data_dir = Path('/Users/<USER>/Desktop/RAGSearch/data'); channel_dirs = [d for d in data_dir.iterdir() if d.is_dir() and d.name.startswith("channel_")]; print(f"🚀 Processing {len(channel_dirs)} channels..."); total_processed = 0; [print(f"📋 Channel: {(channel_id := channel_dir.name.replace('channel_', ''))}") or (lambda: (interface := LocalSlackSourceInterface({'data_dir': str(data_dir), 'channel_id': channel_id, 'use_local_files': True}), documents := interface.fetch_documents(), print(f"📄 Found {len(documents)} documents") if documents else print("⚠️ No documents"), results := ingestion_service.process_documents(documents, source) if documents else {'processed': 0, 'failed': 0}, globals().update(total_processed=total_processed + results.get('processed', 0)), print(f"✅ Processed: {results.get('processed', 0)}, Failed: {results.get('failed', 0)}")))() for channel_dir in channel_dirs]; print(f"🎉 COMPLETE! Total processed: {total_processed}")
# === COPY TO HERE ===
