#!/usr/bin/env python3
"""
Comprehensive RAG System Integrity Test

This script validates all database tables and advanced RAG features are properly
populated and functioning according to recent improvements.
"""

import os
import sys
import logging
from datetime import datetime

# Setup Django
sys.path.insert(0, 'multi_source_rag')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
import django
django.setup()

from apps.accounts.models import Tenant
from apps.documents.models import (
    DocumentSource, RawDocument, DocumentContent, DocumentChunk,
    EmbeddingMetadata, DocumentDomainMetadata, DocumentComplexityProfile
)

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_comprehensive_integrity_test():
    """Run comprehensive integrity tests for all RAG components."""

    print("🔍 COMPREHENSIVE RAG SYSTEM INTEGRITY TEST")
    print("=" * 60)

    try:
        # Get production tenant
        tenant = Tenant.objects.get(slug='stride')
        print(f"✅ Using tenant: {tenant.name}")

        # Test 1: Basic Data Integrity
        print(f"\n📊 TEST 1: BASIC DATA INTEGRITY")
        print("-" * 40)

        sources = DocumentSource.objects.filter(tenant=tenant)
        raw_docs = RawDocument.objects.filter(source__tenant=tenant)
        doc_contents = DocumentContent.objects.filter(document__source__tenant=tenant)
        chunks = DocumentChunk.objects.filter(document__source__tenant=tenant)
        embeddings = EmbeddingMetadata.objects.filter(chunk__document__source__tenant=tenant)

        print(f"📁 Document Sources: {sources.count()}")
        print(f"📄 Raw Documents: {raw_docs.count()}")
        print(f"📝 Document Contents: {doc_contents.count()}")
        print(f"🧩 Document Chunks: {chunks.count()}")
        print(f"🔗 Embeddings: {embeddings.count()}")

        # Validate 1:1:1:1 ratio for skip-chunking
        if raw_docs.count() == chunks.count() == embeddings.count():
            print("✅ Perfect 1:1:1 ratio (docs:chunks:embeddings) - Skip-chunking working correctly")
        else:
            print("❌ Data ratio mismatch - Potential integrity issue")
            return False

        # Test 2: Advanced RAG Metadata
        print(f"\n🧠 TEST 2: ADVANCED RAG METADATA")
        print("-" * 40)

        domain_metadata = DocumentDomainMetadata.objects.filter(tenant=tenant)
        complexity_profiles = DocumentComplexityProfile.objects.filter(tenant=tenant)

        print(f"🎯 Domain Metadata Records: {domain_metadata.count()}")
        print(f"📈 Complexity Profiles: {complexity_profiles.count()}")

        # Check coverage
        domain_coverage = (domain_metadata.count() / raw_docs.count() * 100) if raw_docs.count() > 0 else 0
        complexity_coverage = (complexity_profiles.count() / raw_docs.count() * 100) if raw_docs.count() > 0 else 0

        print(f"📊 Domain metadata coverage: {domain_coverage:.1f}%")
        print(f"📊 Complexity profile coverage: {complexity_coverage:.1f}%")

        if domain_coverage >= 95 and complexity_coverage >= 95:
            print("✅ Excellent advanced metadata coverage")
        elif domain_coverage >= 80 and complexity_coverage >= 80:
            print("⚠️  Good metadata coverage but could be improved")
        else:
            print("❌ Poor metadata coverage - Advanced RAG features may not work optimally")

        # Test 3: Domain Classification Analysis
        print(f"\n🎯 TEST 3: DOMAIN CLASSIFICATION ANALYSIS")
        print("-" * 40)

        if domain_metadata.exists():
            # Analyze domain distribution
            domain_distribution = {}
            for dm in domain_metadata:
                domain = dm.primary_domain
                domain_distribution[domain] = domain_distribution.get(domain, 0) + 1

            print("📋 Domain Distribution:")
            for domain, count in sorted(domain_distribution.items(), key=lambda x: x[1], reverse=True):
                percentage = (count / domain_metadata.count() * 100)
                print(f"  • {domain}: {count} documents ({percentage:.1f}%)")

            # Check domain confidence
            avg_confidence = sum(dm.domain_confidence for dm in domain_metadata) / domain_metadata.count()
            print(f"📊 Average domain confidence: {avg_confidence:.3f}")

            if avg_confidence >= 0.8:
                print("✅ High domain classification confidence")
            elif avg_confidence >= 0.6:
                print("⚠️  Moderate domain classification confidence")
            else:
                print("❌ Low domain classification confidence")

            # Check routing metadata
            routing_metadata_count = sum(1 for dm in domain_metadata if dm.routing_metadata)
            routing_coverage = (routing_metadata_count / domain_metadata.count() * 100)
            print(f"📊 Routing metadata coverage: {routing_coverage:.1f}%")

        else:
            print("❌ No domain metadata found")

        # Test 4: Complexity Analysis
        print(f"\n📈 TEST 4: COMPLEXITY ANALYSIS")
        print("-" * 40)

        if complexity_profiles.exists():
            # Analyze complexity distribution
            complexity_levels = {}
            complexity_scores = []

            for cp in complexity_profiles:
                level = cp.overall_complexity_level
                complexity_levels[level] = complexity_levels.get(level, 0) + 1
                complexity_scores.append(cp.overall_complexity_score)

            print("📋 Complexity Level Distribution:")
            for level, count in sorted(complexity_levels.items(), key=lambda x: x[1], reverse=True):
                percentage = (count / complexity_profiles.count() * 100)
                print(f"  • {level}: {count} documents ({percentage:.1f}%)")

            # Calculate average complexity
            avg_complexity = sum(complexity_scores) / len(complexity_scores)
            print(f"📊 Average complexity score: {avg_complexity:.3f}")

            # Check performance hints and strategy recommendations
            hints_count = sum(1 for cp in complexity_profiles if cp.performance_hints)
            strategies_count = sum(1 for cp in complexity_profiles if cp.strategy_recommendations)

            hints_coverage = (hints_count / complexity_profiles.count() * 100)
            strategies_coverage = (strategies_count / complexity_profiles.count() * 100)

            print(f"📊 Performance hints coverage: {hints_coverage:.1f}%")
            print(f"📊 Strategy recommendations coverage: {strategies_coverage:.1f}%")

            if hints_coverage >= 95 and strategies_coverage >= 95:
                print("✅ Excellent complexity analysis coverage")
            else:
                print("⚠️  Complexity analysis could be improved")

        else:
            print("❌ No complexity profiles found")

        # Test 5: Embedding Quality and Consistency
        print(f"\n🔗 TEST 5: EMBEDDING QUALITY AND CONSISTENCY")
        print("-" * 40)

        if embeddings.exists():
            # Check embedding dimensions from model metadata
            sample_embedding = embeddings.first()
            if sample_embedding:
                embedding_dim = sample_embedding.vector_dimensions
                model_name = sample_embedding.model_name
                print(f"📐 Embedding dimensions: {embedding_dim}d")
                print(f"🤖 Embedding model: {model_name}")

                if embedding_dim == 768:
                    print("✅ Correct embedding dimensions for BAAI/bge-base-en-v1.5")
                else:
                    print(f"⚠️  Unexpected embedding dimensions (expected 768d)")

                if "bge-base-en-v1.5" in model_name:
                    print("✅ Correct embedding model")
                else:
                    print(f"⚠️  Unexpected embedding model")

            # Check for missing vector_ids
            missing_vector_ids = embeddings.filter(vector_id__isnull=True).count()
            missing_percentage = (missing_vector_ids / embeddings.count() * 100)
            print(f"📊 Missing vector IDs: {missing_vector_ids} ({missing_percentage:.1f}%)")

            if missing_percentage == 0:
                print("✅ All embeddings have vector IDs - Perfect embedding coverage")
            elif missing_percentage < 5:
                print("⚠️  Some missing vector IDs but acceptable")
            else:
                print("❌ Too many missing vector IDs - Embedding process may have issues")

            # Check sync status
            synced_embeddings = embeddings.filter(is_synced=True).count()
            sync_percentage = (synced_embeddings / embeddings.count() * 100)
            print(f"📊 Synced embeddings: {synced_embeddings} ({sync_percentage:.1f}%)")

            if sync_percentage >= 95:
                print("✅ Excellent embedding sync status")
            elif sync_percentage >= 80:
                print("⚠️  Good embedding sync status")
            else:
                print("❌ Poor embedding sync status - Vector store may be out of sync")

        else:
            print("❌ No embeddings found")

        # Test 6: Content Quality and Processing
        print(f"\n📝 TEST 6: CONTENT QUALITY AND PROCESSING")
        print("-" * 40)

        if raw_docs.exists():
            # Check content sizes
            content_sizes = []
            empty_content = 0

            for doc in raw_docs:
                try:
                    content = doc.documentcontent.content if hasattr(doc, 'documentcontent') else ""
                    content_size = len(content) if content else 0
                    content_sizes.append(content_size)
                    if content_size == 0:
                        empty_content += 1
                except:
                    empty_content += 1
                    content_sizes.append(0)

            avg_content_size = sum(content_sizes) / len(content_sizes) if content_sizes else 0
            min_content_size = min(content_sizes) if content_sizes else 0
            max_content_size = max(content_sizes) if content_sizes else 0

            print(f"📊 Average content size: {avg_content_size:.0f} characters")
            print(f"📊 Content size range: {min_content_size} - {max_content_size} characters")
            print(f"📊 Empty content documents: {empty_content}")

            if empty_content == 0:
                print("✅ No empty content documents")
            elif empty_content < raw_docs.count() * 0.05:
                print("⚠️  Some empty content but acceptable")
            else:
                print("❌ Too many empty content documents")

            # Check for token-based chunking metadata
            token_based_count = 0
            for chunk in chunks:
                if chunk.metadata and chunk.metadata.get('chunking_strategy') == 'token_based_500':
                    token_based_count += 1

            token_based_percentage = (token_based_count / chunks.count() * 100) if chunks.count() > 0 else 0
            print(f"📊 Token-based chunks: {token_based_count} ({token_based_percentage:.1f}%)")

            if token_based_percentage >= 95:
                print("✅ Excellent token-based chunking coverage")
            else:
                print("⚠️  Mixed chunking strategies detected")

        # Test 7: Recent Data Validation
        print(f"\n⏰ TEST 7: RECENT DATA VALIDATION")
        print("-" * 40)

        recent_docs = raw_docs.order_by('-created_at')[:10]
        print(f"📋 Most recent documents:")
        for i, doc in enumerate(recent_docs, 1):
            print(f"  {i}. {doc.external_id} ({doc.created_at.strftime('%Y-%m-%d %H:%M:%S')})")

        # Check if ingestion is recent
        latest_doc = raw_docs.order_by('-created_at').first()
        if latest_doc:
            time_since_latest = datetime.now(latest_doc.created_at.tzinfo) - latest_doc.created_at
            hours_since = time_since_latest.total_seconds() / 3600

            if hours_since < 1:
                print("✅ Very recent ingestion (< 1 hour ago)")
            elif hours_since < 24:
                print("✅ Recent ingestion (< 24 hours ago)")
            else:
                print("⚠️  Ingestion is older than 24 hours")

        # Final Summary
        print(f"\n🎯 FINAL INTEGRITY SUMMARY")
        print("=" * 60)

        total_tests = 7
        passed_tests = 0

        # Count passed tests based on criteria
        if raw_docs.count() == chunks.count() == embeddings.count():
            passed_tests += 1
        if domain_coverage >= 80 and complexity_coverage >= 80:
            passed_tests += 1
        if domain_metadata.exists():
            try:
                avg_confidence = sum(dm.domain_confidence for dm in domain_metadata) / domain_metadata.count()
                if avg_confidence >= 0.6:
                    passed_tests += 1
            except:
                pass
        if complexity_profiles.exists():
            passed_tests += 1
        if embeddings.exists():
            try:
                missing_vector_ids = embeddings.filter(vector_id__isnull=True).count()
                missing_percentage = (missing_vector_ids / embeddings.count() * 100)
                if missing_percentage < 5:
                    passed_tests += 1
            except:
                pass
        if raw_docs.exists():
            try:
                empty_content = 0
                for doc in raw_docs:
                    try:
                        content = doc.documentcontent.content if hasattr(doc, 'documentcontent') else ""
                        if not content:
                            empty_content += 1
                    except:
                        empty_content += 1
                if empty_content < raw_docs.count() * 0.05:
                    passed_tests += 1
            except:
                pass
        if latest_doc:
            try:
                time_since_latest = datetime.now(latest_doc.created_at.tzinfo) - latest_doc.created_at
                hours_since = time_since_latest.total_seconds() / 3600
                if hours_since < 24:
                    passed_tests += 1
            except:
                pass

        success_rate = (passed_tests / total_tests * 100)

        print(f"📊 Tests Passed: {passed_tests}/{total_tests}")
        print(f"📊 Success Rate: {success_rate:.1f}%")

        if success_rate >= 90:
            print("🎉 EXCELLENT - System is production-ready for comprehensive RAG testing!")
        elif success_rate >= 75:
            print("✅ GOOD - System is ready for RAG testing with minor improvements needed")
        elif success_rate >= 60:
            print("⚠️  FAIR - System needs improvements before comprehensive testing")
        else:
            print("❌ POOR - System requires significant fixes before testing")

        return success_rate >= 75

    except Exception as e:
        print(f"❌ Integrity test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = run_comprehensive_integrity_test()
    sys.exit(0 if success else 1)
