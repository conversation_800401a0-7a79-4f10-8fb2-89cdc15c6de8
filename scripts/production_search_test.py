#!/usr/bin/env python3
"""
Production RAG Search Test

This script tests the complete RAG search system exactly as it operates in production
with real ingested Slack data. Tests all advanced RAG features with production services.

Features:
- Tests production RAGSearchService with real data
- Validates all sophistication levels (Basic, Moderate, Advanced, Ultimate)
- Tests agentic search with intelligent routing
- Tests cross-domain search capabilities
- Production-grade error handling and comprehensive metrics
"""

import os
import sys
import logging
import traceback
import time
from pathlib import Path
from typing import Dict, Any, List

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Setup production logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(project_root / 'production_search_test.log')
    ]
)
logger = logging.getLogger(__name__)

def setup_django():
    """Setup Django environment for production testing."""
    try:
        # Add the multi_source_rag directory to Python path
        multi_source_rag_path = project_root / "multi_source_rag"
        sys.path.insert(0, str(multi_source_rag_path))

        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
        import django
        django.setup()
        logger.info("✅ Django setup complete")
        return True
    except Exception as e:
        logger.error(f"❌ Django setup failed: {e}")
        logger.error(f"Error details: {traceback.format_exc()}")
        return False

def get_production_test_queries():
    """Get production test queries for comprehensive validation."""
    return {
        "basic_queries": [
            "What is authentication?",
            "How do we handle API requests?",
            "What are the main features?",
            "Who worked on this project?",
            "When was this implemented?"
        ],
        "moderate_queries": [
            "How does our authentication system work?",
            "What are the security considerations for our API?",
            "Explain the architecture of our system",
            "What challenges did we face during development?",
            "How do we ensure data consistency?"
        ],
        "advanced_queries": [
            "Compare our current authentication approach with industry best practices",
            "Analyze the trade-offs in our API design decisions",
            "What are the scalability implications of our current architecture?",
            "How would you improve our error handling strategy?",
            "Evaluate the performance characteristics of our system"
        ],
        "ultimate_queries": [
            "Design a comprehensive security audit plan for our entire system",
            "Propose a migration strategy to improve our current architecture",
            "How would you implement distributed tracing across all services?",
            "Create a disaster recovery plan considering all failure modes",
            "Design a multi-tenant architecture while maintaining performance"
        ]
    }

def test_basic_search():
    """Test basic RAG search functionality."""
    logger.info("🔍 Testing Basic RAG Search...")
    
    try:
        from apps.search.services.rag_search_service import RAGSearchService
        
        # Initialize production search service
        search_service = RAGSearchService(tenant_slug='stride')
        
        test_queries = get_production_test_queries()["basic_queries"]
        results = []
        
        for i, query in enumerate(test_queries):
            logger.info(f"Query {i+1}/{len(test_queries)}: {query}")
            
            start_time = time.time()
            
            try:
                # Test basic search
                response = search_service.search(
                    query=query,
                    limit=5,
                    intent="slack"
                )
                
                search_time = time.time() - start_time
                
                # Validate response
                has_response = bool(response.get('response'))
                has_sources = len(response.get('source_nodes', [])) > 0
                response_length = len(response.get('response', ''))
                
                result = {
                    "query": query,
                    "success": has_response,
                    "response_length": response_length,
                    "source_count": len(response.get('source_nodes', [])),
                    "search_time": search_time,
                    "response_preview": response.get('response', '')[:100] + "..." if response.get('response') else "No response"
                }
                
                results.append(result)
                
                logger.info(f"✅ Success: {has_response}, Sources: {len(response.get('source_nodes', []))}, Time: {search_time:.2f}s")
                
            except Exception as e:
                logger.error(f"❌ Query failed: {e}")
                results.append({
                    "query": query,
                    "success": False,
                    "error": str(e),
                    "search_time": time.time() - start_time
                })
        
        # Calculate success rate
        successful_queries = sum(1 for r in results if r.get('success', False))
        success_rate = successful_queries / len(results)
        
        logger.info(f"✅ Basic Search Results: {successful_queries}/{len(results)} successful ({success_rate:.2%})")
        
        return success_rate >= 0.8, {
            "success_rate": success_rate,
            "successful_queries": successful_queries,
            "total_queries": len(results),
            "results": results
        }
        
    except Exception as e:
        logger.error(f"❌ Basic search test failed: {e}")
        logger.error(f"Error details: {traceback.format_exc()}")
        return False, {"error": str(e)}

def test_advanced_rag_search():
    """Test advanced RAG search with sophistication levels."""
    logger.info("🔍 Testing Advanced RAG Search...")
    
    try:
        from apps.core.llama_index_manager import LlamaIndexManager
        from apps.core.retrieval import SophisticationLevel
        
        # Initialize production LlamaIndex manager
        manager = LlamaIndexManager("stride")
        
        # Test different sophistication levels
        test_query = "How does our authentication system work and what are the security implications?"
        
        sophistication_tests = [
            (SophisticationLevel.BASIC, "Basic"),
            (SophisticationLevel.MODERATE, "Moderate"),
            (SophisticationLevel.ADVANCED, "Advanced"),
            (SophisticationLevel.ULTIMATE, "Ultimate")
        ]
        
        results = {}
        
        for level, level_name in sophistication_tests:
            logger.info(f"Testing {level_name} sophistication...")
            
            try:
                start_time = time.time()
                
                # Test agentic search with sophistication level
                response = manager.agentic_search(
                    test_query,
                    intent="slack",
                    sophistication=level
                )
                
                search_time = time.time() - start_time
                
                # Validate response
                has_response = bool(response.get('answer'))
                source_count = len(response.get('source_nodes', []))
                response_length = len(response.get('answer', ''))
                
                results[level_name] = {
                    "success": has_response,
                    "response_length": response_length,
                    "source_count": source_count,
                    "search_time": search_time,
                    "agentic_info": response.get('agentic_info', {}),
                    "metadata": response.get('metadata', {})
                }
                
                logger.info(f"✅ {level_name}: Success={has_response}, Sources={source_count}, Time={search_time:.2f}s")
                
            except Exception as e:
                logger.error(f"❌ {level_name} sophistication failed: {e}")
                results[level_name] = {"success": False, "error": str(e)}
        
        # Calculate success rate
        successful_tests = sum(1 for result in results.values() if result.get('success', False))
        total_tests = len(results)
        success_rate = successful_tests / total_tests
        
        logger.info(f"✅ Advanced RAG Results: {successful_tests}/{total_tests} successful ({success_rate:.2%})")
        
        return success_rate >= 0.75, {
            "success_rate": success_rate,
            "successful_tests": successful_tests,
            "total_tests": total_tests,
            "results": results
        }
        
    except Exception as e:
        logger.error(f"❌ Advanced RAG search test failed: {e}")
        logger.error(f"Error details: {traceback.format_exc()}")
        return False, {"error": str(e)}

def run_production_search_test():
    """Run comprehensive production search test."""
    logger.info("🚀 Starting Production RAG Search Test")
    logger.info("=" * 80)
    
    if not setup_django():
        logger.error("❌ Cannot proceed without Django setup")
        return False
    
    tests = [
        ("Basic RAG Search", test_basic_search),
        ("Advanced RAG Search", test_advanced_rag_search),
    ]
    
    passed = 0
    total = len(tests)
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 {test_name}")
        logger.info("-" * 60)
        
        try:
            success, data = test_func()
            results[test_name] = data
            
            if success:
                passed += 1
                logger.info(f"✅ {test_name}: PASSED")
            else:
                logger.error(f"❌ {test_name}: FAILED")
                
        except Exception as e:
            logger.error(f"❌ {test_name}: ERROR - {e}")
            results[test_name] = {"error": str(e)}

    logger.info("\n" + "=" * 80)
    logger.info(f"🎯 PRODUCTION SEARCH TEST SUMMARY: {passed}/{total} tests passed")
    
    for test_name, result in results.items():
        if result.get('success_rate'):
            logger.info(f"   📊 {test_name}: {result['success_rate']:.2%} success rate")
        elif 'error' in result:
            logger.info(f"   ❌ {test_name}: ERROR")
        else:
            logger.info(f"   ⚠️  {test_name}: UNKNOWN")

    if passed == total:
        logger.info("\n🎉 ALL PRODUCTION SEARCH TESTS PASSED!")
        logger.info("✅ RAG System is fully operational with real data")
        return True
    else:
        logger.error(f"\n❌ {total - passed} PRODUCTION SEARCH TESTS FAILED")
        logger.info("🔧 System requires attention before full deployment")
        return False

if __name__ == "__main__":
    success = run_production_search_test()
    exit(0 if success else 1)
