#!/usr/bin/env python3
"""
System Reliability Validation Script

This script validates the system reliability improvements implemented:
1. Embedding consistency across all components
2. Resource management functionality
3. Database optimization readiness
4. Code quality improvements

Run this script to verify all improvements are working correctly.
"""

import os
import sys
import importlib.util
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def validate_embedding_config():
    """Validate embedding configuration consistency."""
    print("🔍 Validating Embedding Configuration...")

    try:
        # Import the embedding config module
        spec = importlib.util.spec_from_file_location(
            "embedding_config",
            project_root / "multi_source_rag/apps/core/utils/embedding_config.py"
        )
        embedding_config = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(embedding_config)

        # Test basic configuration
        model_name, dimensions = embedding_config.EmbeddingConfig.get_model_config()

        # Validate production configuration
        expected_model = "BAAI/bge-base-en-v1.5"
        expected_dimensions = 768

        if model_name == expected_model and dimensions == expected_dimensions:
            print(f"✅ Embedding Config: {model_name} ({dimensions}d) - CORRECT")
            return True
        else:
            print(f"❌ Embedding Config: Expected {expected_model} ({expected_dimensions}d), got {model_name} ({dimensions}d)")
            return False

    except Exception as e:
        print(f"❌ Embedding Config Error: {e}")
        return False

def validate_resource_manager():
    """Validate resource management functionality."""
    print("🔍 Validating Resource Management...")

    try:
        # Import the resource manager module
        spec = importlib.util.spec_from_file_location(
            "resource_manager",
            project_root / "multi_source_rag/apps/core/utils/resource_manager.py"
        )
        resource_manager = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(resource_manager)

        # Test resource manager functionality
        manager = resource_manager.ResourceManager("test_component")

        # Test resource registration
        test_resource = "test_resource_string"  # Use hashable type
        manager.register_resource(test_resource)

        if len(manager.resources) == 1:
            print("✅ Resource Manager: Registration working")

            # Test cleanup
            manager.cleanup_resources()
            if len(manager.resources) == 0:
                print("✅ Resource Manager: Cleanup working")
                return True
            else:
                print("❌ Resource Manager: Cleanup failed")
                return False
        else:
            print("❌ Resource Manager: Registration failed")
            return False

    except Exception as e:
        print(f"❌ Resource Manager Error: {e}")
        return False

def validate_file_cleanup():
    """Validate that dead code files have been removed."""
    print("🔍 Validating Dead Code Removal...")

    dead_files = [
        "multi_source_rag/apps/search/chain.py",
        "multi_source_rag/apps/search/engines/conversation_aware_query_engine.py",
        "multi_source_rag/apps/search/retrievers/llamaindex_hybrid_retriever.py",
        "multi_source_rag/apps/core/utils/resource_cleanup.py",
        "multi_source_rag/apps/core/utils/llama_index_setup.py",
    ]

    removed_count = 0
    for file_path in dead_files:
        full_path = project_root / file_path
        if not full_path.exists():
            removed_count += 1
        else:
            print(f"❌ Dead file still exists: {file_path}")

    if removed_count == len(dead_files):
        print(f"✅ Dead Code Removal: {removed_count}/{len(dead_files)} files removed")
        return True
    else:
        print(f"❌ Dead Code Removal: Only {removed_count}/{len(dead_files)} files removed")
        return False

def validate_new_files():
    """Validate that new files have been created."""
    print("🔍 Validating New File Creation...")

    new_files = [
        "multi_source_rag/apps/core/utils/embedding_config.py",
        "multi_source_rag/apps/core/utils/resource_manager.py",
        "multi_source_rag/apps/search/migrations/0010_optimize_database_queries.py",
        "docs/SYSTEM_RELIABILITY_PERFORMANCE_OPTIMIZATION.md",
    ]

    created_count = 0
    for file_path in new_files:
        full_path = project_root / file_path
        if full_path.exists():
            created_count += 1
            print(f"✅ Created: {file_path}")
        else:
            print(f"❌ Missing: {file_path}")

    if created_count == len(new_files):
        print(f"✅ New File Creation: {created_count}/{len(new_files)} files created")
        return True
    else:
        print(f"❌ New File Creation: Only {created_count}/{len(new_files)} files created")
        return False

def validate_database_migration():
    """Validate database migration file."""
    print("🔍 Validating Database Migration...")

    migration_file = project_root / "multi_source_rag/apps/search/migrations/0010_optimize_database_queries.py"

    if migration_file.exists():
        content = migration_file.read_text()

        # Check for key optimization patterns
        required_patterns = [
            "CREATE INDEX CONCURRENTLY",
            "idx_citation_result_rank",
            "idx_search_result_user_timestamp",
            "idx_document_chunk_document_profile",
        ]

        found_patterns = 0
        for pattern in required_patterns:
            if pattern in content:
                found_patterns += 1

        if found_patterns == len(required_patterns):
            print(f"✅ Database Migration: All {found_patterns} optimization patterns found")
            return True
        else:
            print(f"❌ Database Migration: Only {found_patterns}/{len(required_patterns)} patterns found")
            return False
    else:
        print("❌ Database Migration: File not found")
        return False

def main():
    """Run all validation tests."""
    print("🚀 SYSTEM RELIABILITY VALIDATION")
    print("=" * 50)

    tests = [
        ("Embedding Configuration", validate_embedding_config),
        ("Resource Management", validate_resource_manager),
        ("Dead Code Removal", validate_file_cleanup),
        ("New File Creation", validate_new_files),
        ("Database Migration", validate_database_migration),
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        if test_func():
            passed += 1
        print()

    print("=" * 50)
    print(f"🎯 VALIDATION SUMMARY: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 ALL TESTS PASSED - SYSTEM RELIABILITY VALIDATED!")
        print("✅ Production deployment ready")
        return 0
    else:
        print("❌ SOME TESTS FAILED - Review issues above")
        return 1

if __name__ == "__main__":
    exit(main())
