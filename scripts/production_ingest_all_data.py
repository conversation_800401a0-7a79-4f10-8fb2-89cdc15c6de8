#!/usr/bin/env python3
"""
Production Script: Ingest ALL Slack Data (Past 2+ Years)

This script uses the production ingestion service to process all available Slack data
from your local data directory with proper configuration.
"""

import os
import sys
import logging
from pathlib import Path

# Setup Django
sys.path.insert(0, 'multi_source_rag')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
import django
django.setup()

from apps.accounts.models import Tenant
from apps.documents.models import DocumentSource
from apps.documents.services.ingestion import IngestionService
from apps.documents.interfaces.slack.local_slack import LocalSlackSourceInterface

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def ingest_all_slack_data():
    """Production ingestion of all Slack data."""

    print("🚀 PRODUCTION SLACK DATA INGESTION")
    print("=" * 50)

    try:
        # Get production tenant
        tenant = Tenant.objects.get(slug='stride')
        print(f"✅ Using tenant: {tenant.name}")

        # Update source config to fetch ALL data
        source = DocumentSource.objects.get(name='slack_production_engineering', tenant=tenant)

        # Update config to fetch ALL data (not just staged messages)
        source.config.update({
            'data_dir': '/Users/<USER>/Desktop/RAGSearch/data',
            'channel_id': 'C065QSSNH8A',
            'use_local_files': True,
            'time_period': 'custom',  # Use custom time period
            'custom_days': 3650,   # 10 years = all available data
            'include_threads': True,
            'filter_bots': False,  # Include all messages
            'quality_threshold': 0.0,  # Include all quality levels
            'enable_summary': False,
            'use_consolidated': True,  # Use consolidated monthly files
            'limit': None  # No document limit
        })
        source.save()
        print(f"✅ Updated source config to fetch ALL data")

        # Initialize production ingestion service
        ingestion_service = IngestionService(tenant.slug)
        print("✅ Production ingestion service initialized")

        # Test interface directly first to see how much data we have
        print("\n📊 CHECKING AVAILABLE DATA...")
        interface = LocalSlackSourceInterface(source.config)

        # Fetch ALL documents (no limit)
        print("📥 Fetching all available documents...")
        documents = interface.fetch_documents()

        if not documents:
            print("❌ No documents found! Check your data directory.")
            return

        print(f"📄 Found {len(documents)} total documents")

        # Show sample of what we found
        if documents:
            sample_doc = documents[0]
            print(f"📋 Sample document ID: {sample_doc.get('id', 'unknown')}")
            print(f"📋 Sample content length: {len(sample_doc.get('content', ''))}")
            print(f"📋 Sample metadata keys: {list(sample_doc.get('metadata', {}).keys())}")

        # Confirm before processing
        print(f"\n⚠️  About to process {len(documents)} documents.")
        print("This will take some time and use significant resources.")

        # Process using production service
        print(f"\n🔄 STARTING PRODUCTION INGESTION...")
        print(f"Processing {len(documents)} documents in batches of 25...")

        processed, failed = ingestion_service.process_source(
            source=source,
            batch_size=25,  # Smaller batches for stability
        )

        print(f"\n🎉 INGESTION COMPLETE!")
        print(f"✅ Processed: {processed}")
        print(f"❌ Failed: {failed}")
        print(f"📊 Success rate: {(processed/(processed+failed)*100):.1f}%" if (processed+failed) > 0 else "N/A")

        # Final validation
        from apps.documents.models import RawDocument, DocumentChunk, EmbeddingMetadata

        total_docs = RawDocument.objects.filter(source__tenant=tenant).count()
        total_chunks = DocumentChunk.objects.filter(document__source__tenant=tenant).count()
        total_embeddings = EmbeddingMetadata.objects.filter(chunk__document__source__tenant=tenant).count()

        print(f"\n📈 FINAL DATABASE STATE:")
        print(f"📄 Total documents: {total_docs}")
        print(f"🧩 Total chunks: {total_chunks}")
        print(f"🔗 Total embeddings: {total_embeddings}")

        if total_docs > 0:
            print(f"📊 Average chunks per document: {total_chunks/total_docs:.1f}")
            print(f"📊 Average embeddings per chunk: {total_embeddings/total_chunks:.1f}" if total_chunks > 0 else "📊 No chunks created")

        print(f"\n🎯 PRODUCTION INGESTION SUCCESSFUL!")
        print("✅ System ready for comprehensive RAG search testing")

    except Exception as e:
        print(f"❌ Production ingestion failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    ingest_all_slack_data()
