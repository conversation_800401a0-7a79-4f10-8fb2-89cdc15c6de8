#!/usr/bin/env python3
"""
Clean Data and Fresh Slack Ingestion Script

This script performs a complete data cleanup and fresh ingestion of all Slack data
using the production ingestion service exactly as it operates in production environment.

Features:
- Complete data cleanup (database + vector store)
- Fresh ingestion with all recent enhancements
- Production-grade error handling and monitoring
- Comprehensive validation and metrics
"""

import os
import sys
import logging
import traceback
from pathlib import Path
from datetime import datetime
# No typing imports needed for this script

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Setup production logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(project_root / 'clean_and_fresh_ingest.log')
    ]
)
logger = logging.getLogger(__name__)

def setup_django():
    """Setup Django environment for production operations."""
    try:
        # Add the multi_source_rag directory to Python path
        multi_source_rag_path = project_root / "multi_source_rag"
        sys.path.insert(0, str(multi_source_rag_path))

        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
        import django
        django.setup()
        logger.info("✅ Django setup complete")
        return True
    except Exception as e:
        logger.error(f"❌ Django setup failed: {e}")
        logger.error(f"Error details: {traceback.format_exc()}")
        return False

def clean_all_data():
    """Clean all existing data from database and vector store."""
    logger.info("🧹 Starting Complete Data Cleanup...")

    try:
        from apps.documents.models import (
            RawDocument, DocumentChunk, DocumentContent,
            EmbeddingMetadata, DocumentDomainMetadata, DocumentComplexityProfile
        )
        from apps.accounts.models import Tenant

        # Get tenant
        tenant = Tenant.objects.get(slug='stride')

        # Clean database tables
        logger.info("🗑️  Cleaning database tables...")

        # Delete in correct order to respect foreign key constraints
        DocumentComplexityProfile.objects.filter(tenant=tenant).delete()
        DocumentDomainMetadata.objects.filter(tenant=tenant).delete()
        EmbeddingMetadata.objects.filter(chunk__document__source__tenant=tenant).delete()
        DocumentChunk.objects.filter(document__source__tenant=tenant).delete()
        DocumentContent.objects.filter(document__source__tenant=tenant).delete()
        RawDocument.objects.filter(source__tenant=tenant).delete()

        logger.info("✅ Database cleanup complete")

        # Clean vector store
        logger.info("🗑️  Cleaning vector store...")
        try:
            from apps.core.utils.collection_manager import get_collection_name
            import qdrant_client

            # Connect to Qdrant
            client = qdrant_client.QdrantClient(host="localhost", port=6333)

            # Get all possible collection names for this tenant
            collection_names = [
                get_collection_name(tenant.slug, intent="slack"),
                get_collection_name(tenant.slug, intent="conversation"),
                get_collection_name(tenant.slug, intent="code"),
                get_collection_name(tenant.slug, intent="document"),
                get_collection_name(tenant.slug, intent="default")
            ]

            # Delete collections
            for collection_name in collection_names:
                try:
                    if client.collection_exists(collection_name):
                        client.delete_collection(collection_name)
                        logger.info(f"✅ Deleted collection: {collection_name}")
                except Exception as e:
                    logger.warning(f"⚠️  Could not delete collection {collection_name}: {e}")

            logger.info("✅ Vector store cleanup complete")

        except Exception as e:
            logger.error(f"❌ Vector store cleanup failed: {e}")
            # Continue with ingestion even if vector cleanup fails

        return True

    except Exception as e:
        logger.error(f"❌ Data cleanup failed: {e}")
        logger.error(f"Error details: {traceback.format_exc()}")
        return False

def setup_production_environment():
    """Setup production tenant and document source."""
    try:
        from apps.accounts.models import Tenant
        from apps.documents.models import DocumentSource
        from django.contrib.auth.models import User

        # Get or create production user
        user, created = User.objects.get_or_create(
            username='<EMAIL>',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Mahesh',
                'last_name': 'Admin'
            }
        )
        if created:
            logger.info("✅ Created production user")

        # Get or create production tenant
        tenant, created = Tenant.objects.get_or_create(
            slug='stride',
            defaults={
                'name': 'Stride Technologies',
                'owner': user
            }
        )
        if created:
            logger.info("✅ Created production tenant")

        # Create production document source for Slack
        source, created = DocumentSource.objects.get_or_create(
            name='slack_production_engineering',
            source_type='local_slack',
            tenant=tenant,
            defaults={
                'config': {
                    "data_dir": "/Users/<USER>/Desktop/RAGSearch/data",
                    "channel_id": "C065QSSNH8A",
                    "channel_name": "1-productengineering",
                    "use_local_files": True,
                    "enable_enhanced_processing": True,
                    "enable_advanced_rag": True
                },
                'is_active': True
            }
        )
        if created:
            logger.info("✅ Created production Slack document source")

        return tenant, source

    except Exception as e:
        logger.error(f"❌ Failed to setup production environment: {e}")
        raise

def run_fresh_slack_ingestion():
    """Run fresh Slack ingestion with all recent enhancements."""
    logger.info("🚀 Starting Fresh Slack Data Ingestion with Recent Enhancements")
    logger.info("=" * 80)

    if not setup_django():
        logger.error("❌ Cannot proceed without Django setup")
        return False

    # Step 1: Clean all existing data
    if not clean_all_data():
        logger.error("❌ Data cleanup failed")
        return False

    # Step 2: Setup production environment
    try:
        tenant, source = setup_production_environment()
        logger.info(f"✅ Production environment ready")
        logger.info(f"   - Tenant: {tenant.name} (slug: {tenant.slug})")
        logger.info(f"   - Source: {source.name} (type: {source.source_type})")
    except Exception as e:
        logger.error(f"❌ Environment setup failed: {e}")
        return False

    # Step 3: Initialize production ingestion service
    try:
        from apps.documents.services.ingestion import IngestionService
        from apps.documents.interfaces.slack.local_slack import LocalSlackSourceInterface

        ingestion_service = IngestionService(tenant.slug)
        logger.info("✅ Production IngestionService initialized")
        logger.info(f"   - Available pipelines: {list(ingestion_service.pipelines.keys())}")
        logger.info(f"   - Advanced RAG services: {ingestion_service.domain_classifier is not None}")

    except Exception as e:
        logger.error(f"❌ Ingestion service initialization failed: {e}")
        return False

    # Step 4: Test Slack interface and fetch all documents
    try:
        logger.info("\n📋 Fetching All Slack Documents...")
        interface = LocalSlackSourceInterface(source.config)

        # Fetch ALL documents (no limit for fresh ingestion)
        documents = interface.fetch_documents()
        logger.info(f"✅ Fetched {len(documents)} total documents from Slack")

        if not documents:
            logger.error("❌ No documents found - check data directory and files")
            return False

        # Log sample document structure
        sample_doc = documents[0]
        logger.info(f"📄 Sample document structure:")
        logger.info(f"   - ID: {sample_doc.get('id', 'N/A')}")
        logger.info(f"   - Content length: {len(sample_doc.get('content', ''))}")
        logger.info(f"   - Metadata keys: {list(sample_doc.get('metadata', {}).keys())}")

    except Exception as e:
        logger.error(f"❌ Document fetching failed: {e}")
        return False

    # Step 5: Run production ingestion with all enhancements
    try:
        logger.info("\n🔄 Starting Fresh Production Ingestion...")
        logger.info(f"📊 Processing {len(documents)} documents with recent enhancements")
        start_time = datetime.now()

        processed_count, failed_count = ingestion_service.process_source(
            source=source,
            batch_size=20,  # Larger batches for efficiency
            # No limit - process ALL documents
        )

        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()

        logger.info("\n" + "=" * 80)
        logger.info("🎯 FRESH INGESTION RESULTS:")
        logger.info(f"   ✅ Documents processed: {processed_count}")
        logger.info(f"   ❌ Documents failed: {failed_count}")
        logger.info(f"   ⏱️  Total processing time: {duration:.2f} seconds")
        logger.info(f"   📊 Processing rate: {processed_count/duration:.2f} docs/sec")

        # Step 6: Comprehensive validation
        logger.info("\n📊 Comprehensive Data Validation...")
        from apps.documents.models import RawDocument, DocumentChunk, EmbeddingMetadata, DocumentDomainMetadata, DocumentComplexityProfile

        total_docs = RawDocument.objects.filter(source=source).count()
        total_chunks = DocumentChunk.objects.filter(document__source=source).count()
        total_embeddings = EmbeddingMetadata.objects.filter(chunk__document__source=source).count()
        total_domain_metadata = DocumentDomainMetadata.objects.filter(document__source=source).count()
        total_complexity_profiles = DocumentComplexityProfile.objects.filter(document__source=source).count()

        logger.info(f"   📄 Total documents in DB: {total_docs}")
        logger.info(f"   🧩 Total chunks in DB: {total_chunks}")
        logger.info(f"   🔗 Total embeddings: {total_embeddings}")
        logger.info(f"   🏷️  Total domain metadata: {total_domain_metadata}")
        logger.info(f"   📈 Total complexity profiles: {total_complexity_profiles}")

        if total_chunks > 0:
            avg_chunks_per_doc = total_chunks / total_docs if total_docs > 0 else 0
            logger.info(f"   📊 Average chunks per document: {avg_chunks_per_doc:.2f}")

        # Step 7: Vector store validation
        logger.info("\n🔍 Vector Store Validation...")
        from apps.core.utils.collection_manager import get_collection_name, is_collection_usable

        collection_name = get_collection_name(tenant.slug, intent="slack")
        is_usable = is_collection_usable(collection_name)

        logger.info(f"   📦 Collection name: {collection_name}")
        logger.info(f"   ✅ Collection usable: {is_usable}")

        # Success criteria
        success = (
            processed_count > 0 and
            total_docs > 0 and
            total_chunks > 0 and
            total_embeddings > 0 and
            is_usable
        )

        if success:
            logger.info("\n🎉 FRESH INGESTION COMPLETED SUCCESSFULLY!")
            logger.info("✅ All recent enhancements validated and working")
            logger.info("✅ System ready for comprehensive RAG testing")
            return True
        else:
            logger.error("\n❌ FRESH INGESTION VALIDATION FAILED!")
            logger.error("Some components did not process correctly")
            return False

    except Exception as e:
        logger.error(f"\n❌ Fresh ingestion failed: {e}")
        logger.error(f"Error details: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = run_fresh_slack_ingestion()

    if success:
        logger.info("\n🚀 Ready for comprehensive RAG testing with fresh data!")
        logger.info("All recent enhancements are validated and operational.")
    else:
        logger.error("\n💥 Fresh ingestion failed!")
        logger.error("Please check logs and fix issues before proceeding.")

    exit(0 if success else 1)
