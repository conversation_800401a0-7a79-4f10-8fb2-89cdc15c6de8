#!/usr/bin/env python3
"""
Simple test script for universal search functionality.
"""

import os
import sys
import django

# Add project root to Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.join(project_root, 'multi_source_rag'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from apps.search.services.rag_search_service import RAGSearchService

# Create service instance
service = RAGSearchService(tenant_slug='stride')

# Test universal search
results = service.search("What is the latest Curana customer feedback?")

# Print results
print(f"Answer: {results['answer']}")
print(f"Strategy: {results['metadata']['strategy_used']}")
print(f"Sources: {len(results['sources'])}")
