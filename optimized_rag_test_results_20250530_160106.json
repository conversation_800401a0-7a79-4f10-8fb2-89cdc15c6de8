{"timestamp": "2025-05-30T16:01:06.936448", "tenant": "stride", "summary": {"total_tests": 12, "passed_tests": 10, "success_rate": 83.33333333333334}, "detailed_results": [{"category": "Core Functionality", "name": "Basic Search", "query": "authentication", "passed": true, "response_time": 64.74148297309875, "result_count": 2, "details": "2 results found", "timestamp": "2025-05-30T15:45:58.496029"}, {"category": "Core Functionality", "name": "Technical Query", "query": "API endpoint configuration", "passed": true, "response_time": 76.83978080749512, "result_count": 2, "details": "2 results found", "timestamp": "2025-05-30T15:47:15.335850"}, {"category": "Core Functionality", "name": "Complex Query", "query": "How do we handle user authentication in our system?", "passed": true, "response_time": 103.3120949268341, "result_count": 2, "details": "2 results found", "timestamp": "2025-05-30T15:48:58.647986"}, {"category": "Core Functionality", "name": "Multiple Results", "query": "engineering discussions", "passed": true, "response_time": 57.00434994697571, "result_count": 2, "details": "2 results found", "timestamp": "2025-05-30T15:49:55.652375"}, {"category": "Result Quality", "name": "Content Quality", "query": "authentication implementation details", "passed": true, "response_time": 90.26161289215088, "result_count": 2, "details": "2 results found", "timestamp": "2025-05-30T15:51:25.914061"}, {"category": "Result Quality", "name": "Metadata Quality", "query": "API security best practices", "passed": true, "response_time": 72.19981026649475, "result_count": 2, "details": "2 results found", "timestamp": "2025-05-30T15:52:38.113931"}, {"category": "Result Quality", "name": "Relevance Quality", "query": "login issues", "passed": true, "response_time": 76.94464087486267, "result_count": 2, "details": "2 results found", "timestamp": "2025-05-30T15:53:55.058613"}, {"category": "Performance", "name": "Standard Performance", "query": "authentication system overview", "passed": false, "response_time": 82.77271485328674, "result_count": 2, "details": "Response time 82.77s exceeded 30.0s", "timestamp": "2025-05-30T15:55:17.831405"}, {"category": "Performance", "name": "Complex Query Performance", "query": "What are the architectural patterns for implementing scalable authentication?", "passed": false, "response_time": 102.83445596694946, "result_count": 2, "details": "Response time 102.83s exceeded 60.0s", "timestamp": "2025-05-30T15:57:00.665904"}, {"category": "Advanced Features", "name": "Intent Classification", "query": "How to debug connection issues?", "passed": true, "response_time": 107.93282890319824, "result_count": 2, "details": "2 results found", "timestamp": "2025-05-30T15:58:48.598804"}, {"category": "Advanced Features", "name": "Query Enhancement", "query": "auth", "passed": true, "response_time": 54.198275089263916, "result_count": 2, "details": "2 results found", "timestamp": "2025-05-30T15:59:42.797120"}, {"category": "Advanced Features", "name": "Hybrid Search", "query": "authentication best practices", "passed": true, "response_time": 84.1377420425415, "result_count": 2, "details": "2 results found", "timestamp": "2025-05-30T16:01:06.934898"}]}